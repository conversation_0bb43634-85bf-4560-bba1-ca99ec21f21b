package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.GetIMGrayStatusRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetIMGrayStatusResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/03/22
*/
@Mapper(componentModel = "spring")
public interface GetIMGrayStatusMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.GetIMGrayStatusRequestType from(GetIMGrayStatusRequestType request);

    GetIMGrayStatusResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.GetIMGrayStatusResponseType response);
}
