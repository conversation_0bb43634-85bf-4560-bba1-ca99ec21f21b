package com.ctrip.car.businessim.bff.service.core.mapper.activity;

import com.ctrip.car.businessim.bff.service.entity.QueryStoreListBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.QueryStoreListBffResponseType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStoreListRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStoreListResponseType;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface QueryStoreListMapper {

    QueryStoreListRequestType from(QueryStoreListBffRequestType request);

    QueryStoreListBffResponseType to(QueryStoreListResponseType response);
}
