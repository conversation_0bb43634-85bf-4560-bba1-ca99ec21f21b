package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.ListDropdownStoreRequestType;
import com.ctrip.car.businessim.bff.service.entity.ListDropdownStoreResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/03/22
*/
@Mapper(componentModel = "spring")
public interface ListDropdownStoreMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownStoreRequestType from(ListDropdownStoreRequestType request);

    ListDropdownStoreResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownStoreResponseType response);
}
