package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.GetBusinessNumberByCallIdRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetBusinessNumberByCallIdResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/03/22
*/
@Mapper(componentModel = "spring")
public interface GetBusinessNumberByCallIdMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.GetBusinessNumberByCallIdRequestType from(GetBusinessNumberByCallIdRequestType request);

    GetBusinessNumberByCallIdResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.GetBusinessNumberByCallIdResponseType response);
}
