package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.ListServiceDataDropdownStoreRequestType;
import com.ctrip.car.businessim.bff.service.entity.ListServiceDataDropdownStoreResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/03/22
*/
@Mapper(componentModel = "spring")
public interface ListServiceDataDropdownStoreMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataDropdownStoreRequestType from(ListServiceDataDropdownStoreRequestType request);

    ListServiceDataDropdownStoreResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataDropdownStoreResponseType response);
}
