package com.ctrip.car.businessim.bff.service.core.mapper.activity;

import com.ctrip.car.businessim.bff.service.entity.AddPromoterBffResponseType;
import com.ctrip.car.businessim.bff.service.entity.AddPromoterBffRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.AddPromoterRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.AddPromoterResponseType;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface AddPromoterMapper {

    AddPromoterRequestType from(AddPromoterBffRequestType request);

    AddPromoterBffResponseType to(AddPromoterResponseType response);
}
