package com.ctrip.car.businessim.bff.service.core.enums;

import com.ctrip.car.businessim.bff.service.core.util.QConfigUtil;

public enum SortTypeEnum {

    DEFAULT(Integer.MIN_VALUE, ""),
    //活动时间近->远
    ACTIVITY_TIME_ASC(0, QConfigUtil.getChineseOrDefault("SortType_ACTIVITY_TIME_ASC", "")),
    //报名时间近->远
    SIGN_UP_TIME_ASC(1, QConfigUtil.getChineseOrDefault("SortType_SIGN_UP_TIME_ASC", "")),
    //报名数多->少
    ACTIVITY_TIME_DESC(2, QConfigUtil.getChineseOrDefault("SortType_ACTIVITY_TIME_DESC", "")),
    ;

    private final int code;
    private final String desc;

    SortTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SortTypeEnum valueOfCode(int code) {
        for (SortTypeEnum obj : SortTypeEnum.values()) {
            if (java.util.Objects.equals(obj.code, code)) {
                return obj;
            }
        }
        return DEFAULT;
    }

    public static SortTypeEnum valueOfDesc(String desc) {
        for (SortTypeEnum obj : SortTypeEnum.values()) {
            if (java.util.Objects.equals(obj.desc, desc)) {
                return obj;
            }
        }
        return DEFAULT;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}