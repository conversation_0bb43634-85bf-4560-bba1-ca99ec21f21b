package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.ListAgentAllocateRuleRequestType;
import com.ctrip.car.businessim.bff.service.entity.ListAgentAllocateRuleResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/12/09
*/
@Mapper(componentModel = "spring")
public interface ListAgentAllocateRuleMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.ListAgentAllocateRuleRequestType from(ListAgentAllocateRuleRequestType request);

    ListAgentAllocateRuleResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.ListAgentAllocateRuleResponseType response);
}
