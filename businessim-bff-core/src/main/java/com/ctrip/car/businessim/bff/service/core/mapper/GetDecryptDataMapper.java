package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.GetDecryptDataRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetDecryptDataResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/03/22
*/
@Mapper(componentModel = "spring")
public interface GetDecryptDataMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.GetDecryptDataRequestType from(GetDecryptDataRequestType request);

    GetDecryptDataResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.GetDecryptDataResponseType response);
}
