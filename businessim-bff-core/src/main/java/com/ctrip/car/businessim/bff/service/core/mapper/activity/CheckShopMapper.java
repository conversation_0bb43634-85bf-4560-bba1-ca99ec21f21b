package com.ctrip.car.businessim.bff.service.core.mapper.activity;

import com.ctrip.car.businessim.bff.service.entity.CheckShopBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.CheckShopBffResponseType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.CheckShopRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.CheckShopResponseType;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface CheckShopMapper {

    CheckShopRequestType from(CheckShopBffRequestType request);

    CheckShopBffResponseType to(CheckShopResponseType response);
}
