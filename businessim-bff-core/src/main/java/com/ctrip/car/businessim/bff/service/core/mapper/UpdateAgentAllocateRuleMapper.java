package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.UpdateAgentAllocateRuleRequestType;
import com.ctrip.car.businessim.bff.service.entity.UpdateAgentAllocateRuleResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/12/09
*/
@Mapper(componentModel = "spring")
public interface UpdateAgentAllocateRuleMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.UpdateAgentAllocateRuleRequestType from(UpdateAgentAllocateRuleRequestType request);

    UpdateAgentAllocateRuleResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.UpdateAgentAllocateRuleResponseType response);
}
