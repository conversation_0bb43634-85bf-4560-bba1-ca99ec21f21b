package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.GetB2OLinkAndParameterRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetB2OLinkAndParameterResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/12/16
*/
@Mapper(componentModel = "spring")
public interface GetB2OLinkAndParameterMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.GetB2OLinkAndParameterRequestType from(GetB2OLinkAndParameterRequestType request);

    GetB2OLinkAndParameterResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.GetB2OLinkAndParameterResponseType response);
}
