package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.GetB2ONewLinkAndParameterRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetB2ONewLinkAndParameterResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/12/16
*/
@Mapper(componentModel = "spring")
public interface GetB2ONewLinkAndParameterMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.GetB2ONewLinkAndParameterRequestType from(GetB2ONewLinkAndParameterRequestType request);

    GetB2ONewLinkAndParameterResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.GetB2ONewLinkAndParameterResponseType response);
}
