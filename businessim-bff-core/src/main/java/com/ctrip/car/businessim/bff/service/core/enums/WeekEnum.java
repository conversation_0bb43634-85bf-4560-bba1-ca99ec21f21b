package com.ctrip.car.businessim.bff.service.core.enums;

import com.ctrip.car.businessim.bff.service.core.util.QConfigUtil;

public enum WeekEnum {

    DEFAULT(Integer.MIN_VALUE, ""),
    MONDAY(1, QConfigUtil.getChineseOrDefault("Week_MONDAY", "")),
    TUESDAY(2, QConfigUtil.getChineseOrDefault("Week_TUESDAY", "")),
    WEDNESDAY(3, QConfigUtil.getChineseOrDefault("Week_WEDNESDAY", "")),
    THURSDAY(4, QConfigUtil.getChineseOrDefault("Week_THURSDAY", "")),
    FRIDAY(5, QConfigUtil.getChineseOrDefault("Week_FRIDAY", "")),
    SATURDAY(6, QConfigUtil.getChineseOrDefault("Week_SATURDAY", "")),
    SUNDAY(7, QConfigUtil.getChineseOrDefault("Week_SUNDAY", "")),
    ;

    private final int code;
    private final String desc;

    WeekEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WeekEnum valueOfCode(int code) {
        for (WeekEnum obj : WeekEnum.values()) {
            if (java.util.Objects.equals(obj.code, code)) {
                return obj;
            }
        }
        return DEFAULT;
    }

    public static WeekEnum valueOfDesc(String desc) {
        for (WeekEnum obj : WeekEnum.values()) {
            if (java.util.Objects.equals(obj.desc, desc)) {
                return obj;
            }
        }
        return DEFAULT;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
