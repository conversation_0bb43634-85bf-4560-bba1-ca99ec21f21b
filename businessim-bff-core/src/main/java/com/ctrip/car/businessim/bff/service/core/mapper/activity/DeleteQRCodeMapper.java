package com.ctrip.car.businessim.bff.service.core.mapper.activity;

import com.ctrip.car.businessim.bff.service.entity.DeleteQRCodeBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.DeleteQRCodeBffResponseType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.DeleteQRCodeRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.DeleteQRCodeResponseType;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface DeleteQRCodeMapper {

    DeleteQRCodeRequestType from(DeleteQRCodeBffRequestType request);

    DeleteQRCodeBffResponseType to(DeleteQRCodeResponseType response);
}