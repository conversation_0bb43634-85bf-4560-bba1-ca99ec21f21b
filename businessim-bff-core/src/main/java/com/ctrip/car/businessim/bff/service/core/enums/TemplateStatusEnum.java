package com.ctrip.car.businessim.bff.service.core.enums;

import com.ctrip.car.businessim.bff.service.core.util.QConfigUtil;

public enum TemplateStatusEnum {

    DEFAULT(Integer.MIN_VALUE, ""),
    //已创建
    CREATED(0, QConfigUtil.getChineseOrDefault("TemplateStatus_CREATED", "")),
    //未生效
    INVALID(1, QConfigUtil.getChineseOrDefault("TemplateStatus_INVALID", "")),
    //报名中
    SIGNING_UP(2, QConfigUtil.getChineseOrDefault("TemplateStatus_SIGNING_UP", "")),
    //已作废
    OBSOLETE(3, QConfigUtil.getChineseOrDefault("TemplateStatus_OBSOLETE", "")),
    //已过期
    EXPIRED(4, QConfigUtil.getChineseOrDefault("TemplateStatus_EXPIRED", "")),
    //已禁用
    FORBIDDEN(5, QConfigUtil.getChineseOrDefault("TemplateStatus_FORBIDDEN", "")),
    ;

    private final int code;
    private final String desc;

    TemplateStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TemplateStatusEnum valueOfCode(int code) {
        for (TemplateStatusEnum obj : TemplateStatusEnum.values()) {
            if (java.util.Objects.equals(obj.code, code)) {
                return obj;
            }
        }
        return DEFAULT;
    }

    public static TemplateStatusEnum valueOfDesc(String desc) {
        for (TemplateStatusEnum obj : TemplateStatusEnum.values()) {
            if (java.util.Objects.equals(obj.desc, desc)) {
                return obj;
            }
        }
        return DEFAULT;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
