package com.ctrip.car.businessim.bff.service.core.mapper.seo;

import com.ctrip.car.businessim.bff.service.dto.seo.QuerySeoPageConfigBffRequestType;
import com.ctrip.car.businessim.bff.service.dto.seo.QuerySeoPageConfigBffResponseType;
import com.ctrip.car.customer.businessim.contract.servicetype.seo.QuerySeoPageConfigRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.seo.QuerySeoPageConfigResponseType;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface QuerySeoPageConfigMapper {

    QuerySeoPageConfigRequestType from(QuerySeoPageConfigBffRequestType request);

    QuerySeoPageConfigBffResponseType to(QuerySeoPageConfigResponseType response);
}
