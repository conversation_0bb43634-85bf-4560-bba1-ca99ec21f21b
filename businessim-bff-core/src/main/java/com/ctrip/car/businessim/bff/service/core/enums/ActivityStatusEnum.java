package com.ctrip.car.businessim.bff.service.core.enums;

import com.ctrip.car.businessim.bff.service.core.util.QConfigUtil;

public enum ActivityStatusEnum {

    DEFAULT(Integer.MIN_VALUE, ""),
    //待审核
    VERIFYING(0, QConfigUtil.getChineseOrDefault("ActivityStatus_VERIFYING", "")),
    //未开始
    WAIT_TO_START(1, QConfigUtil.getChineseOrDefault("ActivityStatus_WAIT_TO_START", "")),
    //活动中
    DOING_ACTIVITY(2, QConfigUtil.getChineseOrDefault("ActivityStatus_DOING_ACTIVITY", "")),
    //已作废
    INVALID(3, QConfigUtil.getChineseOrDefault("ActivityStatus_INVALID", "")),
    //已过期
    EXPIRED(4, QConfigUtil.getChineseOrDefault("ActivityStatus_EXPIRED", "")),
    //已退出
    QUITED(5, QConfigUtil.getChineseOrDefault("ActivityStatus_QUITED", "")),
    ;

    private final int code;
    private final String desc;

    ActivityStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ActivityStatusEnum valueOfCode(int code) {
        for (ActivityStatusEnum obj : ActivityStatusEnum.values()) {
            if (java.util.Objects.equals(obj.code, code)) {
                return obj;
            }
        }
        return DEFAULT;
    }

    public static ActivityStatusEnum valueOfDesc(String desc) {
        for (ActivityStatusEnum obj : ActivityStatusEnum.values()) {
            if (java.util.Objects.equals(obj.desc, desc)) {
                return obj;
            }
        }
        return DEFAULT;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
