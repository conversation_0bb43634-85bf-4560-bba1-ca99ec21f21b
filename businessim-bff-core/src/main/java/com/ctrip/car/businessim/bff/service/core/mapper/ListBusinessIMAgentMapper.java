package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.ListBusinessIMAgentRequestType;
import com.ctrip.car.businessim.bff.service.entity.ListBusinessIMAgentResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/03/22
*/
@Mapper(componentModel = "spring")
public interface ListBusinessIMAgentMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.ListBusinessIMAgentRequestType from(ListBusinessIMAgentRequestType request);

    ListBusinessIMAgentResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.ListBusinessIMAgentResponseType response);
}
