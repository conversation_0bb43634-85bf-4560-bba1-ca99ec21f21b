package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.DownloadIVRCallRequestType;
import com.ctrip.car.businessim.bff.service.entity.DownloadIVRCallResponseType;
import com.ctrip.car.businessim.bff.service.entity.ListServiceDataIMAgentRequestType;
import com.ctrip.car.businessim.bff.service.entity.ListServiceDataIMAgentResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/03/22
*/
@Mapper(componentModel = "spring")
public interface ListServiceDataIMAgentMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataIMAgentRequestType from(ListServiceDataIMAgentRequestType request);

    ListServiceDataIMAgentResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataIMAgentResponseType response);
}
