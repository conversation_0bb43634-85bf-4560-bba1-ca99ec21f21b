package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.DownloadIVRCallRequestType;
import com.ctrip.car.businessim.bff.service.entity.DownloadIVRCallResponseType;
import com.ctrip.car.businessim.bff.service.entity.GetOrderDetailLinkRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetOrderDetailLinkResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/03/22
*/
@Mapper(componentModel = "spring")
public interface GetOrderDetailLinkMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.GetOrderDetailLinkRequestType from(GetOrderDetailLinkRequestType request);

    GetOrderDetailLinkResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.GetOrderDetailLinkResponseType response);
}
