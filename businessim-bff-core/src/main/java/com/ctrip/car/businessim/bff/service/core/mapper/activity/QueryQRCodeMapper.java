package com.ctrip.car.businessim.bff.service.core.mapper.activity;

import com.ctrip.car.businessim.bff.service.entity.QueryQRCodeBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.QueryQRCodeBffResponseType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryQRCodeRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryQRCodeResponseType;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface QueryQRCodeMapper {

    QueryQRCodeRequestType from(QueryQRCodeBffRequestType request);

    QueryQRCodeBffResponseType to(QueryQRCodeResponseType response);
}