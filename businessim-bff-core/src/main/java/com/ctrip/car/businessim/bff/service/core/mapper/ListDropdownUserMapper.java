package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.ListDropdownUserRequestType;
import com.ctrip.car.businessim.bff.service.entity.ListDropdownUserResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/03/22
*/
@Mapper(componentModel = "spring")
public interface ListDropdownUserMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownUserRequestType from(ListDropdownUserRequestType request);

    ListDropdownUserResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownUserResponseType response);
}
