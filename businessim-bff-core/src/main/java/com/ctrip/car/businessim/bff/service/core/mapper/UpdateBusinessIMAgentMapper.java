package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.UpdateBusinessIMAgentRequestType;
import com.ctrip.car.businessim.bff.service.entity.UpdateBusinessIMAgentResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/03/22
*/
@Mapper(componentModel = "spring")
public interface UpdateBusinessIMAgentMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.UpdateBusinessIMAgentRequestType from(UpdateBusinessIMAgentRequestType request);

    UpdateBusinessIMAgentResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.UpdateBusinessIMAgentResponseType response);
}
