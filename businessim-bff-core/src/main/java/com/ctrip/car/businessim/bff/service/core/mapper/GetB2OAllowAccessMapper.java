package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.GetB2OAllowAccessRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetB2OAllowAccessResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2023/3/6
*/
@Mapper(componentModel = "spring")
public interface GetB2OAllowAccessMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.GetB2OAllowAccessRequestType from(GetB2OAllowAccessRequestType request);

    GetB2OAllowAccessResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.GetB2OAllowAccessResponseType response);
}
