package com.ctrip.car.businessim.bff.service.core.mapper.activity;

import com.ctrip.car.businessim.bff.service.entity.QueryStoreQRCodeListBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.QueryStoreQRCodeListBffResponseType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStoreQRCodeListRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStoreQRCodeListResponseType;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface QueryStoreQRCodeListMapper {

    QueryStoreQRCodeListRequestType from(QueryStoreQRCodeListBffRequestType request);

    QueryStoreQRCodeListBffResponseType to(QueryStoreQRCodeListResponseType response);
}

