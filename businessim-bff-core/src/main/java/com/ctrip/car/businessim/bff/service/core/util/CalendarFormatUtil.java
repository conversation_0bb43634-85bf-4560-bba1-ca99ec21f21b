package com.ctrip.car.businessim.bff.service.core.util;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class CalendarFormatUtil {

    private static final ThreadLocal<SimpleDateFormat> yyyy_MM_dd_HH_mm_ss = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    private static final ThreadLocal<SimpleDateFormat> yyyyMMddHHmmss = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMMddHHmmss"));

    public static String format_yyyy_MM_dd_HH_mm_ss(Calendar calendar) {
        return format(calendar, yyyy_MM_dd_HH_mm_ss);
    }

    public static String format_yyyy_MM_dd_HH_mm_ss(Date dt) {
        return format(dt, yyyy_MM_dd_HH_mm_ss);
    }

    public static String format_yyyyMMddHHmmss(Calendar calendar) {
        return format(calendar, yyyyMMddHHmmss);
    }

    private static String format(Calendar calendar, ThreadLocal<SimpleDateFormat> formatter) {
        if (calendar == null) return "";
        try {
            return formatter.get().format(calendar.getTime());
        } finally {
            formatter.remove();
        }
    }

    private static String format(Date dt, ThreadLocal<SimpleDateFormat> formatter) {
        if (dt == null) {
            return "";
        }
        try {
            return formatter.get().format(dt);
        } finally {
            formatter.remove();
        }
    }
}
