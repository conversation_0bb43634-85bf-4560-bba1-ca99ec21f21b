package com.ctrip.car.businessim.bff.service.core.enums;

import com.ctrip.car.businessim.bff.service.core.util.QConfigUtil;

public enum FeeTypeEnum {

    DEFAULT(null, ""),
    //订单总价
    ORDER_AMOUNT("M", QConfigUtil.getChineseOrDefault("FeeType_ORDER_AMOUNT", "")),
    //租车费
    RENT_AMOUNT("A",QConfigUtil.getChineseOrDefault("FeeType_RENT_AMOUNT", "")),
    //基本保险费
    INSURANCE_AMOUNT("B",QConfigUtil.getChineseOrDefault("FeeType_INSURANCE_AMOUNT", "")),
    //手续费
    SERVICES_AMOUNT("C",QConfigUtil.getChineseOrDefault("FeeType_SERVICES_AMOUNT", "")),
    //异门店还车费
    DIFF_STORE_AMOUNT("D",QConfigUtil.getChineseOrDefault("FeeType_DIFF_STORE_AMOUNT", "")),
    //其他费用
    OTHER_AMOUNT("E",QConfigUtil.getChineseOrDefault("FeeType_OTHER_AMOUNT", "")),
    //活动优惠
    REDUCE_AMOUNT("F",QConfigUtil.getChineseOrDefault("FeeType_REDUCE_AMOUNT", "")),
    //首租金额
    FIRST_RENT_AMOUNT("FR",QConfigUtil.getChineseOrDefault("FeeType_FIRST_RENT_AMOUNT", "")),
    //平均日租金
    DAILY_PRICE_AMOUNT("D1",QConfigUtil.getChineseOrDefault("FeeType_DAILY_PRICE_AMOUNT", "")),
    //携程自营保险
    CTRIP_INSURANCE_AMOUNT("S",QConfigUtil.getChineseOrDefault("FeeType_CTRIP_INSURANCE_AMOUNT", "")),
    //零散小时费
    HOURS_AMOUNT("H",QConfigUtil.getChineseOrDefault("FeeType_HOURS_AMOUNT", ""));

    private final String code;
    private final String desc;

    FeeTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static FeeTypeEnum valueOfCode(String code) {
        for (FeeTypeEnum obj : FeeTypeEnum.values()) {
            if (java.util.Objects.equals(obj.code, code)) {
                return obj;
            }
        }
        return DEFAULT;
    }

    public static FeeTypeEnum valueOfDesc(String desc) {
        for (FeeTypeEnum obj : FeeTypeEnum.values()) {
            if (java.util.Objects.equals(obj.desc, desc)) {
                return obj;
            }
        }
        return DEFAULT;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
