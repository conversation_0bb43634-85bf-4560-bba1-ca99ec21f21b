package com.ctrip.car.businessim.bff.service.core.mapper.seo;

import com.ctrip.car.businessim.bff.service.dto.seo.UpdateSeoPageConfigStatusBffRequestType;
import com.ctrip.car.businessim.bff.service.dto.seo.UpdateSeoPageConfigStatusBffResponseType;
import com.ctrip.car.customer.businessim.contract.servicetype.seo.UpdateSeoPageConfigStatusRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.seo.UpdateSeoPageConfigStatusResponseType;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface UpdateSeoPageConfigUpdateMapper {

    UpdateSeoPageConfigStatusRequestType from(UpdateSeoPageConfigStatusBffRequestType request);

    UpdateSeoPageConfigStatusBffResponseType to(UpdateSeoPageConfigStatusResponseType response);
}
