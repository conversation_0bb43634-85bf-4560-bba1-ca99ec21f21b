package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.GetB2CNewLinkRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetB2CNewLinkResponseType;
import org.mapstruct.Mapper;



@Mapper(componentModel = "spring")
public interface GetB2CNewLinkMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.GetB2CNewLinkRequestType from(GetB2CNewLinkRequestType request);

    GetB2CNewLinkResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.GetB2CNewLinkResponseType response);
}
