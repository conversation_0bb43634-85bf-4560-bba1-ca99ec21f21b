package com.ctrip.car.businessim.bff.service.core.mapper.activity;

import com.ctrip.car.businessim.bff.service.entity.QueryPromoterListBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.QueryPromoterListBffResponseType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryPromoterListRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryPromoterListResponseType;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface QueryPromoterListMapper {

    QueryPromoterListRequestType from(QueryPromoterListBffRequestType request);

    QueryPromoterListBffResponseType to(QueryPromoterListResponseType response);
}
