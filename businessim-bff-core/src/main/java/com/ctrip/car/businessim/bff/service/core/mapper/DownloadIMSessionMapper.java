package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.DownloadIMSessionRequestType;
import com.ctrip.car.businessim.bff.service.entity.DownloadIMSessionResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/03/22
*/
@Mapper(componentModel = "spring")
public interface DownloadIMSessionMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.DownloadIMSessionRequestType from(DownloadIMSessionRequestType request);

    DownloadIMSessionResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.DownloadIMSessionResponseType response);
}
