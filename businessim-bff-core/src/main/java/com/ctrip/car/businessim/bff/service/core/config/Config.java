package com.ctrip.car.businessim.bff.service.core.config;

import com.ctrip.car.businessim.bff.service.core.dto.HolidayDto;
import com.ctrip.car.commodity.framework.core.utils.JsonUtil;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.List;
import java.util.Map;

@Component
public class Config {

    @QMapConfig("100041788#config.properties")
    public void onChange(Map<String, String> map) {
        this.holidayList = JsonUtil.jsonToList(map.getOrDefault("holiday", "[]"), HolidayDto.class);
    }

    @QMapConfig("shopConfig.properties")
    public void shopOnChange(Map<String,String> map) {
        this.shareContent = map.get("shareContent");
        this.supportSignupAdjustActivity = Boolean.parseBoolean(map.getOrDefault("supportSignupAdjustActivity", "false"));
    }

    private List<HolidayDto> holidayList;

    private String shareContent;

    private boolean supportSignupAdjustActivity;

    public List<HolidayDto> getHolidayList() {
        return holidayList;
    }

    public String getShareContent() {
        return shareContent;
    }

    public boolean isSupportSignupAdjustActivity() {
        return supportSignupAdjustActivity;
    }
}
