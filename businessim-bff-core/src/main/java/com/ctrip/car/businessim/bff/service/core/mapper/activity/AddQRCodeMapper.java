package com.ctrip.car.businessim.bff.service.core.mapper.activity;

import com.ctrip.car.businessim.bff.service.entity.AddQRCodeBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.AddQRCodeBffResponseType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.AddQRCodeRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.AddQRCodeResponseType;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface AddQRCodeMapper {

    AddQRCodeRequestType from(AddQRCodeBffRequestType request);

    AddQRCodeBffResponseType to(AddQRCodeResponseType response);
}