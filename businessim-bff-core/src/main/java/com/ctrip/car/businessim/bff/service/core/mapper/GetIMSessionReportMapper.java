package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.GetIMSessionReportRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetIMSessionReportResponseType;
import com.ctrip.car.businessim.bff.service.entity.GetStoreBusinessTimeRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetStoreBusinessTimeResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/03/22
*/
@Mapper(componentModel = "spring")
public interface GetIMSessionReportMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.GetIMSessionReportRequestType from(GetIMSessionReportRequestType request);

    GetIMSessionReportResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.GetIMSessionReportResponseType response);
}
