package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.DownloadIVRCallRequestType;
import com.ctrip.car.businessim.bff.service.entity.DownloadIVRCallResponseType;
import com.ctrip.car.businessim.bff.service.entity.GetIVRCallReportRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetIVRCallReportResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/03/22
*/
@Mapper(componentModel = "spring")
public interface GetIVRCallReportMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.GetIVRCallReportRequestType from(GetIVRCallReportRequestType request);

    GetIVRCallReportResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.GetIVRCallReportResponseType response);
}
