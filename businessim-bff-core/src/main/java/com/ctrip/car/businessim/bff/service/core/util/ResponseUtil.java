package com.ctrip.car.businessim.bff.service.core.util;

import com.ctrip.car.businessim.bff.service.core.enums.ResponseCodeEnum;
import com.ctrip.car.top.BaseResponse;
import org.apache.commons.lang3.StringUtils;

public class ResponseUtil {

    public static BaseResponse success() {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setCode(ResponseCodeEnum.SUCCESS.getCode());
        baseResponse.setMessage(ResponseCodeEnum.SUCCESS.getDefaultMsg());
        return baseResponse;
    }

    public static BaseResponse parameterError(String msg) {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setCode(ResponseCodeEnum.PARAMETER_ERROR.getCode());
        if (StringUtils.isNotBlank(msg)) {
            baseResponse.setMessage(msg);
        } else {
            baseResponse.setMessage(ResponseCodeEnum.PARAMETER_ERROR.getDefaultMsg());
        }
        return baseResponse;
    }

    public static BaseResponse thirdServiceError(String msg) {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setCode(ResponseCodeEnum.THIRD_SERVICE_ERROR.getCode());
        if (StringUtils.isNotBlank(msg)) {
            baseResponse.setMessage(msg);
        } else {
            baseResponse.setMessage(ResponseCodeEnum.THIRD_SERVICE_ERROR.getDefaultMsg());
        }
        return baseResponse;
    }
}
