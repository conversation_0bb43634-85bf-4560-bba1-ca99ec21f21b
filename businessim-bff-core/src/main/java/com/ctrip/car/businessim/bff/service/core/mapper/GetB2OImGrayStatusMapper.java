package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.GetB2OImGrayStatusRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetB2OImGrayStatusResponseType;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @Description:
 * @create 2024/11/12 15:19
 */
@Mapper(componentModel = "spring")
public interface GetB2OImGrayStatusMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.GetB2OImGrayStatusRequestType from(GetB2OImGrayStatusRequestType request);

    GetB2OImGrayStatusResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.GetB2OImGrayStatusResponseType response);
}
