package com.ctrip.car.businessim.bff.service.core.enums;

public enum AttendStatusEnum {

    DEFAULT(Integer.MIN_VALUE, ""),
    NOT_ATTEND(0, "not attended"),
    ATTENDED(1, "attended"),
    ;

    private final int code;
    private final String desc;

    AttendStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AttendStatusEnum valueOfCode(int code) {
        for (AttendStatusEnum obj : AttendStatusEnum.values()) {
            if (java.util.Objects.equals(obj.code, code)) {
                return obj;
            }
        }
        return DEFAULT;
    }

    public static AttendStatusEnum valueOfDesc(String desc) {
        for (AttendStatusEnum obj : AttendStatusEnum.values()) {
            if (java.util.Objects.equals(obj.desc, desc)) {
                return obj;
            }
        }
        return DEFAULT;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
