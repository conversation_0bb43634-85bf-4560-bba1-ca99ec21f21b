package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.GetConsultRecordsLinkRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetConsultRecordsLinkResponseType;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @Description:
 * @create 2024/11/12 15:18
 */
@Mapper(componentModel = "spring")
public interface GetConsultRecordsLinkMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.GetConsultRecordsLinkRequestType from(GetConsultRecordsLinkRequestType request);

    GetConsultRecordsLinkResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.GetConsultRecordsLinkResponseType response);
}
