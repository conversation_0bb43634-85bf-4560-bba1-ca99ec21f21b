package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.AddIMAgentRequestType;
import com.ctrip.car.businessim.bff.service.entity.AddIMAgentResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/03/22
*/
@Mapper(componentModel = "spring")
public interface AddIMAgentMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.AddIMAgentRequestType from(AddIMAgentRequestType request);

    AddIMAgentResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.AddIMAgentResponseType response);
}
