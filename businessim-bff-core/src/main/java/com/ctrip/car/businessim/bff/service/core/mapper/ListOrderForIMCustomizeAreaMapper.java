package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.ListOrderForIMCustomizeAreaRequestType;
import com.ctrip.car.businessim.bff.service.entity.ListOrderForIMCustomizeAreaResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/03/22
*/
@Mapper(componentModel = "spring")
public interface ListOrderForIMCustomizeAreaMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.ListOrderForIMCustomizeAreaRequestType from(ListOrderForIMCustomizeAreaRequestType request);

    ListOrderForIMCustomizeAreaResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.ListOrderForIMCustomizeAreaResponseType response);
}
