package com.ctrip.car.businessim.bff.service.core.mapper.activity;

import com.ctrip.car.businessim.bff.service.entity.ShopOpenBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.ShopOpenBffResponseType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.ShopOpenRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.ShopOpenResponseType;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface ShopOpenMapper {

    ShopOpenRequestType from(ShopOpenBffRequestType request);

    ShopOpenBffResponseType to(ShopOpenResponseType response);
}
