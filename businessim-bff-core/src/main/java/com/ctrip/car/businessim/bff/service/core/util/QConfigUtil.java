package com.ctrip.car.businessim.bff.service.core.util;

import org.apache.commons.collections4.MapUtils;
import qunar.tc.qconfig.client.MapConfig;

import java.util.Map;

public class QConfigUtil {

    public static String getChineseOrDefault(String key, String defaultValue) {
        MapConfig mapConfig = MapConfig.get("chinese.properties");
        if (mapConfig != null) {
            Map<String, String> chineseMap = mapConfig.asMap();
            if (MapUtils.isNotEmpty(chineseMap)) {
                return chineseMap.getOrDefault(key, defaultValue);
            }
        }
        return defaultValue;
    }

    public static String getSignOrDefault(String key, String defaultValue) {
        MapConfig mapConfig = MapConfig.get("sign.properties");
        if (mapConfig != null) {
            Map<String, String> chineseMap = mapConfig.asMap();
            if (MapUtils.isNotEmpty(chineseMap)) {
                return chineseMap.getOrDefault(key, defaultValue);
            }
        }
        return defaultValue;
    }
}
