package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.GetStoreBusinessTimeRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetStoreBusinessTimeResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/03/22
*/
@Mapper(componentModel = "spring")
public interface GetStoreBusinessTimeMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.GetStoreBusinessTimeRequestType from(GetStoreBusinessTimeRequestType request);

    GetStoreBusinessTimeResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.GetStoreBusinessTimeResponseType response);
}
