package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.UpdateStoreBusinessTimeRequestType;
import com.ctrip.car.businessim.bff.service.entity.UpdateStoreBusinessTimeResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/03/22
*/
@Mapper(componentModel = "spring")
public interface UpdateStoreBusinessTimeMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.UpdateStoreBusinessTimeRequestType from(UpdateStoreBusinessTimeRequestType request);

    UpdateStoreBusinessTimeResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.UpdateStoreBusinessTimeResponseType response);
}
