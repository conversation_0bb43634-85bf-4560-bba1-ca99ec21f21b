package com.ctrip.car.businessim.bff.service.core.enums;

import com.ctrip.car.businessim.bff.service.core.util.QConfigUtil;

public enum SignUpStatusEnum {

    DEFAULT(Integer.MIN_VALUE, ""),
    //报名中
    SIGNING_UP(1, QConfigUtil.getChineseOrDefault("SignUpStatus_SIGNING_UP", "")),
    //已参加
    ATTENDED(2, QConfigUtil.getChineseOrDefault("SignUpStatus_ATTENDED", "")),
    //已作废
    INVALID(3, QConfigUtil.getChineseOrDefault("SignUpStatus_INVALID", "")),
    //已结束
    FINISHED(4, QConfigUtil.getChineseOrDefault("SignUpStatus_FINISHED", "")),
    ;

    private final int code;
    private final String desc;

    SignUpStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SignUpStatusEnum valueOfCode(int code) {
        for (SignUpStatusEnum obj : SignUpStatusEnum.values()) {
            if (java.util.Objects.equals(obj.code, code)) {
                return obj;
            }
        }
        return DEFAULT;
    }

    public static SignUpStatusEnum valueOfDesc(String desc) {
        for (SignUpStatusEnum obj : SignUpStatusEnum.values()) {
            if (java.util.Objects.equals(obj.desc, desc)) {
                return obj;
            }
        }
        return DEFAULT;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
