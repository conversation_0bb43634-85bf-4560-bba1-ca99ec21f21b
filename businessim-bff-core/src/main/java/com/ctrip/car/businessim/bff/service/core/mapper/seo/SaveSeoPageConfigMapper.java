package com.ctrip.car.businessim.bff.service.core.mapper.seo;

import com.ctrip.car.businessim.bff.service.dto.seo.SaveSeoPageConfigBffRequestType;
import com.ctrip.car.businessim.bff.service.dto.seo.SaveSeoPageConfigBffResponseType;
import com.ctrip.car.customer.businessim.contract.servicetype.seo.SaveSeoPageConfigRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.seo.SaveSeoPageConfigResponseType;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface SaveSeoPageConfigMapper {

    SaveSeoPageConfigRequestType from(SaveSeoPageConfigBffRequestType request);

    SaveSeoPageConfigBffResponseType to(SaveSeoPageConfigResponseType response);
}
