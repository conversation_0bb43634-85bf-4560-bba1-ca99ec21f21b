package com.ctrip.car.businessim.bff.service.core.mapper.activity;

import com.ctrip.car.businessim.bff.service.entity.QueryQRCodeDetailBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.QueryQRCodeDetailBffResponseType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryQRCodeDetailRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryQRCodeDetailResponseType;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface QueryQRCodeDetailMapper {

    QueryQRCodeDetailRequestType from(QueryQRCodeDetailBffRequestType request);

    QueryQRCodeDetailBffResponseType to(QueryQRCodeDetailResponseType response);
}
