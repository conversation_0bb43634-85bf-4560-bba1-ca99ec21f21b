package com.ctrip.car.businessim.bff.service.core.enums;

import lombok.Getter;

@Getter
public enum ResponseCodeEnum {

    SUCCESS("000000", "success"),
    PARAMETER_ERROR("40001", "an error in parameter"),
    SYSTEM_ERROR("50000", "an error in system"),
    THIRD_SERVICE_ERROR("60000", "an error when invoking third-service"),
    ;

    private final String code;
    private final String defaultMsg;

    ResponseCodeEnum(String code, String defaultMsg) {
        this.code = code;
        this.defaultMsg = defaultMsg;
    }

    public String getCode() {
        return code;
    }

    public String getDefaultMsg() {
        return defaultMsg;
    }
}