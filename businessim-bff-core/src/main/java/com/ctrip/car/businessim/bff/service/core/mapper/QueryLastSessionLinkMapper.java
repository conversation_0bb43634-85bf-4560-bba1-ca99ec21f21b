package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.QueryLastSessionLinkRequestType;
import com.ctrip.car.businessim.bff.service.entity.QueryLastSessionLinkResponseType;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @Description:
 * @create 2024/11/12 15:20
 */
@Mapper(componentModel = "spring")
public interface QueryLastSessionLinkMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.QueryLastSessionLinkRequestType from(QueryLastSessionLinkRequestType request);

    QueryLastSessionLinkResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.QueryLastSessionLinkResponseType response);
}
