package com.ctrip.car.businessim.bff.service.core.mapper;

import com.ctrip.car.businessim.bff.service.entity.DownloadIMSessionRequestType;
import com.ctrip.car.businessim.bff.service.entity.DownloadIMSessionResponseType;
import com.ctrip.car.businessim.bff.service.entity.DownloadIVRCallRequestType;
import com.ctrip.car.businessim.bff.service.entity.DownloadIVRCallResponseType;
import org.mapstruct.Mapper;

/**
* <AUTHOR>
* @date 2022/03/22
*/
@Mapper(componentModel = "spring")
public interface DownloadIVRCallMapper {
    com.ctrip.car.customer.businessim.contract.servicetype.DownloadIVRCallRequestType from(DownloadIVRCallRequestType request);

    DownloadIVRCallResponseType to(com.ctrip.car.customer.businessim.contract.servicetype.DownloadIVRCallResponseType response);
}
