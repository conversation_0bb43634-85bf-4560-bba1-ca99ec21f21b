<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ctrip.car.customer</groupId>
        <artifactId>businessim-bff</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>businessim-bff-core</artifactId>
    <version>1.0.0</version>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.car.commodity.framework</groupId>
            <artifactId>soa-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car.commodity.framework</groupId>
            <artifactId>soa-server</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework</groupId>
            <artifactId>vi-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.26009</groupId>
            <artifactId>businessim-bff</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.basebiz</groupId>
            <artifactId>InternationalSmsHelperServiceClient</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>tomcat-servlet-api</artifactId>
                    <groupId>org.apache.tomcat</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.commodity</groupId>
            <artifactId>storeservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car.commodity</groupId>
            <artifactId>logservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car.commodity</groupId>
            <artifactId>basicservice</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.car.sd.vendorbasicdataservice.v1</groupId>
            <artifactId>vendorbasicdataservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.22745</groupId>
            <artifactId>carcoremetadataserivce</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.22697</groupId>
            <artifactId>carcommodityvendor</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>annotations</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car</groupId>
            <artifactId>commodity-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.ibu.platform</groupId>
                    <artifactId>ibu-shark-sdk</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>ctrip-dal-client</artifactId>
                    <groupId>com.ctrip.platform</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--第三方依赖组件 start -->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.20392</groupId>
            <artifactId>carinfoservice</artifactId>
        </dependency>

        <!--业务依赖-->
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.25434</groupId>
            <artifactId>businessim</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car.pms</groupId>
            <artifactId>car-business-auth</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car</groupId>
            <artifactId>coupon-common</artifactId>
            <version>3.2.35</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.flight.intl.common</groupId>
                    <artifactId>metric-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>