<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <modules>
        <module>businessim-bff-api</module>
        <module>businessim-bff-core</module>
        <module>businessim-bff-proxy</module>
        <module>businessim-bff-svc</module>
    </modules>

    <parent>
        <groupId>com.ctrip</groupId>
        <artifactId>super-pom</artifactId>
        <version>1.0.7</version>
    </parent>

    <groupId>com.ctrip.car.customer</groupId>
    <artifactId>businessim-bff</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0</version>

    <properties>
        <java.version>21</java.version>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>

        <!-- 内部服务版本 -->
        <commodity-framework-bom.version>0.0.64-jdk21</commodity-framework-bom.version>
        <top.promotion.version>0.0.5</top.promotion.version>
        <releases.repo>http://maven.release.ctripcorp.com/nexus/content/repositories/carrelease/</releases.repo>
        <snapshots.repo>http://maven.release.ctripcorp.com/nexus/content/repositories/carsnapshot/</snapshots.repo>

        <!-- 第三方依赖 -->
        <aspectjweaver.version>1.8.9</aspectjweaver.version>
        <slf4j.version>1.7.23</slf4j.version>
        <logback.version>1.1.7</logback.version>
        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
        <jackson.version>2.10.3</jackson.version>
        <lombok.version>1.18.32</lombok.version>
        <jmockit.version>1.52.0-jdk21</jmockit.version>
        <junit.version>4.12</junit.version>
        <velocity.version>1.7</velocity.version>
        <!-- 内部组件依赖 -->
        <top.basecache.version>1.1.79</top.basecache.version>
        <internationalSmsHelperService.version>1.0.6</internationalSmsHelperService.version>
        <baseservice-version>1.0.1</baseservice-version>
        <storeservice.version>1.0.44</storeservice.version>
        <carcoremetadataserivce-version>0.0.293</carcoremetadataserivce-version>
        <carcommoditycommon-version>0.0.86</carcommoditycommon-version>

        <vendorbasicdataservice-version>0.1.31</vendorbasicdataservice-version>
        <market.commom.version>	2.0.21</market.commom.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <jacoco.version>0.8.11</jacoco.version>
        <sonar.jacoco.reportPaths>${project.basedir}/code-coverage/jacoco.exec</sonar.jacoco.reportPaths>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-prometheus-exporter</artifactId>
            <version>3.5.16</version>
        </dependency>
        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>org.jacoco.agent</artifactId>
            <classifier>runtime</classifier>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!-- bom 依赖 -->
            <dependency>
                <groupId>com.ctrip.framework</groupId>
                <artifactId>framework-bom</artifactId>
                <version>8.1.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- 商家端框架依赖 -->
            <dependency>
                <groupId>com.ctrip.car.commodity.framework</groupId>
                <artifactId>commodity-framework-bom</artifactId>
                <version>${commodity-framework-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.ctrip.car.commodity</groupId>
                <artifactId>logservice</artifactId>
                <version>1.0.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.ctrip.car.top.framework</groupId>
                        <artifactId>soa-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ctrip.ibu.platform</groupId>
                <artifactId>ibu-shark-sdk</artifactId>
                <version>5.1.3</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.jacoco</groupId>
                        <artifactId>org.jacoco.agent</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ctrip.car.pms</groupId>
                <artifactId>car-business-auth</artifactId>
                <version>1.0.16</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.ctrip.car.commodity.framework</groupId>
                        <artifactId>soa-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.26009</groupId>
                <artifactId>businessim-bff</artifactId>
                <version>0.0.30</version>
                <exclusions>
                    <exclusion>
                        <artifactId>cglib-nodep</artifactId>
                        <groupId>cglib</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.ctrip.soa.22697</groupId>
                <artifactId>carcommodityvendor</artifactId>
                <version>0.0.18</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.20392</groupId>
                <artifactId>carinfoservice</artifactId>
                <version>0.20.0</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-protobuf</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-xml</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-module-jaxb-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.basebiz</groupId>
                <artifactId>InternationalSmsHelperServiceClient</artifactId>
                <version>${internationalSmsHelperService.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.commodity</groupId>
                <artifactId>storeservice</artifactId>
                <version>${storeservice.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.ctrip.car.top.framework</groupId>
                        <artifactId>soa-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ctrip.car.commodity</groupId>
                <artifactId>basicservice</artifactId>
                <version>${baseservice-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.ctrip.car.top.framework</groupId>
                        <artifactId>soa-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.ctrip.soa.22745</groupId>
                <artifactId>carcoremetadataserivce</artifactId>
                <version>${carcoremetadataserivce-version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car</groupId>
                <artifactId>commodity-common</artifactId>
                <version>${carcommoditycommon-version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.car.25057</groupId>
                <artifactId>carcommoditycommonservice</artifactId>
                <version>0.0.64</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.soa.car.sd.vendorbasicdataservice.v1</groupId>
                <artifactId>vendorbasicdataservice</artifactId>
                <version>${vendorbasicdataservice-version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.25434</groupId>
                <artifactId>businessim</artifactId>
                <version>0.0.63</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>5.11.0</version>
                <scope>test</scope>
            </dependency>
            <!-- spock 核心包 -->
            <dependency>
                <groupId>org.spockframework</groupId>
                <artifactId>spock-core</artifactId>
                <version>2.3-groovy-4.0</version>
                <scope>test</scope>
            </dependency>
            <!-- groovy -->
            <dependency> <!-- use a specific Groovy version rather than the one specified by spock-core -->
                <groupId>org.apache.groovy</groupId>
                <artifactId>groovy</artifactId>
                <version>4.0.19</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>1.9.10</version>
            </dependency>
            <dependency>
                <groupId>org.objenesis</groupId>
                <artifactId>objenesis</artifactId>
                <version>3.2</version>
            </dependency>
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib-nodep</artifactId>
                <version>3.3.0</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>car-market-common</artifactId>
                <version>${market.commom.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.flight.bff</groupId>
                <artifactId>bff-sdk-signature</artifactId>
                <version>1.0.16</version>
                <exclusions>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-classic</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-slf4j-impl</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.ctrip.flight.mom</groupId>
                        <artifactId>common-model</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>2.2</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <forceJavacCompilerUse>true</forceJavacCompilerUse>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <id>default-instrument</id>
                        <goals>
                            <goal>instrument</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-restore-instrumented-classes</id>
                        <goals>
                            <goal>restore-instrumented-classes</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <!-- 指定jacoco插件生成的覆盖率测试报告文件路径 -->
                            <dataFile>${sonar.jacoco.reportPaths}</dataFile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <argLine>
                        -javaagent:"${settings.localRepository}"/org/jacoco/org.jacoco.agent/${jacoco.version}/org.jacoco.agent-${jacoco.version}-runtime.jar=destfile=${project.build.directory}/jacoco.exec,excludes=*
                        -javaagent:"${settings.localRepository}"/org/jmockit/jmockit/${jmockit.version}/jmockit-${jmockit.version}.jar
                        --add-opens java.base/java.text=ALL-UNNAMED
                        --add-opens java.base/java.lang=ALL-UNNAMED
                        --add-opens java.base/java.lang.reflect=ALL-UNNAMED
                        --add-opens java.base/sun.reflect.annotation=ALL-UNNAMED
                        --add-opens java.base/java.math=ALL-UNNAMED
                        --add-opens java.base/java.util=ALL-UNNAMED
                        --add-opens java.base/sun.util.calendar=ALL-UNNAMED
                        --add-opens java.base/java.util.concurrent=ALL-UNNAMED
                        --add-opens java.base/java.util.concurrent.atomic=ALL-UNNAMED
                        --add-opens java.base/java.io=ALL-UNNAMED
                        --add-opens java.base/java.net=ALL-UNNAMED
                        --add-opens java.xml/com.sun.org.apache.xerces.internal.jaxp.datatype=ALL-UNNAMED
                        --add-opens java.management/sun.management=ALL-UNNAMED
                        --add-opens java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED
                        --add-opens jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.vm.annotation=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.access=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.util.random=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.misc=ALL-UNNAMED
                        --add-opens jdk.jfr/jdk.jfr.internal.tool=ALL-UNNAMED
                    </argLine>
                    <systemPropertyVariables>
                        <jacoco-agent.destfile>target/jacoco.exec</jacoco-agent.destfile>
                    </systemPropertyVariables>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>3.0.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>compileTests</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.ctrip.ibu.platform</groupId>
                <artifactId>shark-maven-plugin</artifactId>
                <version>1.1.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>pack-download</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>