package com.ctrip.car.businessim.bff.service.proxy.adapter;

import com.ctrip.car.commodity.framework.soa.client.BaseClient;
import com.ctrip.car.market.client.contract.QueryInfoForTangtuRequestType;
import com.ctrip.car.market.client.contract.QueryInfoForTangtuResponseType;
import com.ctrip.car.market.client.contract.SDMarketingServiceClient;
import org.springframework.stereotype.Component;

@Component
public class SDMarketingServiceAdapter extends BaseClient<SDMarketingServiceClient> {

    public QueryInfoForTangtuResponseType queryInfoForTangtu(QueryInfoForTangtuRequestType request) throws Exception {
        return client.queryInfoForTangtu(request);
    }

}
