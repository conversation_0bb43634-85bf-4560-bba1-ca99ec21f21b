package com.ctrip.car.businessim.bff.service.proxy.soa;

import com.ctrip.car.businessim.bff.service.proxy.adapter.StoreServiceAdapter;
import com.ctrip.car.commodity.store.dto.StoreShortInfoDto;
import com.ctrip.car.commodity.store.entity.*;
import com.ctrip.car.top.BaseRequest;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class StoreServiceProxy {

    private final ILog logger = LogManager.getLogger("StoreServiceProxy");

    @Autowired
    private StoreServiceAdapter adapter;

    public List<StoreShortInfoDto> getStoreInfo(Long vendorId) {
        try {
            GetStoreInfoListRequestType storeRequest = new GetStoreInfoListRequestType();
            storeRequest.setVendorIdList(Lists.newArrayList(vendorId));
            storeRequest.setStoreLevelList(Lists.newArrayList(1));
            storeRequest.setPageIndex(1);
            storeRequest.setPageSize(1000);
            GetStoreInfoListResponseType storeResponse = adapter.getStoreInfoList(storeRequest);
            return CollectionUtils.isNotEmpty(storeResponse.getStoreInfoList()) ? storeResponse.getStoreInfoList() : Lists.newArrayList();
        } catch (Exception e) {
            logger.error("getStoreInfo", e);
            return Lists.newArrayList();
        }
    }

    public List<Integer> getAllVendorCity(Long vendorId) {
        try {
            GetVendorCityListRequestType requestType = new GetVendorCityListRequestType();
            requestType.setBaseRequest(new BaseRequest());
            requestType.getBaseRequest().setRequestId(UUID.randomUUID().toString());
            requestType.setVendorId(vendorId);
            GetVendorCityListResponseType responseType = adapter.getVendorCityList(requestType);
            return CollectionUtils.isNotEmpty(responseType.getCityIdList()) ? responseType.getCityIdList() : Lists.newArrayList();
        } catch (Exception e) {
            logger.error("getAllVendorCity", e);
            return Lists.newArrayList();
        }
    }
}
