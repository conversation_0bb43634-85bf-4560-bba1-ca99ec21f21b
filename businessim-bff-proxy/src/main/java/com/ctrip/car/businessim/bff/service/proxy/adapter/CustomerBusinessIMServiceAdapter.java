package com.ctrip.car.businessim.bff.service.proxy.adapter;

import com.ctrip.car.commodity.framework.core.clogging.Logger;
import com.ctrip.car.commodity.framework.core.clogging.LoggerFactory;
import com.ctrip.car.commodity.framework.soa.client.BaseClient;
import com.ctrip.car.customer.businessim.contract.BusinessIMServiceClient;
import com.ctrip.car.customer.businessim.contract.servicetype.*;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.*;
import com.ctrip.car.customer.businessim.contract.servicetype.seo.*;
import org.springframework.stereotype.Component;

@Component
public class CustomerBusinessIMServiceAdapter extends BaseClient<BusinessIMServiceClient> {

    protected static Logger log = LoggerFactory.getLogger(CustomerBusinessIMServiceAdapter.class);

    public GetIMGrayStatusResponseType getIMGrayStatus(GetIMGrayStatusRequestType request) {
        GetIMGrayStatusResponseType response = new GetIMGrayStatusResponseType();
        try {
            response = client.getIMGrayStatus(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.getIMGrayStatus", e);
        }
        return response;
    }

    public ListOrderForIMCustomizeAreaResponseType listOrderForIMCustomizeArea(ListOrderForIMCustomizeAreaRequestType request) {
        ListOrderForIMCustomizeAreaResponseType response = new ListOrderForIMCustomizeAreaResponseType();
        try {
            response = client.listOrderForIMCustomizeArea(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.listOrderForIMCustomizeArea", e);
        }
        return response;
    }

    public ListDropdownStoreResponseType listDropdownStore(ListDropdownStoreRequestType request) {
        ListDropdownStoreResponseType response = new ListDropdownStoreResponseType();
        try {
            response = client.listDropdownStore(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.listDropdownStore", e);
        }
        return response;
    }

    public GetStoreBusinessTimeResponseType getStoreBusinessTime(GetStoreBusinessTimeRequestType request) {
        GetStoreBusinessTimeResponseType response = new GetStoreBusinessTimeResponseType();
        try {
            response = client.getStoreBusinessTime(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.getStoreBusinessTime", e);
        }
        return response;
    }

    public UpdateStoreBusinessTimeResponseType updateStoreBusinessTime(UpdateStoreBusinessTimeRequestType request) {
        UpdateStoreBusinessTimeResponseType response = new UpdateStoreBusinessTimeResponseType();
        try {
            response = client.updateStoreBusinessTime(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.updateStoreBusinessTime", e);
        }
        return response;
    }

    public ListBusinessIMAgentResponseType listBusinessIMAgent(ListBusinessIMAgentRequestType request) {
        ListBusinessIMAgentResponseType response = new ListBusinessIMAgentResponseType();
        try {
            response = client.listBusinessIMAgent(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.listBusinessIMAgent", e);
        }
        return response;
    }

    public UpdateBusinessIMAgentResponseType updateBusinessIMAgent(UpdateBusinessIMAgentRequestType request) {
        UpdateBusinessIMAgentResponseType response = new UpdateBusinessIMAgentResponseType();
        try {
            response = client.updateBusinessIMAgent(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.updateBusinessIMAgent", e);
        }
        return response;
    }

    public AddIMAgentResponseType addIMAgent(AddIMAgentRequestType request) {
        AddIMAgentResponseType response = new AddIMAgentResponseType();
        try {
            response = client.addIMAgent(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.addIMAgent", e);
        }
        return response;
    }

    public ListDropdownUserResponseType listDropdownUser(ListDropdownUserRequestType request) {
        ListDropdownUserResponseType response = new ListDropdownUserResponseType();
        try {
            response = client.listDropdownUser(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.listDropdownUser", e);
        }
        return response;
    }

    public ListAgentAllocateRuleResponseType listAgentAllocateRule(ListAgentAllocateRuleRequestType request) {
        ListAgentAllocateRuleResponseType response = new ListAgentAllocateRuleResponseType();
        try {
            response = client.listAgentAllocateRule(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.listAgentAllocateRule", e);
        }
        return response;
    }

    public UpdateAgentAllocateRuleResponseType updateAgentAllocateRule(UpdateAgentAllocateRuleRequestType request) {
        UpdateAgentAllocateRuleResponseType response = new UpdateAgentAllocateRuleResponseType();
        try {
            response = client.updateAgentAllocateRule(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.updateAgentAllocateRule", e);
        }
        return response;
    }

    public GetB2OLinkAndParameterResponseType getB2OLinkAndParameter(GetB2OLinkAndParameterRequestType request) {
        GetB2OLinkAndParameterResponseType response = new GetB2OLinkAndParameterResponseType();
        try {
            response = client.getB2OLinkAndParameter(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.getB2OLinkAndParameter", e);
        }
        return response;
    }

    public GetB2OAllowAccessResponseType getB2OAllowAccess(GetB2OAllowAccessRequestType request) {
        GetB2OAllowAccessResponseType response = new GetB2OAllowAccessResponseType();
        try {
            response = client.getB2OAllowAccess(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.getB2OAllowAccess", e);
        }
        return response;
    }

    public GetIMSessionReportResponseType getIMSessionReport(GetIMSessionReportRequestType request) {
        GetIMSessionReportResponseType response = new GetIMSessionReportResponseType();
        try {
            response = client.getIMSessionReport(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.getIMSessionReport", e);
        }
        return response;
    }

    public DownloadIMSessionResponseType downloadIMSession(DownloadIMSessionRequestType request) {
        DownloadIMSessionResponseType response = new DownloadIMSessionResponseType();
        try {
            response = client.downloadIMSession(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.downloadIMSession", e);
        }
        return response;
    }

    public DownloadIVRCallResponseType downloadIVRCall(DownloadIVRCallRequestType request) {
        DownloadIVRCallResponseType response = new DownloadIVRCallResponseType();
        try {
            response = client.downloadIVRCall(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.downloadIVRCall", e);
        }
        return response;
    }

    public GetIVRCallReportResponseType getIVRCallReport(GetIVRCallReportRequestType request) {
        GetIVRCallReportResponseType response = new GetIVRCallReportResponseType();
        try {
            response = client.getIVRCallReport(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.getIVRCallReport", e);
        }
        return response;
    }

    public GetDecryptDataResponseType getDecryptData(GetDecryptDataRequestType request) {
        GetDecryptDataResponseType response = new GetDecryptDataResponseType();
        try {
            response = client.getDecryptData(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.getDecryptData", e);
        }
        return response;
    }

    public GetOrderDetailLinkResponseType getOrderDetailLink(GetOrderDetailLinkRequestType request) {
        GetOrderDetailLinkResponseType response = new GetOrderDetailLinkResponseType();
        try {
            response = client.getOrderDetailLink(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.getOrderDetailLink", e);
        }
        return response;
    }

    public ListServiceDataIMAgentResponseType listServiceDataIMAgent(ListServiceDataIMAgentRequestType request) {
        ListServiceDataIMAgentResponseType response = new ListServiceDataIMAgentResponseType();
        try {
            response = client.listServiceDataIMAgent(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.listServiceDataIMAgent", e);
        }
        return response;
    }

    public ListServiceDataDropdownStoreResponseType listServiceDataDropdownStore(ListServiceDataDropdownStoreRequestType request) {
        ListServiceDataDropdownStoreResponseType response = new ListServiceDataDropdownStoreResponseType();
        try {
            response = client.listServiceDataDropdownStore(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.listServiceDataDropdownStore", e);
        }
        return response;
    }

    public GetBusinessNumberByCallIdResponseType getBusinessNumberByCallId(GetBusinessNumberByCallIdRequestType request) {
        GetBusinessNumberByCallIdResponseType response = new GetBusinessNumberByCallIdResponseType();
        try {
            response = client.getBusinessNumberByCallId(request);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.getBusinessNumberByCallId", e);
        }
        return response;
    }

    public QueryActivityListResponseType queryActivityList(QueryActivityListRequestType requestType) {
        QueryActivityListResponseType response = new QueryActivityListResponseType();
        try {
            response = client.queryActivityList(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.queryActivityList", e);
        }
        return response;
    }

    public QueryVendorActivityListResponseType queryVendorActivityList(QueryVendorActivityListRequestType requestType) {
        QueryVendorActivityListResponseType response = new QueryVendorActivityListResponseType();
        try {
            response = client.queryVendorActivityList(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.queryVendorActivityList", e);
        }
        return response;
    }

    public QueryVendorActivityDetailResponseType queryVendorActivityDetail(QueryVendorActivityDetailRequestType requestType) {
        QueryVendorActivityDetailResponseType response = new QueryVendorActivityDetailResponseType();
        try {
            response = client.queryVendorActivityDetail(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.queryVendorActivityDetail", e);
        }
        return response;
    }

    public JoinActivityResponseType joinActivity(JoinActivityRequestType requestType) {
        JoinActivityResponseType response = new JoinActivityResponseType();
        try {
            response = client.joinActivity(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.joinActivity", e);
        }
        return response;
    }

    public ModifyActivityResponseType modifyActivity(ModifyActivityRequestType requestType) {
        ModifyActivityResponseType response = new ModifyActivityResponseType();
        try {
            response = client.modifyActivity(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.modifyActivity", e);
        }
        return response;
    }

    public UpdateActivityStatusResponseType updateActivityStatus(UpdateActivityStatusRequestType requestType) {
        UpdateActivityStatusResponseType response = new UpdateActivityStatusResponseType();
        try {
            response = client.updateActivityStatus(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.updateActivityStatus", e);
        }
        return response;
    }

    public QueryVendorInfoByUserIdResponseType queryVendorInfoByUserId(QueryVendorInfoByUserIdRequestType requestType) {
        QueryVendorInfoByUserIdResponseType response = new QueryVendorInfoByUserIdResponseType();
        try {
            response = client.queryVendorInfoByUserId(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.queryVendorInfoByUserId", e);
        }
        return response;
    }

    public QueryOpenCityInfoByVendorIdsResponseType queryOpenCityInfoByVendorIds(QueryOpenCityInfoByVendorIdsRequestType requestType) {
        QueryOpenCityInfoByVendorIdsResponseType response = new QueryOpenCityInfoByVendorIdsResponseType();
        try {
            response = client.queryOpenCityInfoByVendorIds(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.queryOpenCityInfoByVendorIds", e);
        }
        return response;
    }

    public QueryStoreInfoByCityAndVendorResponseType queryStoreInfoByCityAndVendor(QueryStoreInfoByCityAndVendorRequestType requestType) {
        QueryStoreInfoByCityAndVendorResponseType response = new QueryStoreInfoByCityAndVendorResponseType();
        try {
            response = client.queryStoreInfoByCityAndVendor(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.queryStoreInfoByCityAndVendor", e);
        }
        return response;
    }

    public QueryStandardProductInfoResponseType queryStandardProductInfo(QueryStandardProductInfoRequestType requestType) {
        QueryStandardProductInfoResponseType response = new QueryStandardProductInfoResponseType();
        try {
            response = client.queryStandardProductInfo(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.queryStandardProductInfo", e);
        }
        return response;
    }

    public QueryLabeInfoByConditionResponseType queryLabeInfoByCondition(QueryLabeInfoByConditionRequestType requestType) {
        QueryLabeInfoByConditionResponseType response = new QueryLabeInfoByConditionResponseType();
        try {
            response = client.queryLabeInfoByCondition(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.queryLabeInfoByCondition", e);
        }
        return response;
    }

    public QueryVehicleGroupInfoResponseType queryVehicleGroupInfo(QueryVehicleGroupInfoRequestType requestType) {
        QueryVehicleGroupInfoResponseType response = new QueryVehicleGroupInfoResponseType();
        try {
            response = client.queryVehicleGroupInfo(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.queryVehicleGroupInfo", e);
        }
        return response;
    }

    public CheckShopResponseType checkShop(CheckShopRequestType requestType) {
        CheckShopResponseType response = new CheckShopResponseType();
        try {
            response = client.checkShop(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.checkShop", e);
        }
        return response;
    }

    public ShopOpenResponseType shopOpen(ShopOpenRequestType requestType) {
        ShopOpenResponseType response = new ShopOpenResponseType();
        try {
            response = client.shopOpen(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.shopOpen", e);
        }
        return response;
    }

    public QueryStoreListResponseType queryStoreList(QueryStoreListRequestType requestType) {
        QueryStoreListResponseType response = new QueryStoreListResponseType();
        try {
            response = client.queryStoreList(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.queryStoreList", e);
        }
        return response;
    }

    public QueryStoreQRCodeListResponseType queryStoreQRCodeList(QueryStoreQRCodeListRequestType requestType) {
        QueryStoreQRCodeListResponseType response = new QueryStoreQRCodeListResponseType();
        try {
            response = client.queryStoreQRCodeList(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.queryStoreQRCodeList", e);
        }
        return response;
    }

    public DeleteQRCodeResponseType deleteQRCode(DeleteQRCodeRequestType requestType) {
        DeleteQRCodeResponseType response = new DeleteQRCodeResponseType();
        try {
            response = client.deleteQRCode(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.deleteQRCode", e);
        }
        return response;
    }

    public AddQRCodeResponseType addQRCode(AddQRCodeRequestType requestType) {
        AddQRCodeResponseType response = new AddQRCodeResponseType();
        try {
            response = client.addQRCode(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.addQRCode", e);
        }
        return response;
    }

    public QueryQRCodeDetailResponseType queryQRCodeDetail(QueryQRCodeDetailRequestType requestType) {
        QueryQRCodeDetailResponseType response = new QueryQRCodeDetailResponseType();
        try {
            response = client.queryQRCodeDetail(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.queryQRCodeDetail", e);
        }
        return response;
    }

    public QueryPromoterListResponseType queryPromoterList(QueryPromoterListRequestType requestType) {
        QueryPromoterListResponseType response = new QueryPromoterListResponseType();
        try {
            response = client.queryPromoterList(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.queryPromoterList", e);
        }
        return response;
    }

    public AddPromoterResponseType addPromoter(AddPromoterRequestType requestType) {
        AddPromoterResponseType response = new AddPromoterResponseType();
        try {
            response = client.addPromoter(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.addPromoter", e);
        }
        return response;
    }

    public QueryQRCodeResponseType queryQRCode(QueryQRCodeRequestType requestType) {
        QueryQRCodeResponseType response = new QueryQRCodeResponseType();
        try {
            response = client.queryQRCode(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.queryQRCode", e);
        }
        return response;
    }

    public QueryCarCityResponseType queryCarCity(QueryCarCityRequestType requestType) {
        QueryCarCityResponseType response = new QueryCarCityResponseType();
        try {
            response = client.queryCarCity(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.queryCarCity", e);
        }
        return response;
    }

    public GetB2ONewLinkAndParameterResponseType getB2ONewLinkAndParameter(GetB2ONewLinkAndParameterRequestType requestType) {
        GetB2ONewLinkAndParameterResponseType response = new GetB2ONewLinkAndParameterResponseType();
        try {
            response = client.getB2ONewLinkAndParameter(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.getB2ONewLinkAndParameter", e);
        }
        return response;
    }

    public GetConsultRecordsLinkResponseType getConsultRecordsLink(GetConsultRecordsLinkRequestType requestType) {
        GetConsultRecordsLinkResponseType response = new GetConsultRecordsLinkResponseType();
        try {
            response = client.getConsultRecordsLink(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.getConsultRecordsLink", e);
        }
        return response;
    }

    public QueryLastSessionLinkResponseType queryLastSessionLink(QueryLastSessionLinkRequestType requestType) {
        QueryLastSessionLinkResponseType response = new QueryLastSessionLinkResponseType();
        try {
            response = client.queryLastSessionLink(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.queryLastSessionLink", e);
        }
        return response;
    }

    public GetB2OImGrayStatusResponseType getB2OImGrayStatus(GetB2OImGrayStatusRequestType requestType) {
        GetB2OImGrayStatusResponseType response = new GetB2OImGrayStatusResponseType();
        try {
            response = client.getB2OImGrayStatus(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.getB2OImGrayStatus", e);
        }
        return response;
    }

    public GetB2CNewLinkResponseType GetB2CNewLink(GetB2CNewLinkRequestType requestType) {
        GetB2CNewLinkResponseType response = new GetB2CNewLinkResponseType();
        try {
            response = client.getB2CNewLink(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.GetB2CNewLink", e);
        }
        return response;
    }

    public QueryVendorPricePlanCityResponseType queryVendorPricePlanCity(QueryVendorPricePlanCityRequestType requestType){
        QueryVendorPricePlanCityResponseType response = new QueryVendorPricePlanCityResponseType();
        try {
            response = client.queryVendorPricePlanCity(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.queryVendorPricePlanCity", e);
        }
        return response;
    }

    public QuerySeoPageConfigResponseType querySeoPageConfig(QuerySeoPageConfigRequestType requestType) {
        QuerySeoPageConfigResponseType response = new QuerySeoPageConfigResponseType();
        try {
            response = client.querySeoPageConfig(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.querySeoPageConfig", e);
        }
        return response;
    }

    public SaveSeoPageConfigResponseType saveSeoPageConfig(SaveSeoPageConfigRequestType requestType) {
        SaveSeoPageConfigResponseType response = new SaveSeoPageConfigResponseType();
        try {
            response = client.saveSeoPageConfig(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.saveSeoPageConfig", e);
        }
        return response;
    }

    public UpdateSeoPageConfigStatusResponseType updateSeoPageConfigStatus(UpdateSeoPageConfigStatusRequestType requestType) {
        UpdateSeoPageConfigStatusResponseType response = new UpdateSeoPageConfigStatusResponseType();
        try {
            response = client.updateSeoPageConfigStatus(requestType);
        } catch (Exception e) {
            log.error("CustomerBusinessIMServiceAdapter.updateSeoPageConfigStatus", e);
        }
        return response;
    }

}
