package com.ctrip.car.businessim.bff.service.proxy.adapter;

import com.ctrip.car.commodity.framework.core.clogging.Logger;
import com.ctrip.car.commodity.framework.core.clogging.LoggerFactory;
import com.ctrip.car.commodity.framework.soa.client.BaseClient;
import com.ctrip.car.commodity.vendor.service.method.*;
import com.ctrip.car.commodity.vendor.service.soa.CarCommodityVendorApiClient;
import org.springframework.stereotype.Component;

@Component
public class CarCommodityVendorSvcAdapter extends BaseClient<CarCommodityVendorApiClient> {

    protected static Logger log = LoggerFactory.getLogger(CarCommodityVendorSvcAdapter.class);

    public QueryCarUserResponseType queryCarUser(QueryCarUserRequestType request) {
        QueryCarUserResponseType response = new QueryCarUserResponseType();
        try {
            response = client.queryCarUser(request);
        } catch (Exception e) {
            log.error("CarCommodityVendorSvcAdapter.queryCarUser", e);
        }
        return response;
    }

    public QueryCarUserListResponseType queryCarUserList(QueryCarUserListRequestType request) {
        QueryCarUserListResponseType response = new QueryCarUserListResponseType();
        try {
            response = client.queryCarUserList(request);
        } catch (Exception e) {
            log.error("CarCommodityVendorSvcAdapter.queryCarUserList", e);
        }
        return response;
    }

    public QueryVendorResponseType queryVendor(QueryVendorRequestType request) {//有缓存，有延迟
        QueryVendorResponseType response = new QueryVendorResponseType();
        try {
            response = client.queryVendor(request);
        } catch (Exception e) {
            log.error("CarCommodityVendorSvcAdapter.queryVendor", e);
        }
        return response;
    }

    public QueryVendorListResponseType queryVendorList(QueryVendorListRequestType request) {//无缓存，实时
        QueryVendorListResponseType response = new QueryVendorListResponseType();
        try {
            response = client.queryVendorList(request);
        } catch (Exception e) {
            log.error("CarCommodityVendorSvcAdapter.queryVendorList", e);
        }
        return response;
    }

}
