package com.ctrip.car.businessim.bff.service.proxy.adapter;

import com.ctrip.car.commodity.framework.core.clogging.Logger;
import com.ctrip.car.commodity.framework.core.clogging.LoggerFactory;
import com.ctrip.car.commodity.framework.soa.client.BaseClient;
import com.ctrip.car.commodity.store.api.StoreServiceClient;
import com.ctrip.car.commodity.store.entity.*;
import org.springframework.stereotype.Component;

@Component
public class StoreServiceAdapter extends BaseClient<StoreServiceClient> {

    protected static Logger log = LoggerFactory.getLogger(StoreServiceAdapter.class);

    public GetStoreInfoListResponseType getStoreInfoList(GetStoreInfoListRequestType request) {
        GetStoreInfoListResponseType response = new GetStoreInfoListResponseType();
        try {
            response = client.getStoreInfoList(request);
        } catch (Exception e) {
            log.error("StoreServiceAdapter.getStoreInfoList", e);
        }
        return response;
    }

    public GetStoreListResponseType getStoreList(GetStoreListRequestType request) {
        GetStoreListResponseType response = new GetStoreListResponseType();
        try {
            response = client.getStoreList(request);
        } catch (Exception e) {
            log.error("StoreServiceAdapter.getStoreList", e);
        }
        return response;

    }

    public GetVendorCityListResponseType getVendorCityList(GetVendorCityListRequestType request) {
        GetVendorCityListResponseType response = new GetVendorCityListResponseType();
        try {
            response = client.getVendorCityList(request);
        } catch (Exception e) {
            log.error("StoreServiceAdapter.getVendorCityList", e);
        }
        return response;
    }

}
