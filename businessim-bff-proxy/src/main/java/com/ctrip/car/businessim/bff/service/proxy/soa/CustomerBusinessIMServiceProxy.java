package com.ctrip.car.businessim.bff.service.proxy.soa;

import com.ctrip.car.businessim.bff.service.proxy.adapter.CustomerBusinessIMServiceAdapter;
import com.ctrip.car.customer.businessim.contract.dto.activity.CityDto;
import com.ctrip.car.customer.businessim.contract.servicetype.*;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.*;
import com.ctrip.car.customer.businessim.contract.servicetype.seo.*;
import com.ctrip.car.pms.component.common.util.JsonUtil;
import com.ctrip.car.top.BaseRequest;
import com.ctriposs.baiji.rpc.common.types.AckCodeType;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.core.util.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/08/06
 **/
@Service
public class CustomerBusinessIMServiceProxy {

    private static final Logger log = LoggerFactory.getLogger(CustomerBusinessIMServiceProxy.class);
    @Autowired
    private CustomerBusinessIMServiceAdapter adapter;

    public GetIMGrayStatusResponseType getIMGrayStatus(GetIMGrayStatusRequestType request) {
        return adapter.getIMGrayStatus(request);
    }

    public ListOrderForIMCustomizeAreaResponseType listOrderForIMCustomizeArea(ListOrderForIMCustomizeAreaRequestType request) {
        return adapter.listOrderForIMCustomizeArea(request);
    }

    public ListDropdownStoreResponseType listDropdownStore(ListDropdownStoreRequestType request) {
        return adapter.listDropdownStore(request);
    }

    public GetStoreBusinessTimeResponseType getStoreBusinessTime(GetStoreBusinessTimeRequestType request) {
        return adapter.getStoreBusinessTime(request);
    }

    public UpdateStoreBusinessTimeResponseType updateStoreBusinessTime(UpdateStoreBusinessTimeRequestType request) {
        return adapter.updateStoreBusinessTime(request);
    }

    public ListBusinessIMAgentResponseType listBusinessIMAgent(ListBusinessIMAgentRequestType request) {
        return adapter.listBusinessIMAgent(request);
    }

    public UpdateBusinessIMAgentResponseType updateBusinessIMAgent(UpdateBusinessIMAgentRequestType request) {
        return adapter.updateBusinessIMAgent(request);
    }

    public AddIMAgentResponseType addIMAgent(AddIMAgentRequestType request) {
        return adapter.addIMAgent(request);
    }

    public ListDropdownUserResponseType listDropdownUser(ListDropdownUserRequestType request) {
        return adapter.listDropdownUser(request);
    }

    public ListAgentAllocateRuleResponseType listAgentAllocateRule(ListAgentAllocateRuleRequestType request) {
        return adapter.listAgentAllocateRule(request);
    }

    public UpdateAgentAllocateRuleResponseType updateAgentAllocateRule(UpdateAgentAllocateRuleRequestType request) {
        return adapter.updateAgentAllocateRule(request);
    }

    public GetB2OLinkAndParameterResponseType getB2OLinkAndParameter(GetB2OLinkAndParameterRequestType request) {
        return adapter.getB2OLinkAndParameter(request);
    }

    public GetB2OAllowAccessResponseType getB2OAllowAccess(GetB2OAllowAccessRequestType request) {
        return adapter.getB2OAllowAccess(request);
    }

    public GetIMSessionReportResponseType getIMSessionReport(GetIMSessionReportRequestType request) {
        return adapter.getIMSessionReport(request);
    }

    public DownloadIMSessionResponseType downloadIMSession(DownloadIMSessionRequestType request) {
        return adapter.downloadIMSession(request);
    }

    public DownloadIVRCallResponseType downloadIVRCall(DownloadIVRCallRequestType request) {
        return adapter.downloadIVRCall(request);
    }

    public GetIVRCallReportResponseType getIVRCallReport(GetIVRCallReportRequestType request) {
        return adapter.getIVRCallReport(request);
    }

    public GetDecryptDataResponseType getDecryptData(GetDecryptDataRequestType request) {
        return adapter.getDecryptData(request);
    }

    public GetOrderDetailLinkResponseType getOrderDetailLink(GetOrderDetailLinkRequestType request) {
        return adapter.getOrderDetailLink(request);
    }

    public ListServiceDataIMAgentResponseType listServiceDataIMAgent(ListServiceDataIMAgentRequestType request) {
        return adapter.listServiceDataIMAgent(request);
    }

    public ListServiceDataDropdownStoreResponseType listServiceDataDropdownStore(ListServiceDataDropdownStoreRequestType request) {
        return adapter.listServiceDataDropdownStore(request);
    }

    public GetBusinessNumberByCallIdResponseType getBusinessNumberByCallId(GetBusinessNumberByCallIdRequestType request) {
        return adapter.getBusinessNumberByCallId(request);
    }

    public QueryActivityListResponseType queryActivityList(QueryActivityListRequestType request) {
        if (request.getPageSize() == null || request.getPageSize() <= 0) request.setPageSize(10);
        if (request.getPageNo() == null || request.getPageNo() <= 0) request.setPageNo(1);
        return adapter.queryActivityList(request);
    }

    public QueryVendorActivityListResponseType queryVendorActivityList(QueryVendorActivityListRequestType request) {
        if (request.getPageSize() == null || request.getPageSize() <= 0) request.setPageSize(10);
        if (request.getPageNo() == null || request.getPageNo() <= 0) request.setPageNo(1);
        return adapter.queryVendorActivityList(request);
    }

    public QueryVendorActivityDetailResponseType queryVendorActivityDetail(QueryVendorActivityDetailRequestType request) {
        return adapter.queryVendorActivityDetail(request);
    }

    public JoinActivityResponseType joinActivity(JoinActivityRequestType request) {
        return adapter.joinActivity(request);
    }

    public ModifyActivityResponseType modifyActivity(ModifyActivityRequestType request) {
        return adapter.modifyActivity(request);
    }

    public UpdateActivityStatusResponseType updateActivityStatus(UpdateActivityStatusRequestType request) {
        return adapter.updateActivityStatus(request);
    }

    public QueryVendorInfoByUserIdResponseType queryVendorInfoByUserId(QueryVendorInfoByUserIdRequestType request) {
        if (request == null || request.getUserId() == null) return null;
        return adapter.queryVendorInfoByUserId(request);
    }

    public QueryOpenCityInfoByVendorIdsResponseType queryOpenCityInfoList(QueryOpenCityInfoByVendorIdsRequestType request) {
        return adapter.queryOpenCityInfoByVendorIds(request);
    }

    public QueryStoreInfoByCityAndVendorResponseType queryStoreInfoList(QueryStoreInfoByCityAndVendorRequestType request) {
        return adapter.queryStoreInfoByCityAndVendor(request);
    }

    public QueryStandardProductInfoResponseType queryProductInfoList(QueryStandardProductInfoRequestType request) {
        return adapter.queryStandardProductInfo(request);
    }

    public QueryLabeInfoByConditionResponseType queryLabelInfo(QueryLabeInfoByConditionRequestType request) {
        return adapter.queryLabeInfoByCondition(request);
    }

    public QueryVehicleGroupInfoResponseType queryVehicleGroupInfo(QueryVehicleGroupInfoRequestType request) {
        return adapter.queryVehicleGroupInfo(request);
    }

    public List<Integer> queryVendorVehicleGroup(Long vendorId, String userId, com.ctrip.car.top.BaseRequest baseRequest) {
        try {
            QueryVehicleGroupInfoRequestType queryVehicleGroupInfoRequestType = new QueryVehicleGroupInfoRequestType();
            queryVehicleGroupInfoRequestType.setBaseRequest(baseRequest);
            queryVehicleGroupInfoRequestType.setUserId(Long.parseLong(userId));
            queryVehicleGroupInfoRequestType.setVendorId(vendorId);
            QueryVehicleGroupInfoResponseType vehicleGroupInfo = adapter.queryVehicleGroupInfo(queryVehicleGroupInfoRequestType);
            return vehicleGroupInfo != null && CollectionUtils.isNotEmpty(vehicleGroupInfo.getInfos()) ?
                    vehicleGroupInfo.getInfos().stream().filter(l -> StringUtils.isNotBlank(l.getDataKey()))
                            .map(dataSourceInfo -> Integer.parseInt(dataSourceInfo.getDataKey()))
                            .collect(Collectors.toList()) : Lists.newArrayList();
        } catch (Exception e) {
            return Lists.newArrayList();
        }
    }

    public AddPromoterResponseType addPromoter(AddPromoterRequestType requestType) {
        return adapter.addPromoter(requestType);
    }

    public AddQRCodeResponseType addQRCode(AddQRCodeRequestType requestType) {
        return adapter.addQRCode(requestType);
    }

    public CheckShopResponseType checkShop(CheckShopRequestType requestType) {
        return adapter.checkShop(requestType);
    }

    public DeleteQRCodeResponseType deleteQRCode(DeleteQRCodeRequestType requestType) {
        return adapter.deleteQRCode(requestType);
    }

    public QueryPromoterListResponseType queryPromoterList(QueryPromoterListRequestType requestType) {
        return adapter.queryPromoterList(requestType);
    }

    public QueryQRCodeResponseType queryQRCode(QueryQRCodeRequestType requestType) {
        return adapter.queryQRCode(requestType);
    }

    public QueryQRCodeDetailResponseType queryQRCodeDetail(QueryQRCodeDetailRequestType requestType) {
        return adapter.queryQRCodeDetail(requestType);
    }

    public QueryStoreListResponseType queryStoreList(QueryStoreListRequestType requestType) {
        return adapter.queryStoreList(requestType);
    }

    public QueryStoreQRCodeListResponseType queryStoreQRCodeList(QueryStoreQRCodeListRequestType requestType) {
        return adapter.queryStoreQRCodeList(requestType);
    }

    public ShopOpenResponseType shopOpen(ShopOpenRequestType requestType) {
        return adapter.shopOpen(requestType);
    }

    public List<CityDto> queryCarCity(List<Long> cityIds) {
        try {
            QueryCarCityRequestType requestType = new QueryCarCityRequestType();
            requestType.setCityIds(cityIds);
            requestType.setBaseRequest(new BaseRequest());
            QueryCarCityResponseType responseType = adapter.queryCarCity(requestType);
            return Objects.nonNull(responseType) && CollectionUtils.isNotEmpty(responseType.getCityList()) ? responseType.getCityList() : Lists.newArrayList();
        } catch (Exception e) {
            return Lists.newArrayList();
        }
    }

    public GetB2ONewLinkAndParameterResponseType getB2ONewLinkAndParameter(GetB2ONewLinkAndParameterRequestType request) {
        return adapter.getB2ONewLinkAndParameter(request);
    }

    public GetConsultRecordsLinkResponseType getConsultRecordsLink(GetConsultRecordsLinkRequestType request) {
        return adapter.getConsultRecordsLink(request);
    }

    public QueryLastSessionLinkResponseType queryLastSessionLink(QueryLastSessionLinkRequestType request) {
        return adapter.queryLastSessionLink(request);
    }

    public GetB2OImGrayStatusResponseType getB2OImGrayStatus(GetB2OImGrayStatusRequestType request) {
        return adapter.getB2OImGrayStatus(request);
    }

    public GetB2CNewLinkResponseType GetB2CNewLink(GetB2CNewLinkRequestType request) {
        return adapter.GetB2CNewLink(request);
    }

    public List<Integer> queryVendorPricePlanCity(Long vendorId, BaseRequest baseRequest) {
        try{
            QueryVendorPricePlanCityRequestType requestType = new QueryVendorPricePlanCityRequestType();
            requestType.setVendorId(vendorId);
            requestType.setBaseRequest(baseRequest);
            QueryVendorPricePlanCityResponseType responseType = adapter.queryVendorPricePlanCity(requestType);
            return Objects.nonNull(responseType) && CollectionUtils.isNotEmpty(responseType.getCityList()) ? responseType.getCityList().stream().map(Long::intValue).toList() : Lists.newArrayList();
        } catch (Exception e) {
            return Lists.newArrayList();
        }
    }

    public QuerySeoPageConfigResponseType querySeoPageConfig(QuerySeoPageConfigRequestType requestType) {
        return adapter.querySeoPageConfig(requestType);
    }

    public SaveSeoPageConfigResponseType saveSeoPageConfig(SaveSeoPageConfigRequestType requestType) {
        return adapter.saveSeoPageConfig(requestType);
    }

    public UpdateSeoPageConfigStatusResponseType updateSeoPageConfigStatus(UpdateSeoPageConfigStatusRequestType requestType) {
        return adapter.updateSeoPageConfigStatus(requestType);
    }
}
