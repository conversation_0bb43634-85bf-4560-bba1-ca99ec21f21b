package com.ctrip.car.businessim.bff.service.proxy.soa;

import com.ctrip.car.businessim.bff.service.proxy.adapter.SDMarketingServiceAdapter;
import com.ctrip.car.businessim.bff.service.proxy.dto.VehicleGroupDTO;
import com.ctrip.car.market.client.contract.QueryInfoForTangtuRequestType;
import com.ctrip.car.market.client.contract.QueryInfoForTangtuResponseType;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class SDMarketingServiceProxy {

    private final ILog logger = LogManager.getLogger("SDMarketingServiceProxy");

    @Autowired
    private SDMarketingServiceAdapter adapter;

    public List<VehicleGroupDTO> queryAllVehicleGroup() {
        QueryInfoForTangtuRequestType requestType = new QueryInfoForTangtuRequestType();
        requestType.setKeyType(3);
        try {
            QueryInfoForTangtuResponseType responseType = adapter.queryInfoForTangtu(requestType);
            if (responseType != null && CollectionUtils.isNotEmpty(responseType.getInfos())) {
                return responseType.getInfos().stream().filter(l -> NumberUtils.isNumber(l.getKey()))
                        .map(tangtuBasicInfo -> {
                            VehicleGroupDTO vehicleGroup = new VehicleGroupDTO();
                            vehicleGroup.setCode(Integer.parseInt(tangtuBasicInfo.getKey()));
                            vehicleGroup.setName(tangtuBasicInfo.getDisplayName());
                            return vehicleGroup;
                        })
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            logger.error("SDMarketingServiceProxy.queryAllVehicleGroup", e);
        }
        return new ArrayList<>();
    }
}
