package com.ctrip.car.businessim.bff.service.proxy.soa;

import com.ctrip.car.businessim.bff.service.proxy.adapter.CarCommodityVendorSvcAdapter;
import com.ctrip.car.commodity.framework.core.clogging.Logger;
import com.ctrip.car.commodity.framework.core.clogging.LoggerFactory;
import com.ctrip.car.commodity.vendor.service.contract.dto.BaseRequest;
import com.ctrip.car.commodity.vendor.service.contract.dto.CarBasicUserDto;
import com.ctrip.car.commodity.vendor.service.contract.dto.VendorInfo;
import com.ctrip.car.commodity.vendor.service.method.*;
import com.ctrip.car.market.common.utils.GsonUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
public class CarCommodityVendorServiceProxy {

    private static final Logger log = LoggerFactory.getLogger(CarCommodityVendorServiceProxy.class);

    @Autowired
    private CarCommodityVendorSvcAdapter adapter;

    public QueryCarUserResponseType queryCarUser(QueryCarUserRequestType request) {
        return adapter.queryCarUser(request);
    }

    public VendorInfo queryVendorInfoByVendorIdWithCache(Long vendorId) {
        Map<String, String> logTagMap = new HashMap<>();
        logTagMap.put("vendorId", vendorId + "");
        if (Objects.isNull(vendorId) || vendorId <= 0) {
            log.error("CarCommodityVendorServiceProxy.queryVendorInfoByVendorIdWithCache", "vendorId invalid(" + vendorId +")", logTagMap);
            return null;
        }
        QueryVendorRequestType request = new QueryVendorRequestType();
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setVendorId(vendorId);
        request.setBaseRequest(baseRequest);
        QueryVendorResponseType response = adapter.queryVendor(request); //queryVendor 有缓存，有延迟
        log.info("CarCommodityVendorServiceProxy.queryVendorInfoByVendorIdWithCache",
                "request: " + GsonUtils.toJson(request, GsonUtils.NO_SERIALIZE_NULL) + "\r\n\r\n" + "response: " +  GsonUtils.toJson(response, GsonUtils.NO_SERIALIZE_NULL),
                logTagMap);
        if (response != null && response.getBaseResponse().isIsSuccess()) {
            return response.getVendorInfo();
        }
        return null;
    }

    public VendorInfo queryVendorInfoByVendorId(Long vendorId) {
        Map<String, String> logTagMap = new HashMap<>();
        logTagMap.put("vendorId", vendorId + "");
        if (Objects.isNull(vendorId) || vendorId <= 0L) {
            log.error("CarCommodityVendorServiceProxy.queryVendorInfoByVendorId", "vendorId invalid(" + vendorId +")", logTagMap);
            return null;
        }
        QueryVendorListRequestType request = new QueryVendorListRequestType();
        //BaseRequest baseRequest = new BaseRequest();
        //baseRequest.setVendorId(vendorId);
        //request.setBaseRequest(baseRequest);
        request.setVendorIds(Lists.newArrayList(vendorId));
        QueryVendorListResponseType response = adapter.queryVendorList(request); //queryVendorList 无缓存，实时
        log.info("CarCommodityVendorServiceProxy.queryVendorInfoByVendorId",
                "request: " + GsonUtils.toJson(request, GsonUtils.NO_SERIALIZE_NULL) + "\r\n\r\n" + "response: " +  GsonUtils.toJson(response, GsonUtils.NO_SERIALIZE_NULL),
                logTagMap);
        if (Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getList())) {
            return response.getList().stream().filter(Objects::nonNull).findFirst().orElse(null);
        }
        return null;
    }

}