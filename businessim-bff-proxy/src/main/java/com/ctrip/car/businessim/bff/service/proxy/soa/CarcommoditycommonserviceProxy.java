package com.ctrip.car.businessim.bff.service.proxy.soa;

import com.ctrip.car.commodity.common.CarCommodityCommonApiClient;
import com.ctrip.car.commodity.common.service.datasync.StandardProductDto;
import com.ctrip.car.commodity.common.service.recall.QueryStandardProductRequestType;
import com.ctrip.car.commodity.common.service.recall.QueryStandardProductResponseType;
import com.ctrip.car.commodity.common.service.types.CommodityCommonRequest;
import com.ctrip.car.commodity.framework.core.clogging.Logger;
import com.ctrip.car.commodity.framework.core.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Component
public class CarcommoditycommonserviceProxy {

    private static final Logger log = LoggerFactory.getLogger(CarcommoditycommonserviceProxy.class);

    public final CarCommodityCommonApiClient client = CarCommodityCommonApiClient.getInstance();

    public List<StandardProductDto> queryStandardProduct(List<Long> spIds) {
        List<StandardProductDto> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(spIds)) {
            return result;
        }
        QueryStandardProductRequestType requestType = new QueryStandardProductRequestType();
        requestType.setCommonRequest(buildCommodityCommonRequest());
        try {
            if (spIds.size() > 30) {
                List<List<Long>> idList = Lists.partition(spIds, 30);
                for (List<Long> ids : idList) {
                    requestType.setStandardProductIdList(ids);
                    QueryStandardProductResponseType responseType = client.queryStandardProduct(requestType);
                    if (Objects.nonNull(responseType) && CollectionUtils.isNotEmpty(responseType.getStandardProductList())) {
                        result.addAll(responseType.getStandardProductList());
                    }
                }
            } else {
                requestType.setStandardProductIdList(spIds);
                QueryStandardProductResponseType responseType = client.queryStandardProduct(requestType);
                result.addAll(responseType.getStandardProductList());
            }
        } catch (Exception e) {
            log.error("queryStandardProduct", e);
        }
        return result;
    }

    private CommodityCommonRequest buildCommodityCommonRequest() {
        CommodityCommonRequest commodityCommonRequest = new CommodityCommonRequest();
        //commodityCommonRequest.setLocale();
        commodityCommonRequest.setRequestId(UUID.randomUUID().toString());
        //commodityCommonRequest.setOperateUser();
        //commodityCommonRequest.setVendorIds();
        //commodityCommonRequest.setStoreIds();
        //commodityCommonRequest.setClientType();
        return commodityCommonRequest;
    }

}
