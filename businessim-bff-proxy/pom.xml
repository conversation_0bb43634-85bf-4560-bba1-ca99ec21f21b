<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ctrip.car.customer</groupId>
        <artifactId>businessim-bff</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>businessim-bff-proxy</artifactId>
    <version>1.0.0</version>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.car.customer</groupId>
            <artifactId>businessim-bff-core</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>soa-common</artifactId>
                    <groupId>com.ctrip.car.top.framework</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.cat</groupId>
                    <artifactId>cat-prometheus-exporter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- spock 核心包 -->
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- groovy -->
        <dependency> <!-- use a specific Groovy version rather than the one specified by spock-core -->
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
        </dependency>
        <dependency>
            <groupId>org.objenesis</groupId>
            <artifactId>objenesis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework</groupId>
            <artifactId>vi-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib-nodep</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car</groupId>
            <artifactId>coupon-client</artifactId>
            <version>3.2.35</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car.25057</groupId>
            <artifactId>carcommoditycommonservice</artifactId>
        </dependency>

    </dependencies>

</project>