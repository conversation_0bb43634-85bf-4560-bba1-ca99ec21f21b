package com.ctrip.car.businessim.bff.service;

import com.ctrip.car.businessim.bff.service.api.BusinessBffServiceApi;
import com.ctrip.car.commodity.framework.soa.server.CommodityBaijiListener;
import com.ctriposs.baiji.rpc.extensions.springboot.BaijiRegistrationBean;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@SpringBootApplication(scanBasePackages={"com.ctrip.car"})
@EnableAspectJAutoProxy
public class ServiceInitializer extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(ServiceInitializer.class);
    }

    @Configuration
    static class BaijiServletConfiguration extends BaijiRegistrationBean {
        protected BaijiServletConfiguration() {
            super("/*", new CommodityBaijiListener(BusinessBffServiceApi.class));
        }
    }

}