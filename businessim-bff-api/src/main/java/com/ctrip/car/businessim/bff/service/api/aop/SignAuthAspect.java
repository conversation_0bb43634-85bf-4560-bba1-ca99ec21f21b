package com.ctrip.car.businessim.bff.service.api.aop;

import com.ctrip.car.business.auth.enums.BusinessRespExceptionCodeEnum;
import com.ctrip.car.businessim.bff.service.core.util.QConfigUtil;
import com.ctrip.car.commodity.framework.core.clogging.ClogManager;
import com.ctrip.car.commodity.framework.core.clogging.Logger;
import com.ctrip.car.commodity.framework.core.clogging.LoggerFactory;
import com.ctrip.car.commodity.framework.core.context.RequestContextFactory;
import com.ctrip.car.commodity.framework.core.exception.BizException;
import com.ctrip.car.top.KeyValueDTO;
import com.ctrip.flight.mobile.fx.signature.SensitiveFieldSignatureUtil;
import com.ctrip.flight.mobile.fx.signature.exception.SignatureSdkException;
import com.ctrip.flight.mobile.fx.signature.model.SignVerifyResult;
import com.ctrip.flight.mobile.fx.signature.model.SummarySignVerifyResult;
import com.ctrip.flight.mobile.fx.signature.model.SummaryVerifyResultEnum;
import com.ctrip.flight.mobile.fx.signature.model.VerifyResultEnum;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description:
 * @create 2024/11/26 22:01
 */
@Aspect
@Component
public class SignAuthAspect {

    protected static Logger log = LoggerFactory.getLogger(SignAuthAspect.class);

    public static final String TRIP_SIGNATURE = "tripsignature";

    public static final String FLIGHT_SIGNATURE = "flightsignature";

    public static final String BASE_REQUEST = "baseRequest";

    @Pointcut("@annotation(SignAuth)")
    public void executeService() {
    }

    @Around("executeService()")
    public Object signAuth(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();

        Object request = point.getArgs()[0];
        Boolean checkSignature = Boolean.TRUE;
        String signatureSwitch = QConfigUtil.getSignOrDefault("signature.switch", "false"); //验签开关
        try {
            if (signatureSwitch.equals("true")) { // 开关打开
                SignAuth signAuthAnnotation = method.getAnnotation(SignAuth.class);
                String field = signAuthAnnotation.field();
                Object orderIdObject = PropertyUtils.getProperty(request, field);
                Long orderId = 0L;
                if (orderIdObject != null) { // orderId可能不是必有
                    orderId = (Long) orderIdObject;
                }
                List<String> flightSignResults = getSignatureResult(request, FLIGHT_SIGNATURE);
                List<String> tripSignResults = getSignatureResult(request, TRIP_SIGNATURE);
                if (CollectionUtils.isNotEmpty(flightSignResults) && orderId > 0) { //都有值才验证
                    SummarySignVerifyResult summarySignVerifyResult = SensitiveFieldSignatureUtil.verify(request, flightSignResults, tripSignResults);
                    if (summarySignVerifyResult.getSummaryResult() == SummaryVerifyResultEnum.FAILED) {
                        checkSignature = Boolean.FALSE;
                    } else if (CollectionUtils.isNotEmpty(summarySignVerifyResult.getSignVerifyResults())) {
                        SignVerifyResult signVerifyResult = summarySignVerifyResult.getSignVerifyResults().get(0);
                        // 处理验签不成功
                        if (signVerifyResult.getVerifyResult() == VerifyResultEnum.FAILED) {
                            checkSignature = Boolean.FALSE;
                        }
                    }
                }
            }
        } catch (SignatureSdkException e) {
            log.error("signAuth Sdk error", e);
        } catch (Exception e) {
            log.error("signAuth error", e);
        }
        if (BooleanUtils.isFalse(checkSignature)) {
            throw BizException.instance(BusinessRespExceptionCodeEnum.SERVER_100302);
        }
        Object response = point.proceed();
        ClogManager.infoGlobal(request.getClass().getSimpleName().replace("RequestType", ""),
                RequestContextFactory.INSTANCE.getCurrent().getRequestId(),
                request, response);
        return response;

    }

    protected List<String> getSignatureResult(Object request, String name) {
        List<String> empty = new ArrayList<>();
        try {
            com.ctrip.car.top.BaseRequest baseRequest = (com.ctrip.car.top.BaseRequest) PropertyUtils.getProperty(request, BASE_REQUEST);
            if (baseRequest == null) {
                return empty;
            }
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(baseRequest.getExtList())) {
                return baseRequest.getExtList().stream().filter(keyValueDTO -> name.equals(keyValueDTO.getKey()) && keyValueDTO.getValue() != null).map(KeyValueDTO::getValue).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.warn("getSignatureResult fail", e.getMessage());
        }
        return empty;
    }


}

