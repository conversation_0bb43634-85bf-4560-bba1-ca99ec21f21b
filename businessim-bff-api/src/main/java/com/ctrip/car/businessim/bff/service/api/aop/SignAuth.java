package com.ctrip.car.businessim.bff.service.api.aop;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @Description:
 * @create 2024/11/26 22:00
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ ElementType.METHOD })
public @interface SignAuth {

    /*
     * 需校验的字段
     */
    String field() default "orderId";
}
