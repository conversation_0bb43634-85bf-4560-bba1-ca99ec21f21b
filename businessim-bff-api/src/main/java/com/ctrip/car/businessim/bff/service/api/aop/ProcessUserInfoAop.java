package com.ctrip.car.businessim.bff.service.api.aop;

import com.ctrip.car.business.auth.enums.BusinessRespExceptionCodeEnum;
import com.ctrip.car.businessim.bff.service.proxy.soa.CarCommodityVendorServiceProxy;
import com.ctrip.car.commodity.framework.core.clogging.ClogManager;
import com.ctrip.car.commodity.framework.core.context.RequestContext;
import com.ctrip.car.commodity.framework.core.context.RequestContextFactory;
import com.ctrip.car.commodity.framework.core.exception.BizException;
import com.ctrip.car.commodity.vendor.service.contract.dto.BaseRequest;
import com.ctrip.car.commodity.vendor.service.method.QueryCarUserRequestType;
import com.ctrip.car.commodity.vendor.service.method.QueryCarUserResponseType;
import com.ctriposs.baiji.rpc.server.*;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tour.auth.soa.annotation.AuthTourUserList;
import tour.auth.soa.filter.AuthTourAccountFilter;
import tour.auth.soa.session.SessionContext;

import java.lang.reflect.Method;
import java.util.Objects;

@Aspect
@Component
public class ProcessUserInfoAop {

    @Autowired
    private CarCommodityVendorServiceProxy proxy;

    private static final String UID = "uid";
    private static final String VENDOR_ID = "vendorId";
    private static final String VENDOR_ID_LIST = "vendorIdList";
    private static final String STORE_ID = "storeId";
    private static final String SUPPLIER_ID = "supplierId";
    private static final String LOCALE = "locale";
    private static final String BASE_REQUEST = "baseRequest";
    private static final String PC_KEY = "cookie";
    private static final String VBK_LOCALE = "vbk-locale-lang";
    // 表示来源：business、zcbApp代表商户端，operate代表运营端
    private static final String CLIENT_TYPE = "clientType";
    // business代表商户端PC
    private static final String BUSINESS = "business";
    // zcbApp代表商户端App
    private static final String ZCB_APP = "zcbApp";

    @Pointcut("@annotation(ProccessUser)")
    public void executeService() {
    }

    @Around("executeService()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        MethodSignature pointSignature = (MethodSignature) point.getSignature();
        Method method = pointSignature.getMethod();
        Object request = point.getArgs()[0];
        try {
            if (!initSessionInfo(method, request)) {
                Object response = point.proceed();
                ClogManager.infoGlobal(request.getClass().getSimpleName().replace("RequestType", ""),
                        RequestContextFactory.INSTANCE.getCurrent().getRequestId(),
                        request, response);
                return response;
            }
            SessionContext userInfo = preCheckUserInfo();
            handleRequestFromUserInfo(request, userInfo);
        } catch (Exception ex) {
            ClogManager.error("ProcessUserInfoAop error", ex);
            throw ex;
        }
        Object response = point.proceed();
        ClogManager.infoGlobal(request.getClass().getSimpleName().replace("RequestType", ""),
                RequestContextFactory.INSTANCE.getCurrent().getRequestId(),
                request, response);
        return response;
    }

    private boolean initSessionInfo(Method method, Object value) throws Exception {
        try {
            AuthTourUserList authTourUserList = null;
            if (method.isAnnotationPresent(AuthTourUserList.class)) {
                authTourUserList = method.getAnnotation(AuthTourUserList.class);
            }
            if (authTourUserList != null) {
                HttpRequestContext context = HttpRequestContext.getInstance();
                AuthTourAccountFilter filter = new AuthTourAccountFilter();
                filter.init(authTourUserList);
                ClogManager.infoGlobal("HttpRequestContext", RequestContextFactory.INSTANCE.getCurrent().getRequestId(),
                        "request", context.request(), "response", context.response());

                HttpResponseWrapper response = context.response();
                filter.apply(getServiceHost(context), context.request(), response);

                if (response.isResponseSent()) {
                    throw BizException.instance(BusinessRespExceptionCodeEnum.SERVER_100302);
                }
                return true;
            } else {
                return false;
            }
        } catch (BizException e) {
            ClogManager.warn("initSessionInfo biz", e);
            throw e;
        } catch (Exception e) {
            ClogManager.warn("initSessionInfo error", e);
            throw BizException.instance(BusinessRespExceptionCodeEnum.SERVER_401);
        }
    }

    private SessionContext preCheckUserInfo() {
        SessionContext cxt = SessionContext.getInstance();
        if (Objects.isNull(cxt) || Objects.isNull(cxt.getUserInfo()) ||
                (StringUtils.isBlank(cxt.getUserInfo().getBuid()) && StringUtils.isBlank(cxt.getUserInfo().getEid()))) {
            throw BizException.instance(BusinessRespExceptionCodeEnum.SERVER_401);
        }
        return cxt;
    }

    private void handleRequestFromUserInfo(Object request, SessionContext userInfo) throws Exception {
        ClogManager.infoGlobal("handleRequestForUserDataPrivilege", RequestContextFactory.INSTANCE.getCurrent().getRequestId(), "userInfo", userInfo);
        String eId = userInfo.getUserInfo().getEid(); //运营 Eid
        Long userId = userInfo.getUserInfo().getUserId(); //商家 Uid
        QueryCarUserRequestType req = new QueryCarUserRequestType();
        req.setUserId(userId);
        req.setBaseRequest(new BaseRequest());
        //req.set(new MobileRequestHead());
        QueryCarUserResponseType response = proxy.queryCarUser(req);
        if (response == null || !response.getBaseResponse().isIsSuccess()) {
            throw BizException.instance(BusinessRespExceptionCodeEnum.SERVER_200003);
        }
        String clientType = BUSINESS;
        if(StringUtils.equals(userInfo.getFrom(), "zcb_app")){
            clientType = ZCB_APP;
        }
        processUserInfo(eId, response, request, clientType);
    }

    private ServiceHost getServiceHost(HttpRequestContext context) {
        return BaijiServletContext.INSTANCE.getServiceHost(context.request().servicePath());
    }

    private String getCookie( HttpRequestWrapper requestWrapper) {
        String cookie = "";
        if (Objects.nonNull(requestWrapper.getHeader(PC_KEY))) {
            cookie = requestWrapper.getHeader(PC_KEY);
        }
        return cookie;
    }

    private String getLocale(String clientType) {
        String locale = "zh-CN";
        HttpRequestWrapper requestWrapper= HttpRequestContext.getInstance().request();
        if(clientType.equals(ZCB_APP)){
            String localeHeader =requestWrapper.getHeader(VBK_LOCALE);
            if(!StringUtils.isEmpty(localeHeader)){
                return localeHeader;
            }
        } else {
            String cookie = getCookie(requestWrapper);
            if (StringUtils.isBlank(cookie)) {
                return locale;
            }
            String cookieArray[] = cookie.split(";");
            for (int i = 0; i < cookieArray.length; i++) {
                String cookieKV[] = cookieArray[i].split("=");
                if (cookieKV.length > 1 && cookieKV[0].trim().equals(VBK_LOCALE) && StringUtils.isNotBlank(cookieKV[1])) {
                    return cookieKV[1].trim();
                }
            }
        }
        return locale;
    }

    private void processUserInfo(String employeeId, QueryCarUserResponseType response, Object request,String clientType) throws Exception {
        if (PropertyUtils.isReadable(request, BASE_REQUEST)) {
            com.ctrip.car.top.BaseRequest baseRequest = (com.ctrip.car.top.BaseRequest) PropertyUtils.getProperty(request, BASE_REQUEST);
            if (baseRequest == null) {
                baseRequest = new com.ctrip.car.top.BaseRequest();
                PropertyUtils.setProperty(request, BASE_REQUEST, baseRequest);
            }
            if (StringUtils.isNotBlank(employeeId)) {
                PropertyUtils.setProperty(baseRequest, "proxyUid", employeeId);
            }
            if (Objects.nonNull(response.getBasicUserInfo())) {
                if (response.getBasicUserInfo().getVendorId() != null) {
                    PropertyUtils.setProperty(baseRequest, VENDOR_ID, response.getBasicUserInfo().getVendorId());
                }
                if (response.getBasicUserInfo().getAdminVendorId() != null) {
                    PropertyUtils.setProperty(baseRequest, VENDOR_ID_LIST, response.getBasicUserInfo().getAdminVendorId());
                }
                if (CollectionUtils.isNotEmpty(response.getBasicUserInfo().getStoreIds())) {
                    PropertyUtils.setProperty(baseRequest, STORE_ID, response.getBasicUserInfo().getStoreIds());
                }
                if (response.getBasicUserInfo().getSupplierId() != null) {
                    PropertyUtils.setProperty(baseRequest, SUPPLIER_ID, response.getBasicUserInfo().getSupplierId());
                }
                if (response.getBasicUserInfo().getUserId() != null) {
                    PropertyUtils.setProperty(baseRequest, UID, response.getBasicUserInfo().getUserId().toString());
                }
            }
            PropertyUtils.setProperty(baseRequest, LOCALE, getLocale(clientType));
            PropertyUtils.setProperty(baseRequest, CLIENT_TYPE, clientType);
            RequestContext requestContext = RequestContextFactory.INSTANCE.getCurrent();
            requestContext.setLocale(getLocale(clientType));
            ClogManager.infoGlobal("processUserInfo", RequestContextFactory.INSTANCE.getCurrent().getRequestId(), request.getClass().getSimpleName(), request);
        }
    }

}
