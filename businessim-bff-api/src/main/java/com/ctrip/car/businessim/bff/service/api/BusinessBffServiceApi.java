package com.ctrip.car.businessim.bff.service.api;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.Auths;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.api.aop.SignAuth;
import com.ctrip.car.businessim.bff.service.dto.seo.*;
import com.ctrip.car.businessim.bff.service.entity.*;
import com.ctrip.car.businessim.bff.service.svc.*;
import com.ctrip.car.businessim.bff.service.svc.activity.*;
import com.ctrip.car.businessim.bff.service.svc.seo.QuerySeoPageConfigService;
import com.ctrip.car.businessim.bff.service.svc.seo.SaveSeoPageConfigService;
import com.ctrip.car.businessim.bff.service.svc.seo.UpdateSeoPageConfigStatusService;
import com.ctriposs.baiji.rpc.common.types.CheckHealthRequestType;
import com.ctriposs.baiji.rpc.common.types.CheckHealthResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tour.auth.soa.annotation.AuthTourUser;
import tour.auth.soa.annotation.AuthTourUserList;
import com.ctrip.car.businessim.bff.service.api.aop.ProccessUser;
import tour.auth.soa.model.AuthType;

@Component
public class BusinessBffServiceApi implements BusinessBffService {

    @Override
    public CheckHealthResponseType checkHealth(CheckHealthRequestType request) throws Exception {
        return new CheckHealthResponseType();
    }

    @Autowired
    private GetIMGrayStatusService getIMGrayStatusService;

    @Auths(value = {
            @Auth(platformType = PlatformTypeEnum.ZCBAPP),
            @Auth(platformType = PlatformTypeEnum.BUSINESS)
    })
    @Override
    public GetIMGrayStatusResponseType getIMGrayStatus(GetIMGrayStatusRequestType request) throws Exception {
        return getIMGrayStatusService.toResponse(request);
    }

    @Autowired
    private ListOrderForIMCustomizeAreaService listOrderForIMCustomizeAreaService;


    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"Carrental.zcbonline.customerserver"})
    public ListOrderForIMCustomizeAreaResponseType listOrderForIMCustomizeArea(ListOrderForIMCustomizeAreaRequestType request) throws Exception {
        return listOrderForIMCustomizeAreaService.toResponse(request);
    }

    @Autowired
    private ListDropdownStoreService listDropdownStoreService;

    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"Carrental.zcbonline.customerserver"})
    public ListDropdownStoreResponseType listDropdownStore(ListDropdownStoreRequestType request) throws Exception {
        return listDropdownStoreService.toResponse(request);
    }

    @Autowired
    private GetStoreBusinessTimeService getStoreBusinessTimeService;

    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"Carrental.zcbonline.customerserver"})
    public GetStoreBusinessTimeResponseType getStoreBusinessTime(GetStoreBusinessTimeRequestType request) throws Exception {
        return getStoreBusinessTimeService.toResponse(request);
    }

    @Autowired
    private UpdateStoreBusinessTimeService updateStoreBusinessTimeService;

    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"Carrental.zcbonline.customerserver"})
    public UpdateStoreBusinessTimeResponseType updateStoreBusinessTime(UpdateStoreBusinessTimeRequestType request) throws Exception {
        return updateStoreBusinessTimeService.toResponse(request);
    }

    @Autowired
    private ListBusinessIMAgentService listBusinessIMAgentService;

    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"Carrental.zcbonline.customerserver"})
    public ListBusinessIMAgentResponseType listBusinessIMAgent(ListBusinessIMAgentRequestType request) throws Exception {
        return listBusinessIMAgentService.toResponse(request);
    }

    @Autowired
    private UpdateBusinessIMAgentService updateBusinessIMAgentService;

    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"Carrental.zcbonline.customerserver"})
    public UpdateBusinessIMAgentResponseType updateBusinessIMAgent(UpdateBusinessIMAgentRequestType request) throws Exception {
        return updateBusinessIMAgentService.toResponse(request);
    }

    @Autowired
    private AddIMAgentService addIMAgentService;

    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"Carrental.zcbonline.customerserver"})
    public AddIMAgentResponseType addIMAgent(AddIMAgentRequestType request) throws Exception {
        return addIMAgentService.toResponse(request);
    }

    @Autowired
    private ListDropdownUserService listDropdownUserService;

    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"Carrental.zcbonline.customerserver"})
    public ListDropdownUserResponseType listDropdownUser(ListDropdownUserRequestType request) throws Exception {
        return listDropdownUserService.toResponse(request);
    }

    @Autowired
    private ListAgentAllocateRuleService listAgentAllocateRuleService;

    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"Carrental.zcbonline.customerserver"})
    public ListAgentAllocateRuleResponseType listAgentAllocateRule(ListAgentAllocateRuleRequestType request) throws Exception {
        return listAgentAllocateRuleService.toResponse(request);
    }

    @Autowired
    private UpdateAgentAllocateRuleService updateAgentAllocateRuleService;

    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"Carrental.zcbonline.customerserver"})
    public UpdateAgentAllocateRuleResponseType updateAgentAllocateRule(UpdateAgentAllocateRuleRequestType request) throws Exception {
        return updateAgentAllocateRuleService.toResponse(request);
    }

    @Autowired
    private GetB2OLinkAndParameterService getB2OLinkAndParameterService;

    @Override
    @SignAuth
    @Auths(value = {
            @Auth(platformType = PlatformTypeEnum.ZCBAPP),
            @Auth(platformType = PlatformTypeEnum.BUSINESS)
    })
    public GetB2OLinkAndParameterResponseType getB2OLinkAndParameter(GetB2OLinkAndParameterRequestType request) throws Exception {
        return getB2OLinkAndParameterService.toResponse(request);
    }

    @Autowired
    private GetB2OAllowAccessService getB2OAllowAccessService;

    @Override
    @Auths(value = {
            @Auth(platformType = PlatformTypeEnum.ZCBAPP),
            @Auth(platformType = PlatformTypeEnum.BUSINESS)
    })
    public GetB2OAllowAccessResponseType getB2OAllowAccess(GetB2OAllowAccessRequestType request) throws Exception {
        return getB2OAllowAccessService.toResponse(request);
    }

    @Autowired
    private ListServiceDataIMAgentService listServiceDataIMAgentService;

    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"FP22165"})
    public ListServiceDataIMAgentResponseType listServiceDataIMAgent(ListServiceDataIMAgentRequestType request) throws Exception {
        return listServiceDataIMAgentService.toResponse(request);
    }

    @Autowired
    private DownloadIVRCallService downloadIVRCallService;

    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"FP22165"})
    public DownloadIVRCallResponseType downloadIVRCall(DownloadIVRCallRequestType request) throws Exception {
        return downloadIVRCallService.toResponse(request);
    }

    @Autowired
    private GetIVRCallReportService getIVRCallReportService;

    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"FP22165"})
    public GetIVRCallReportResponseType getIVRCallReport(GetIVRCallReportRequestType request) throws Exception {
        return getIVRCallReportService.toResponse(request);
    }

    @Autowired
    private GetIMSessionReportService getIMSessionReportService;

    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"FP22165"})
    public GetIMSessionReportResponseType getIMSessionReport(GetIMSessionReportRequestType request) throws Exception {
        return getIMSessionReportService.toResponse(request);
    }

    @Autowired
    private DownloadIMSessionService downloadIMSessionService;

    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"FP22165"})
    public DownloadIMSessionResponseType downloadIMSession(DownloadIMSessionRequestType request) throws Exception {
        return downloadIMSessionService.toResponse(request);
    }

    @Autowired
    private GetOrderDetailLinkService getOrderDetailLinkService;

    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"FP22165"})
    public GetOrderDetailLinkResponseType getOrderDetailLink(GetOrderDetailLinkRequestType request) throws Exception {
        return getOrderDetailLinkService.toResponse(request);
    }

    @Autowired
    private GetDecryptDataService getDecryptDataService;

    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"FP22165"})
    public GetDecryptDataResponseType getDecryptData(GetDecryptDataRequestType request) throws Exception {
        return getDecryptDataService.toResponse(request);
    }

    @Autowired
    private ListServiceDataDropdownStoreService listServiceDataDropdownStoreService;

    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"FP22165"})
    public ListServiceDataDropdownStoreResponseType listServiceDataDropdownStore(ListServiceDataDropdownStoreRequestType request) throws Exception {
        return listServiceDataDropdownStoreService.toResponse(request);
    }
    @Autowired
    GetBusinessNumberByCallIdService getBusinessNumberByCallIdService;

    @Override
    @Auth(platformType = PlatformTypeEnum.BUSINESS, permissionList = {"FP22165"})
    public GetBusinessNumberByCallIdResponseType getBusinessNumberByCallId(GetBusinessNumberByCallIdRequestType request) throws Exception {
        return getBusinessNumberByCallIdService.toResponse(request);
    }

    @Override
    public QueryUserInfoResponseType queryUserInfo(QueryUserInfoRequestType request) throws Exception {
        return null;
    }

    @Autowired
    private SupplierQueryActivityTemplateListService supplierQueryActivityTemplateListService;

    @ProccessUser
    @AuthTourUserList(userList = {
            @AuthTourUser(from = "", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "commodity_business", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "internalBusiness", authType = AuthType.EID_VBK, permissionList = {})
    })
    @Override
    public SupplierQueryActivityTemplateListResponseType supplierQueryActivityTemplateList(SupplierQueryActivityTemplateListRequestType request) throws Exception {
        return supplierQueryActivityTemplateListService.service(request);
    }

    @Autowired
    private SupplierQueryActivityHistoryListService supplierQueryActivityHistoryListService;

    @ProccessUser
    @AuthTourUserList(userList = {
            @AuthTourUser(from = "", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "commodity_business", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "internalBusiness", authType = AuthType.EID_VBK, permissionList = {})
    })
    @Override
    public SupplierQueryActivityHistoryListResponseType supplierQueryActivityHistoryList(SupplierQueryActivityHistoryListRequestType request) throws Exception {
        return supplierQueryActivityHistoryListService.service(request);
    }

    @Autowired
    private SupplierQueryActivityDetailService supplierQueryActivityDetailService;

    @ProccessUser
    @AuthTourUserList(userList = {
            @AuthTourUser(from = "", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "commodity_business", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "internalBusiness", authType = AuthType.EID_VBK, permissionList = {})
    })
    @Override
    public SupplierQueryActivityDetailResponseType supplierQueryActivityDetail(SupplierQueryActivityDetailRequestType request) throws Exception {
        return supplierQueryActivityDetailService.service(request);
    }

    @Autowired
    private SupplierJoinActivityService supplierJoinActivityService;

    @ProccessUser
    @AuthTourUserList(userList = {
            @AuthTourUser(from = "", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "commodity_business", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "internalBusiness", authType = AuthType.EID_VBK, permissionList = {})
    })
    @Override
    public SupplierJoinActivityResponseType supplierJoinActivity(SupplierJoinActivityRequestType request) throws Exception {
        return supplierJoinActivityService.service(request);
    }

    @Autowired
    private SupplierModifyActivityService supplierModifyActivityService;

    @ProccessUser
    @AuthTourUserList(userList = {
            @AuthTourUser(from = "", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "commodity_business", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "internalBusiness", authType = AuthType.EID_VBK, permissionList = {})
    })
    @Override
    public SupplierModifyActivityResponseType supplierModifyActivity(SupplierModifyActivityRequestType request) throws Exception {
        return supplierModifyActivityService.service(request);
    }

    @Autowired
    private SupplierQuitActivityService supplierQuitActivityService;

    @ProccessUser
    @AuthTourUserList(userList = {
            @AuthTourUser(from = "", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "commodity_business", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "internalBusiness", authType = AuthType.EID_VBK, permissionList = {})
    })
    @Override
    public SupplierQuitActivityResponseType supplierQuitActivity(SupplierQuitActivityRequestType request) throws Exception {
        return supplierQuitActivityService.service(request);
    }

    @Autowired
    private GetB2CNewLinkService getB2CNewLinkService;

    @Override
    @Auths(value = {
            @Auth(platformType = PlatformTypeEnum.ZCBAPP),
            @Auth(platformType = PlatformTypeEnum.BUSINESS)
    })
    public GetB2CNewLinkResponseType getB2CNewLink(GetB2CNewLinkRequestType getB2CNewLinkRequestType) throws Exception {
        return getB2CNewLinkService.toResponse(getB2CNewLinkRequestType);
    }

    @Autowired
    private SupplierQueryVendorInfoService supplierQueryVendorInfoService;

    @ProccessUser
    @AuthTourUserList(userList = {
            @AuthTourUser(from = "", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "commodity_business", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "internalBusiness", authType = AuthType.EID_VBK, permissionList = {})
    })
    @Override
    public SupplierQueryVendorInfoResponseType supplierQueryVendorInfo(SupplierQueryVendorInfoRequestType request) throws Exception {
        return supplierQueryVendorInfoService.service(request);
    }

    @Autowired
    private SupplierQueryOpenCityInfoService supplierQueryOpenCityInfoService;

    @ProccessUser
    @AuthTourUserList(userList = {
            @AuthTourUser(from = "", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "commodity_business", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "internalBusiness", authType = AuthType.EID_VBK, permissionList = {})
    })
    @Override
    public SupplierQueryOpenCityInfoResponseType supplierQueryOpenCityInfo(SupplierQueryOpenCityInfoRequestType request) throws Exception {
        return supplierQueryOpenCityInfoService.service(request);
    }

    @Autowired
    private SupplierQueryProductInfoService supplierQueryProductInfoService;

    @ProccessUser
    @AuthTourUserList(userList = {
            @AuthTourUser(from = "", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "commodity_business", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "internalBusiness", authType = AuthType.EID_VBK, permissionList = {})
    })
    @Override
    public SupplierQueryProductInfoResponseType supplierQueryProductInfo(SupplierQueryProductInfoRequestType request) throws Exception {
        return supplierQueryProductInfoService.service(request);
    }

    @Autowired
    private SupplierQueryLabelInfoService supplierQueryLabelInfoService;

    @ProccessUser
    @AuthTourUserList(userList = {
            @AuthTourUser(from = "", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "commodity_business", authType = AuthType.BUID_VBK_BTICKET, permissionList = {}),
            @AuthTourUser(from = "internalBusiness", authType = AuthType.EID_VBK, permissionList = {})
    })
    @Override
    public SupplierQueryLabelInfoResponseType supplierQueryLabelInfo(SupplierQueryLabelInfoRequestType request) throws Exception {
        return supplierQueryLabelInfoService.service(request);
    }

    @Autowired
    private CheckShopService checkShopService;

    @Auth(platformType = PlatformTypeEnum.ZCBAPP)
    @Override
    public CheckShopBffResponseType checkShop(CheckShopBffRequestType request) throws Exception {
        return checkShopService.toResponse(request);
    }

    @Autowired
    private ShopOpenService shopOpenService;

    @Auth(platformType = PlatformTypeEnum.ZCBAPP)
    @Override
    public ShopOpenBffResponseType shopOpen(ShopOpenBffRequestType request) throws Exception {
        return shopOpenService.toResponse(request);
    }

    @Autowired
    private QueryStoreListService queryStoreListService;

    @Auth(platformType = PlatformTypeEnum.ZCBAPP)
    @Override
    public QueryStoreListBffResponseType queryStoreList(QueryStoreListBffRequestType request) throws Exception {
        return queryStoreListService.toResponse(request);
    }

    @Autowired
    private QueryStoreQRCodeListService queryStoreQRCodeListService;

    @Auth(platformType = PlatformTypeEnum.ZCBAPP)
    @Override
    public QueryStoreQRCodeListBffResponseType queryStoreQRCodeList(QueryStoreQRCodeListBffRequestType request) throws Exception {
        return queryStoreQRCodeListService.toResponse(request);
    }

    @Autowired
    private DeleteQRCodeService deleteQRCodeService;

    @Auth(platformType = PlatformTypeEnum.ZCBAPP)
    @Override
    public DeleteQRCodeBffResponseType deleteQRCode(DeleteQRCodeBffRequestType request) throws Exception {
        return deleteQRCodeService.toResponse(request);
    }

    @Autowired
    private AddQRCodeService addQRCodeService;

    @Auth(platformType = PlatformTypeEnum.ZCBAPP)
    @Override
    public AddQRCodeBffResponseType addQRCode(AddQRCodeBffRequestType request) throws Exception {
        return addQRCodeService.toResponse(request);
    }

    @Autowired
    private QueryQRCodeDetailService queryQRCodeDetailService;

    @Auth(platformType = PlatformTypeEnum.ZCBAPP)
    @Override
    public QueryQRCodeDetailBffResponseType queryQRCodeDetail(QueryQRCodeDetailBffRequestType request) throws Exception {
        return queryQRCodeDetailService.toResponse(request);
    }

    @Autowired
    private QueryPromoterListService queryPromoterListService;

    @Auth(platformType = PlatformTypeEnum.ZCBAPP)
    @Override
    public QueryPromoterListBffResponseType queryPromoterList(QueryPromoterListBffRequestType request) throws Exception {
        return queryPromoterListService.toResponse(request);
    }

    @Autowired
    private AddPromoterService addPromoterService;

    @Auth(platformType = PlatformTypeEnum.ZCBAPP)
    @Override
    public AddPromoterBffResponseType addPromoter(AddPromoterBffRequestType request) throws Exception {
        return addPromoterService.toResponse(request);
    }

    @Autowired
    private QueryQRCodeService queryQRCodeService;

    @Override
    public QueryQRCodeBffResponseType queryQRCode(QueryQRCodeBffRequestType request) throws Exception {
        return queryQRCodeService.toResponse(request);
    }

    @Autowired
    private GetB2ONewLinkAndParameterService getB2ONewLinkAndParameterService;

    @Auths(value = {
            @Auth(platformType = PlatformTypeEnum.ZCBAPP),
            @Auth(platformType = PlatformTypeEnum.BUSINESS)
    })
    @Override
    public GetB2ONewLinkAndParameterResponseType getB2ONewLinkAndParameter(GetB2ONewLinkAndParameterRequestType getB2ONewLinkAndParameterRequestType) throws Exception {
        return getB2ONewLinkAndParameterService.toResponse(getB2ONewLinkAndParameterRequestType);
    }

    @Autowired
    private GetB2OImGrayStatusService getB2OImGrayStatusService;

    @Auths(value = {
            @Auth(platformType = PlatformTypeEnum.ZCBAPP),
            @Auth(platformType = PlatformTypeEnum.BUSINESS)
    })
    @Override
    public GetB2OImGrayStatusResponseType getB2OImGrayStatus(GetB2OImGrayStatusRequestType getB2OImGrayStatusRequestType) throws Exception {
        return getB2OImGrayStatusService.toResponse(getB2OImGrayStatusRequestType);
    }

    @Autowired
    private QueryLastSessionLinkService queryLastSessionLinkService;

    @Auths(value = {
            @Auth(platformType = PlatformTypeEnum.ZCBAPP),
            @Auth(platformType = PlatformTypeEnum.BUSINESS)
    })
    @Override
    public QueryLastSessionLinkResponseType queryLastSessionLink(QueryLastSessionLinkRequestType queryLastSessionLinkRequestType) throws Exception {
        return queryLastSessionLinkService.toResponse(queryLastSessionLinkRequestType);
    }

    @Autowired
    private GetConsultRecordsLinkService getConsultRecordsLinkService;

    @Auth(platformType = PlatformTypeEnum.BUSINESS)
    @Override
    public GetConsultRecordsLinkResponseType getConsultRecordsLink(GetConsultRecordsLinkRequestType getConsultRecordsLinkRequestType) throws Exception {
        return getConsultRecordsLinkService.toResponse(getConsultRecordsLinkRequestType);
    }

    @Autowired
    private QuerySeoPageConfigService querySeoPageConfigService;

    @Override
    public QuerySeoPageConfigBffResponseType querySeoPageConfig(QuerySeoPageConfigBffRequestType querySeoPageConfigBffRequestType) throws Exception {
        return querySeoPageConfigService.toResponse(querySeoPageConfigBffRequestType);
    }

    @Autowired
    private SaveSeoPageConfigService saveSeoPageConfigService;

    @Override
    public SaveSeoPageConfigBffResponseType saveSeoPageConfig(SaveSeoPageConfigBffRequestType saveSeoPageConfigBffRequestType) throws Exception {
        return saveSeoPageConfigService.toResponse(saveSeoPageConfigBffRequestType);
    }

    @Autowired
    private UpdateSeoPageConfigStatusService updateSeoPageConfigStatusService;

    @Override
    public UpdateSeoPageConfigStatusBffResponseType updateSeoPageConfigStatus(UpdateSeoPageConfigStatusBffRequestType updateSeoPageConfigStatusBffRequestType) throws Exception {
        return updateSeoPageConfigStatusService.toResponse(updateSeoPageConfigStatusBffRequestType);
    }
}
