<dal name="com.ctrip.car.isd.pms.redtful">
    <databaseSets>

    </databaseSets>
    <LogListener>
        <logger>com.ctrip.platform.dal.sql.logging.CtripDalLogger</logger>
    </LogListener>
    <ConnectionLocator>
        <settings>
            <dataSourceConfigureProvider>com.ctrip.datasource.titan.TitanProvider</dataSourceConfigureProvider>
        </settings>
    </ConnectionLocator>
    <TaskFactory>
        <factory>com.ctrip.platform.dal.dao.CtripTaskFactory</factory>
    </TaskFactory>
</dal>