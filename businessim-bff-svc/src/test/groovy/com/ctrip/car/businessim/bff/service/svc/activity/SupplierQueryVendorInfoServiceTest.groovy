package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.basebiz.offlinebase.utils.QconfigUtils
import com.ctrip.car.businessim.bff.service.entity.SupplierQueryVendorInfoRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.activity.DataSourceInfo
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryVendorInfoByUserIdRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryVendorInfoByUserIdResponseType
import com.ctrip.car.top.BaseRequest
import com.google.common.collect.Lists
import org.apache.commons.collections4.CollectionUtils

import spock.lang.Specification

class SupplierQueryVendorInfoServiceTest extends Specification {

    def proxy = Mock(CustomerBusinessIMServiceProxy.class)

    def testInstance = new SupplierQueryVendorInfoService(proxy: proxy)

    def "test_validRequest"() {
        setup: ""

        when: ""
        def result1 = testInstance.validRequest(new SupplierQueryVendorInfoRequestType())
        def result2 = testInstance.validRequest(new SupplierQueryVendorInfoRequestType(baseRequest: new BaseRequest()))
        def result3 = testInstance.validRequest(new SupplierQueryVendorInfoRequestType(baseRequest: new BaseRequest(uid: "10000")))

        then: ""
        Objects.nonNull(result1)
        Objects.equals(result1.getBaseResponse().getCode(), "40001")
        Objects.equals(result1.getBaseResponse().getMessage(), "baseRequest is null; ")

        Objects.nonNull(result2)
        Objects.equals(result2.getBaseResponse().getCode(), "40001")
        Objects.equals(result2.getBaseResponse().getMessage(), "uid is null; ")

        Objects.isNull(result3)
    }

    def "test_business"() {
        setup: ""

        and: ""
        proxy.queryVendorInfoByUserId(_ as QueryVendorInfoByUserIdRequestType)\
        >> null\
        >> new QueryVendorInfoByUserIdResponseType(infos: new ArrayList<DataSourceInfo>())\
        >> new QueryVendorInfoByUserIdResponseType(infos: Lists.newArrayList(new DataSourceInfo(dataKey: "1", displayName: "name")))

        when: ""
        def result1 = testInstance.business(new SupplierQueryVendorInfoRequestType(baseRequest: new BaseRequest(uid: "A")))
        def result2 = testInstance.business(new SupplierQueryVendorInfoRequestType(baseRequest: new BaseRequest(uid: "1")))
        def result3 = testInstance.business(new SupplierQueryVendorInfoRequestType(baseRequest: new BaseRequest(uid: "1")))

        then: ""
        Objects.nonNull(result1)
        CollectionUtils.isEmpty(result1.getVendorInfoList())
        Objects.equals(result1.getBaseResponse().getCode(), "60000")

        Objects.nonNull(result2)

        Objects.nonNull(result3)
        Objects.equals(result3.getVendorInfoList().get(0).getVendorId(), 1L)
        Objects.equals(result3.getVendorInfoList().get(0).getVendorName(), "1-name")
    }

}
