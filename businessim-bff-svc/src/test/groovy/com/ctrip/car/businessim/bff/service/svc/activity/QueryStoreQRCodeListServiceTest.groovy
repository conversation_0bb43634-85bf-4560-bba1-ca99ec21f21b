package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.car.businessim.bff.service.core.mapper.activity.QueryStoreQRCodeListMapper
import com.ctrip.car.businessim.bff.service.core.mapper.activity.ShopOpenMapper
import com.ctrip.car.businessim.bff.service.dto.activity.ShopQrcodeInfo
import com.ctrip.car.businessim.bff.service.entity.QueryStoreQRCodeListBffRequestType
import com.ctrip.car.businessim.bff.service.entity.QueryStoreQRCodeListBffResponseType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.activity.ShopQrcodeDto
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStoreQRCodeListRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStoreQRCodeListResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class QueryStoreQRCodeListServiceTest extends Specification {


    def mapper = Mock(QueryStoreQRCodeListMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def queryStoreQRCodeListService = new QueryStoreQRCodeListService(
            proxy: proxy,
            mapper: mapper)

    def "test convert Request"() {
        given:
         mapper.from(_ as QueryStoreQRCodeListBffRequestType)>>(new QueryStoreQRCodeListRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, 0, 0))

        when:
        QueryStoreQRCodeListRequestType result = queryStoreQRCodeListService.convertRequest(new QueryStoreQRCodeListBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, 0, 0))

        then:
        result == new QueryStoreQRCodeListRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, 0, 0)
    }

    def "test execute SOA"() {
        given:
         (proxy.queryStoreQRCodeList(_ as QueryStoreQRCodeListRequestType))>>(new QueryStoreQRCodeListResponseType([new ShopQrcodeDto(1l, "qrCode", 1l, "promoterName", "expireTime", Boolean.TRUE, "remark", Boolean.TRUE)], new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()))

        when:
        QueryStoreQRCodeListResponseType result = queryStoreQRCodeListService.executeSOA(new QueryStoreQRCodeListRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, 0, 0))

        then:
        result == new QueryStoreQRCodeListResponseType([new ShopQrcodeDto(1l, "qrCode", 1l, "promoterName", "expireTime", Boolean.TRUE, "remark", Boolean.TRUE)], new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType())
    }

    def "test convert Response"() {
        given:
         (mapper.to(_ as QueryStoreQRCodeListResponseType))>>(new QueryStoreQRCodeListBffResponseType([new ShopQrcodeInfo(1l, "qrCode", 1l, "promoterName", "expireTime", Boolean.TRUE, "remark", Boolean.TRUE)], new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()))

        when:
        QueryStoreQRCodeListBffResponseType result = queryStoreQRCodeListService.convertResponse(new QueryStoreQRCodeListResponseType([new ShopQrcodeDto(1l, "qrCode", 1l, "promoterName", "expireTime", Boolean.TRUE, "remark", Boolean.TRUE)], new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()), new QueryStoreQRCodeListRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, 0, 0), new QueryStoreQRCodeListBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, 0, 0))

        then:
        result == new QueryStoreQRCodeListBffResponseType([new ShopQrcodeInfo(1l, "qrCode", 1l, "promoterName", "expireTime", Boolean.TRUE, "remark", Boolean.TRUE)], new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType())
    }


}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme