package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.car.businessim.bff.service.core.mapper.activity.DeleteQRCodeMapper
import com.ctrip.car.businessim.bff.service.core.mapper.activity.QueryPromoterListMapper
import com.ctrip.car.businessim.bff.service.entity.DeleteQRCodeBffRequestType
import com.ctrip.car.businessim.bff.service.entity.DeleteQRCodeBffResponseType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.activity.DeleteQRCodeRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.DeleteQRCodeResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class DeleteQRCodeServiceTest extends Specification {


    def mapper = Mock(DeleteQRCodeMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def deleteQRCodeService = new DeleteQRCodeService(
            proxy: proxy,
            mapper: mapper)
    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        (mapper.from(_ as DeleteQRCodeBffRequestType))>>(new DeleteQRCodeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l))

        expect:
        deleteQRCodeService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                     || expectedResult
        new DeleteQRCodeBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) || new DeleteQRCodeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l)
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        (proxy.deleteQRCode(_ as DeleteQRCodeRequestType))>>(new DeleteQRCodeResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()))

        expect:
        deleteQRCodeService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                  || expectedResult
        new DeleteQRCodeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) || new DeleteQRCodeResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType())
    }

    @Unroll
    def "convert Response where response=#response and var3=#var3 and var2=#var2 then expect: #expectedResult"() {
        given:
        (mapper.to(_ as DeleteQRCodeResponseType))>>(new DeleteQRCodeBffResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()))

        expect:
        deleteQRCodeService.convertResponse(response, var2, var3) == expectedResult

        where:
        response                                                                                                       | var3                                                                                                                                                                                                                                        | var2                                                                                                                                                                                                                                     || expectedResult
        new DeleteQRCodeResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()) | new DeleteQRCodeBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) | new DeleteQRCodeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) || new DeleteQRCodeBffResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType())
    }


}

