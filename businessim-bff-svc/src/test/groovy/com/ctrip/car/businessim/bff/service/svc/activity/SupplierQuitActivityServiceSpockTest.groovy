package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.car.businessim.bff.service.entity.SupplierQuitActivityRequestType
import com.ctrip.car.businessim.bff.service.entity.SupplierQuitActivityResponseType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.activity.UpdateActivityStatusRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.UpdateActivityStatusResponseType
import com.ctrip.car.top.BaseRequest
import org.junit.Assert
import org.mockito.Mockito

import spock.lang.Specification
import spock.lang.Unroll

import java.lang.reflect.Method

class SupplierQuitActivityServiceSpockTest extends Specification {

    def proxy = Mock(CustomerBusinessIMServiceProxy.class)

    private testInstance = new SupplierQuitActivityService(proxy: proxy)

    def "test_validRequest"() {
        setup: ""

        when: ""
        SupplierQuitActivityService var10000 = this.testInstance;
        Method validRequestMethod = var10000.getClass().getDeclaredMethod("validRequest", SupplierQuitActivityRequestType.class);
        validRequestMethod.setAccessible(true);
        SupplierQuitActivityRequestType requestType = new SupplierQuitActivityRequestType();
        SupplierQuitActivityResponseType result1 = (SupplierQuitActivityResponseType) validRequestMethod.invoke(this.testInstance, requestType);

        //==============================================================================================================
        BaseRequest baseRequest = new BaseRequest();
        requestType.setBaseRequest(baseRequest);
        requestType.setActivityId(10086L);
        SupplierQuitActivityResponseType result2 = (SupplierQuitActivityResponseType) validRequestMethod.invoke(this.testInstance, requestType);

        //==============================================================================================================
        baseRequest.setUid("10001");
        SupplierQuitActivityResponseType result3 = (SupplierQuitActivityResponseType) validRequestMethod.invoke(this.testInstance, requestType);

        then: ""
        Assert.assertNotNull(result1);
        Assert.assertEquals("40001", result1.getBaseResponse().getCode())
        Assert.assertEquals("baseRequest is null; activityId is null; ", result1.getBaseResponse().getMessage())

        Assert.assertNotNull(result2);
        Assert.assertEquals("40001", result2.getBaseResponse().getCode())
        Assert.assertEquals("uid is null; ", result2.getBaseResponse().getMessage())

        Assert.assertNull(result3);
    }

    def "test_buildSoaRequest"() {
        setup: ""

        when: ""
        SupplierQuitActivityService var10000 = this.testInstance;
        Method buildSoaRequestMethod = var10000.getClass().getDeclaredMethod("buildSoaRequest", SupplierQuitActivityRequestType.class);
        buildSoaRequestMethod.setAccessible(true);
        SupplierQuitActivityRequestType requestType = new SupplierQuitActivityRequestType();
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setUid("UID");
        requestType.setBaseRequest(baseRequest);
        UpdateActivityStatusRequestType result1 = (UpdateActivityStatusRequestType) buildSoaRequestMethod.invoke(this.testInstance, requestType);

        //==============================================================================================================
        baseRequest.setUid("10086");
        UpdateActivityStatusRequestType result2 = (UpdateActivityStatusRequestType) buildSoaRequestMethod.invoke(this.testInstance, requestType);

        then: ""
        Assert.assertNotNull(result1);
        Assert.assertEquals(5, result1.getStatus())
        Assert.assertNull(result1.getUserId())

        Assert.assertNotNull(result2);
        Assert.assertEquals(10086L, result2.getUserId())
    }

    def "test_buildBffResponse"() {
        setup: ""

        when: ""
        SupplierQuitActivityService var10000 = this.testInstance;
        Method buildBffResponseMethod = var10000.getClass().getDeclaredMethod("buildBffResponse", UpdateActivityStatusResponseType.class);
        buildBffResponseMethod.setAccessible(true);
        UpdateActivityStatusResponseType responseType = new UpdateActivityStatusResponseType();
        Object result1 = buildBffResponseMethod.invoke(this.testInstance, responseType);

        then: ""
        Assert.assertNotNull(result1);
    }

    @Unroll
    def "test_business"() {
        setup: ""

        when: ""
        SupplierQuitActivityRequestType requestType = new SupplierQuitActivityRequestType();
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setUid("10086");
        requestType.setBaseRequest(baseRequest);
        CustomerBusinessIMServiceProxy var10000 = this.proxy;
        (var10000.updateActivityStatus(_ as UpdateActivityStatusRequestType)) >> (null);
        SupplierQuitActivityService var5 = this.testInstance;
        SupplierQuitActivityResponseType result1 = var5.business(requestType);

        UpdateActivityStatusResponseType responseType = new UpdateActivityStatusResponseType();
        (this.proxy.updateActivityStatus(_ as UpdateActivityStatusRequestType)) >> (responseType);
        SupplierQuitActivityResponseType result2 = this.testInstance.business(requestType);

        then: ""
        Assert.assertNotNull(result1);
        Assert.assertEquals("60000", result1.getBaseResponse().getCode())
        Assert.assertEquals("an error occurs when invoking third-service, fail to quit activity.", result1.getBaseResponse().getMessage())

        Assert.assertNotNull(result2);
    }

}
