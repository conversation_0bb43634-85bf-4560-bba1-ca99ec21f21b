package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.UpdateAgentAllocateRuleMapper
import com.ctrip.car.businessim.bff.service.core.mapper.UpdateBusinessIMAgentMapper
import com.ctrip.car.businessim.bff.service.entity.UpdateAgentAllocateRuleRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.VendorAgentAllocateRuleDto
import com.ctrip.car.customer.businessim.contract.servicetype.UpdateAgentAllocateRuleResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class UpdateAgentAllocateRuleServiceTest extends Specification {

    def mapper = Mock(UpdateAgentAllocateRuleMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def updateAgentAllocateRuleService = new UpdateAgentAllocateRuleService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where updateAgentAllocateRuleRequestType=#updateAgentAllocateRuleRequestType then expect: #expectedResult"() {
        given:
         (mapper.from(_ as UpdateAgentAllocateRuleRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.UpdateAgentAllocateRuleRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, new VendorAgentAllocateRuleDto(1l, "vendorName", 0)))

        expect:
        updateAgentAllocateRuleService.convertRequest(updateAgentAllocateRuleRequestType) == expectedResult

        where:
        updateAgentAllocateRuleRequestType                                                                                                                                                                                                                                                                                                                || expectedResult
        new UpdateAgentAllocateRuleRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, new com.ctrip.car.businessim.bff.service.dto.VendorAgentAllocateRuleDto(1l, "vendorName", 0)) || new com.ctrip.car.customer.businessim.contract.servicetype.UpdateAgentAllocateRuleRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, new VendorAgentAllocateRuleDto(1l, "vendorName", 0))
    }

    @Unroll
    def "execute SOA where updateAgentAllocateRuleRequestType=#updateAgentAllocateRuleRequestType then expect: #expectedResult"() {
        given:
         (proxy.updateAgentAllocateRule(_ as com.ctrip.car.customer.businessim.contract.servicetype.UpdateAgentAllocateRuleRequestType))>>(new UpdateAgentAllocateRuleResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l)))

        expect:
        updateAgentAllocateRuleService.executeSOA(updateAgentAllocateRuleRequestType) == expectedResult

        where:
        updateAgentAllocateRuleRequestType                                                                                                                                                                                                                                                                                                                              || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.UpdateAgentAllocateRuleRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, new VendorAgentAllocateRuleDto(1l, "vendorName", 0)) || new UpdateAgentAllocateRuleResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l))
    }

    @Unroll
    def "convert Response where updateAgentAllocateRuleRequestType2=#updateAgentAllocateRuleRequestType2 and updateAgentAllocateRuleResponseType=#updateAgentAllocateRuleResponseType and updateAgentAllocateRuleRequestType=#updateAgentAllocateRuleRequestType then expect: #expectedResult"() {
        given:
         (mapper.to(_ as UpdateAgentAllocateRuleResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.UpdateAgentAllocateRuleResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l)))

        expect:
        updateAgentAllocateRuleService.convertResponse(updateAgentAllocateRuleResponseType, updateAgentAllocateRuleRequestType, updateAgentAllocateRuleRequestType2) == expectedResult

        where:
        updateAgentAllocateRuleRequestType2                                                                                                                                                                                                                                                                                                               | updateAgentAllocateRuleResponseType                                                                                       | updateAgentAllocateRuleRequestType                                                                                                                                                                                                                                                                                                                              || expectedResult
        new UpdateAgentAllocateRuleRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, new com.ctrip.car.businessim.bff.service.dto.VendorAgentAllocateRuleDto(1l, "vendorName", 0)) | new UpdateAgentAllocateRuleResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l)) | new com.ctrip.car.customer.businessim.contract.servicetype.UpdateAgentAllocateRuleRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, new VendorAgentAllocateRuleDto(1l, "vendorName", 0)) || new com.ctrip.car.businessim.bff.service.entity.UpdateAgentAllocateRuleResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l))
    }


}

