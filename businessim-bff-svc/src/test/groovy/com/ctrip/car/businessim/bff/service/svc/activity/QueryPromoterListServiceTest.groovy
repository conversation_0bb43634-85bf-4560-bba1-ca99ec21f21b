package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.car.businessim.bff.service.core.mapper.activity.QueryPromoterListMapper
import com.ctrip.car.businessim.bff.service.core.mapper.activity.QueryQRCodeMapper
import com.ctrip.car.businessim.bff.service.dto.activity.PromoterInfo
import com.ctrip.car.businessim.bff.service.entity.QueryPromoterListBffRequestType
import com.ctrip.car.businessim.bff.service.entity.QueryPromoterListBffResponseType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.activity.PromoterDto
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryPromoterListRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryPromoterListResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class QueryPromoterListServiceTest extends Specification {


    def mapper = Mock(QueryPromoterListMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def queryPromoterListService = new QueryPromoterListService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        (mapper.from(_ as QueryPromoterListBffRequestType))>>(new QueryPromoterListRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "keyword", 0, 0))

        expect:
        queryPromoterListService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                           || expectedResult
        new QueryPromoterListBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "keyword", 0, 0) || new QueryPromoterListRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "keyword", 0, 0)
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        (proxy.queryPromoterList(_ as QueryPromoterListRequestType))>>(new QueryPromoterListResponseType([new PromoterDto(1l, "promoterName")], new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()))

        expect:
        queryPromoterListService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                        || expectedResult
        new QueryPromoterListRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "keyword", 0, 0) || new QueryPromoterListResponseType([new PromoterDto(1l, "promoterName")], new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType())
    }

    @Unroll
    def "convert Response where response=#response and var3=#var3 and var2=#var2 then expect: #expectedResult"() {
        given:
        (mapper.to(_ as QueryPromoterListResponseType))>>(new QueryPromoterListBffResponseType([new PromoterInfo(1l, "promoterName")], new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()))

        expect:
        queryPromoterListService.convertResponse(response, var2, var3) == expectedResult

        where:
        response                                                                                                                                                   | var3                                                                                                                                                                                                                                                              | var2                                                                                                                                                                                                                                                           || expectedResult
        new QueryPromoterListResponseType([new PromoterDto(1l, "promoterName")], new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()) | new QueryPromoterListBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "keyword", 0, 0) | new QueryPromoterListRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "keyword", 0, 0) || new QueryPromoterListBffResponseType([new PromoterInfo(1l, "promoterName")], new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType())
    }


}

