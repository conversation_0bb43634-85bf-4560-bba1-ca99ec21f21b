package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.DownloadIMSessionMapper
import com.ctrip.car.businessim.bff.service.core.mapper.DownloadIVRCallMapper
import com.ctrip.car.businessim.bff.service.entity.DownloadIMSessionRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.DownloadIMSessionResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class DownloadIMSessionServiceTest extends Specification {


    def mapper = Mock(DownloadIMSessionMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def downloadIMSessionService = new DownloadIMSessionService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        (mapper.from(_ as  DownloadIMSessionRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.DownloadIMSessionRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "sessionCreateStartTime", "sessionCreateEndTime", [1l], ["vbkAccountList"], [0], [0],"sessionId",[1]))

        expect:
        downloadIMSessionService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                         || expectedResult
        new DownloadIMSessionRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "sessionCreateStartTime", "sessionCreateEndTime", [1l], ["vbkAccountList"], [0], [0],[1],"sessionId") || new com.ctrip.car.customer.businessim.contract.servicetype.DownloadIMSessionRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "sessionCreateStartTime", "sessionCreateEndTime", [1l], ["vbkAccountList"], [0], [0],"sessionId",[1])
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        (proxy.downloadIMSession(_ as  com.ctrip.car.customer.businessim.contract.servicetype.DownloadIMSessionRequestType))>>(new DownloadIMSessionResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "exportUrl", 1l))

        expect:
        downloadIMSessionService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.DownloadIMSessionRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "sessionCreateStartTime", "sessionCreateEndTime", [1l], ["vbkAccountList"], [0], [0],"sessionId",[1]) || new DownloadIMSessionResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "exportUrl", 1l)
    }

    @Unroll
    def "convert Response where request=#request and response=#response and request2=#request2 then expect: #expectedResult"() {
        given:
        (mapper.to(_ as  DownloadIMSessionResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.DownloadIMSessionResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "exportUrl", 1l))

        expect:
        downloadIMSessionService.convertResponse(response, request, request2) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                | response                                                                                                                             | request2                                                                                                                                                                                                                                                                                                                        || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.DownloadIMSessionRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "sessionCreateStartTime", "sessionCreateEndTime", [1l], ["vbkAccountList"], [0], [0],"sessionId",[1]) | new DownloadIMSessionResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "exportUrl", 1l) | new DownloadIMSessionRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "sessionCreateStartTime", "sessionCreateEndTime", [1l], ["vbkAccountList"], [0], [0],[1],"sessionId") || new com.ctrip.car.businessim.bff.service.entity.DownloadIMSessionResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "exportUrl", 1l)
    }


}

