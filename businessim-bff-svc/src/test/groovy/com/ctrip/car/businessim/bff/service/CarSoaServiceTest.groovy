package com.ctrip.car.businessim.bff.service

import com.ctrip.car.businessim.bff.service.core.mapper.GetIMGrayStatusMapper
import com.ctrip.car.businessim.bff.service.entity.GetIMGrayStatusRequestType
import com.ctrip.car.businessim.bff.service.entity.GetIMGrayStatusResponseType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.businessim.bff.service.svc.GetIMGrayStatusService
import spock.lang.*

class CarSoaServiceTest extends Specification {

    def mapper = Mock(GetIMGrayStatusMapper)

    def proxy = Mock(CustomerBusinessIMServiceProxy)

    GetIMGrayStatusService testInstance = new GetIMGrayStatusService(mapper: mapper, proxy: proxy)

    def "test to Response"() {
        when:
        GetIMGrayStatusResponseType result = testInstance.toResponse(new GetIMGrayStatusRequestType())

        then:
        Objects.isNull(result)
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme