package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.GetIMSessionReportMapper
import com.ctrip.car.businessim.bff.service.core.mapper.GetIVRCallReportMapper
import com.ctrip.car.businessim.bff.service.entity.GetIMSessionReportRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.IMSessionRateDto
import com.ctrip.car.customer.businessim.contract.dto.IMSessionRateLevelDto
import com.ctrip.car.customer.businessim.contract.dto.IMSesssionDto
import com.ctrip.car.customer.businessim.contract.servicetype.GetIMSessionReportResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class GetIMSessionReportServiceTest extends Specification {


    def mapper = Mock(GetIMSessionReportMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def getIMSessionReportService = new GetIMSessionReportService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        (mapper.from( _ as GetIMSessionReportRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.GetIMSessionReportRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "sessionCreateStartTime", "sessionCreateEndTime", [1l], ["vbkAccountList"], [0], [0], 0, 0,"sessionId",[1]))

        expect:
        getIMSessionReportService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                || expectedResult
        new GetIMSessionReportRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "sessionCreateStartTime", "sessionCreateEndTime", [1l], ["vbkAccountList"], [0], [0], 0, 0,"sessionId",[1]) || new com.ctrip.car.customer.businessim.contract.servicetype.GetIMSessionReportRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "sessionCreateStartTime", "sessionCreateEndTime", [1l], ["vbkAccountList"], [0], [0], 0, 0,"sessionId",[1])
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        (proxy.getIMSessionReport( _ as com.ctrip.car.customer.businessim.contract.servicetype.GetIMSessionReportRequestType))>>(new GetIMSessionReportResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), 1l, [new IMSesssionDto("sessionId", "sessionDate", "saleType", 1l, 1l, "storeName", "vbkAccountNames", "replyStatus", "firstReplyInterval", "sessionCreateTime", "sessionToHumanTime", "sessionEndTime", "sessionInterval", "sessionReview", "presaleConversionStatus",null)], new IMSessionRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new IMSessionRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new IMSessionRateLevelDto(0, 0, 0, 0)))

        expect:
        getIMSessionReportService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                       || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetIMSessionReportRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "sessionCreateStartTime", "sessionCreateEndTime", [1l], ["vbkAccountList"], [0], [0], 0, 0,"sessionId",[1]) || new GetIMSessionReportResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), 1l, [new IMSesssionDto("sessionId", "sessionDate", "saleType", 1l, 1l, "storeName", "vbkAccountNames", "replyStatus", "firstReplyInterval", "sessionCreateTime", "sessionToHumanTime", "sessionEndTime", "sessionInterval", "sessionReview", "presaleConversionStatus",null)], new IMSessionRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new IMSessionRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new IMSessionRateLevelDto(0, 0, 0, 0))
    }

    @Unroll
    def "convert Response where request=#request and response=#response and request2=#request2 then expect: #expectedResult"() {
        given:
        (mapper.to( _ as GetIMSessionReportResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.GetIMSessionReportResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), 1l, [new com.ctrip.car.businessim.bff.service.dto.IMSesssionDto("sessionId", "sessionDate", "saleType", 1l, 1l, "storeName", "vbkAccountNames", "replyStatus", "firstReplyInterval", "sessionCreateTime", "sessionToHumanTime", "sessionEndTime", "sessionInterval", "sessionReview", "presaleConversionStatus",null)], new com.ctrip.car.businessim.bff.service.dto.IMSessionRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new com.ctrip.car.businessim.bff.service.dto.IMSessionRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new com.ctrip.car.businessim.bff.service.dto.IMSessionRateLevelDto(0, 0, 0, 0)))

        expect:
        getIMSessionReportService.convertResponse(response, request, request2) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                       | response                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | request2 || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetIMSessionReportRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "sessionCreateStartTime", "sessionCreateEndTime", [1l], ["vbkAccountList"], [0], [0], 0, 0,"sessionId",[1]) | new GetIMSessionReportResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), 1l, [new IMSesssionDto("sessionId", "sessionDate", "saleType", 1l, 1l, "storeName", "vbkAccountNames", "replyStatus", "firstReplyInterval", "sessionCreateTime", "sessionToHumanTime", "sessionEndTime", "sessionInterval", "sessionReview", "presaleConversionStatus",null)], new IMSessionRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new IMSessionRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new IMSessionRateLevelDto(0, 0, 0, 0)) | new GetIMSessionReportRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "sessionCreateStartTime", "sessionCreateEndTime", [1l], ["vbkAccountList"], [0], [0], 0, 0,"sessionId",[1]) || new com.ctrip.car.businessim.bff.service.entity.GetIMSessionReportResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), 1l, [new com.ctrip.car.businessim.bff.service.dto.IMSesssionDto("sessionId", "sessionDate", "saleType", 1l, 1l, "storeName", "vbkAccountNames", "replyStatus", "firstReplyInterval", "sessionCreateTime", "sessionToHumanTime", "sessionEndTime", "sessionInterval", "sessionReview", "presaleConversionStatus",null)], new com.ctrip.car.businessim.bff.service.dto.IMSessionRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new com.ctrip.car.businessim.bff.service.dto.IMSessionRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new com.ctrip.car.businessim.bff.service.dto.IMSessionRateLevelDto(0, 0, 0, 0))
    }

}

