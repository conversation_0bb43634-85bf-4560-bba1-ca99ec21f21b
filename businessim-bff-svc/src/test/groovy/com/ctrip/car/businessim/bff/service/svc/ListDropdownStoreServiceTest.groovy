package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.ListDropdownStoreMapper
import com.ctrip.car.businessim.bff.service.core.mapper.ListDropdownUserMapper
import com.ctrip.car.businessim.bff.service.entity.ListDropdownStoreRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.DropdownStoreDto
import com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownStoreResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class ListDropdownStoreServiceTest extends Specification {


    def mapper = Mock(ListDropdownStoreMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def listDropdownStoreService = new ListDropdownStoreService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        (mapper.from(_ as ListDropdownStoreRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownStoreRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "keyword"))

        expect:
        listDropdownStoreService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                  || expectedResult
        new ListDropdownStoreRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "keyword") || new com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownStoreRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "keyword")
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        (proxy.listDropdownStore(_ as com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownStoreRequestType))>>(new ListDropdownStoreResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new DropdownStoreDto(1l, "storeName")], "restDropdownStoreCacheKey", 0))

        expect:
        listDropdownStoreService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                         || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownStoreRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "keyword") || new ListDropdownStoreResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new DropdownStoreDto(1l, "storeName")], "restDropdownStoreCacheKey", 0)
    }

    @Unroll
    def "convert Response where request=#request and response=#response and request2=#request2 then expect: #expectedResult"() {
        given:
        (mapper.to(_ as ListDropdownStoreResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.ListDropdownStoreResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new com.ctrip.car.businessim.bff.service.dto.DropdownStoreDto(1l, "storeName")], "restDropdownStoreCacheKey", 0))

        expect:
        listDropdownStoreService.convertResponse(response, request, request2) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                         | response                                                                                                                                                                                     | request2                                                                                                                                                                                                                                                 || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownStoreRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "keyword") | new ListDropdownStoreResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new DropdownStoreDto(1l, "storeName")], "restDropdownStoreCacheKey", 0) | new ListDropdownStoreRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "keyword") || new com.ctrip.car.businessim.bff.service.entity.ListDropdownStoreResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new com.ctrip.car.businessim.bff.service.dto.DropdownStoreDto(1l, "storeName")], "restDropdownStoreCacheKey", 0)
    }


}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme