//package com.ctrip.car.businessim.bff.service.svc.activity
//
//import com.ctrip.basebiz.offlinebase.utils.QconfigUtils
//import com.ctrip.car.businessim.bff.service.entity.SupplierQueryOpenCityInfoRequestType
//import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
//import com.ctrip.car.customer.businessim.contract.dto.activity.DataSourceInfo
//import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryOpenCityInfoByVendorIdsRequestType
//import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryOpenCityInfoByVendorIdsResponseType
//import com.ctrip.car.top.BaseRequest
//import com.google.common.collect.Lists
//import org.apache.commons.collections4.CollectionUtils
//import org.junit.runner.RunWith
//import org.powermock.core.classloader.annotations.PowerMockIgnore
//import org.powermock.core.classloader.annotations.PrepareForTest
//import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
//import org.powermock.modules.junit4.PowerMockRunner
//import org.powermock.modules.junit4.PowerMockRunnerDelegate
//import org.spockframework.runtime.Sputnik
//import spock.lang.Specification
//
//@RunWith(PowerMockRunner.class)
//@PowerMockRunnerDelegate(Sputnik.class)
//@PrepareForTest([QconfigUtils.class])
//@SuppressStaticInitializationFor(["com.ctrip.ibu.platform.shark.sdk.api.Shark"])
//@PowerMockIgnore(["jdk.internal.reflect.*", "javax.xml.*", "org.xml.*", "org.w3c.*", "java.lang.LinkageError", "com.sun.org.*", "javax.net.ssl.*", "javax.management.*"])
//class SupplierQueryOpenCityInfoServiceTest extends Specification {
//
//    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
//
//    private testInstance = new SupplierQueryOpenCityInfoService(proxy: proxy)
//
//    def "test_validRequest"() {
//        setup: ""
//
//        when: ""
//        def result1 = testInstance.validRequest(new SupplierQueryOpenCityInfoRequestType())
//        def result2 = testInstance.validRequest(new SupplierQueryOpenCityInfoRequestType(baseRequest: new BaseRequest()))
//        def result3 = testInstance.validRequest(new SupplierQueryOpenCityInfoRequestType(baseRequest: new BaseRequest(uid: "10000")))
//        def result4 = testInstance.validRequest(new SupplierQueryOpenCityInfoRequestType(baseRequest: new BaseRequest(uid: "10000"), templateId: -1))
//        def result5 = testInstance.validRequest(new SupplierQueryOpenCityInfoRequestType(baseRequest: new BaseRequest(uid: "10000"), templateId: 1L))
//        def result6 = testInstance.validRequest(new SupplierQueryOpenCityInfoRequestType(baseRequest: new BaseRequest(uid: "10000"), templateId: 1L, vendorId: -1L))
//        def result7 = testInstance.validRequest(new SupplierQueryOpenCityInfoRequestType(baseRequest: new BaseRequest(uid: "10000"), templateId: 1L, vendorId: 1L))
//
//        then: ""
//        Objects.nonNull(result1)
//        Objects.equals(result1.getBaseResponse().getCode(), "40001")
//        Objects.equals(result1.getBaseResponse().getMessage(), "baseRequest is null; templateId is null; vendorId is null; ")
//
//        Objects.nonNull(result2)
//        Objects.equals(result2.getBaseResponse().getCode(), "40001")
//        Objects.equals(result2.getBaseResponse().getMessage(), "uid is null; templateId is null; vendorId is null; ")
//
//        Objects.nonNull(result3)
//        Objects.equals(result3.getBaseResponse().getCode(), "40001")
//        Objects.equals(result3.getBaseResponse().getMessage(), "templateId is null; vendorId is null; ")
//
//        Objects.nonNull(result4)
//        Objects.equals(result4.getBaseResponse().getCode(), "40001")
//        Objects.equals(result4.getBaseResponse().getMessage(), "templateId is null; vendorId is null; ")
//
//        Objects.nonNull(result5)
//        Objects.equals(result5.getBaseResponse().getCode(), "40001")
//        Objects.equals(result5.getBaseResponse().getMessage(), "vendorId is null; ")
//
//        Objects.nonNull(result6)
//        Objects.equals(result6.getBaseResponse().getCode(), "40001")
//        Objects.equals(result6.getBaseResponse().getMessage(), "vendorId is null; ")
//
//        Objects.isNull(result7)
//    }
//
//    def "test_business"() {
//        setup: ""
//
//        and: ""
//        proxy.queryOpenCityInfoList(_ as QueryOpenCityInfoByVendorIdsRequestType)\
//        >> null\
//        >> new QueryOpenCityInfoByVendorIdsResponseType(infos: new ArrayList<DataSourceInfo>())\
//        >> new QueryOpenCityInfoByVendorIdsResponseType(infos: Lists.newArrayList(new DataSourceInfo(dataKey: "1", displayName: "name")))
//
//        when: ""
//        def result1 = testInstance.business(new SupplierQueryOpenCityInfoRequestType())
//        def result2 = testInstance.business(new SupplierQueryOpenCityInfoRequestType(keyWords: "keyWord", baseRequest: new BaseRequest(uid: "A")))
//        def result3 = testInstance.business(new SupplierQueryOpenCityInfoRequestType(keyWords: "keyWord", baseRequest: new BaseRequest(uid: "1")))
//        def result4 = testInstance.business(new SupplierQueryOpenCityInfoRequestType(keyWords: "keyWord", baseRequest: new BaseRequest(uid: "1")))
//
//        then: ""
//        Objects.nonNull(result1)
//        CollectionUtils.isEmpty(result1.getCityInfoList())
//        Objects.equals(result1.getBaseResponse().getCode(), "000000")
//
//        Objects.nonNull(result2)
//        CollectionUtils.isEmpty(result2.getCityInfoList())
//        Objects.equals(result2.getBaseResponse().getCode(), "60000")
//
//        Objects.nonNull(result3)
//
//        Objects.nonNull(result4)
//        Objects.equals(result4.getCityInfoList().get(0).getCityId(), 1L)
//        Objects.equals(result4.getCityInfoList().get(0).getCityName(), "name")
//    }
//
//}