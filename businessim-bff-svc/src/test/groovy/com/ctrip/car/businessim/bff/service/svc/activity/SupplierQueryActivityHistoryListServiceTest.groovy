package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.car.businessim.bff.service.entity.SupplierQueryActivityHistoryListRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.activity.VendorActivityListItem
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryVendorActivityListRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryVendorActivityListResponseType
import com.ctrip.car.top.BaseRequest
import com.google.common.collect.Lists
import org.apache.commons.collections4.CollectionUtils

import spock.lang.Specification
import spock.lang.Unroll

class SupplierQueryActivityHistoryListServiceTest extends Specification {

    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def testInstance = new SupplierQueryActivityHistoryListService(
            proxy: proxy
    )

    def "test_validRequest"() {
        setup: ""

        when: ""
        def result1 = testInstance.validRequest(new SupplierQueryActivityHistoryListRequestType())
        def result2 = testInstance.validRequest(new SupplierQueryActivityHistoryListRequestType(baseRequest: new BaseRequest()))
        def result3 = testInstance.validRequest(new SupplierQueryActivityHistoryListRequestType(baseRequest: new BaseRequest(uid: "10000")))

        then: ""
        Objects.nonNull(result1)
        Objects.equals(result1.getBaseResponse().getCode(), "40001")
        Objects.equals(result1.getBaseResponse().getMessage(), "baseRequest is null; ")

        Objects.nonNull(result2)
        Objects.equals(result2.getBaseResponse().getCode(), "40001")
        Objects.equals(result2.getBaseResponse().getMessage(), "uid is null; ")

        Objects.isNull(result3)
    }

    @Unroll
    def "test_business"() {
        setup: ""

        and: ""
        proxy.queryVendorActivityList(_ as QueryVendorActivityListRequestType)\
                 >> null\
                 >> new QueryVendorActivityListResponseType(vendorActivityList: Lists.newArrayList(new VendorActivityListItem(status: 1)))

        when: ""
        def result1 = testInstance.business(new SupplierQueryActivityHistoryListRequestType(baseRequest: new BaseRequest(uid: "10000")))
        def result2 = testInstance.business(new SupplierQueryActivityHistoryListRequestType(baseRequest: new BaseRequest(uid: "10000")))

        then: ""
        Objects.nonNull(result1)
        Objects.equals(result1.getPageIndex(), 1)
        CollectionUtils.isEmpty(result1.getActivityInfoList())

        Objects.nonNull(result2)
       // Objects.equals(result2.getSortTypeName(), "活动时间近->远")
        Objects.equals(result2.getPageIndex(), 1)
        CollectionUtils.isNotEmpty(result2.getActivityInfoList())
        Objects.equals(result2.getActivityInfoList().get(0).getDiscountStrength(), "null<br/>null")
        //Objects.equals(result2.getActivityInfoList().get(0).getActivityStatusName(), "未开始")
        Objects.equals(result2.getActivityInfoList().get(0).getActivityStatus(), 1)
    }

}
