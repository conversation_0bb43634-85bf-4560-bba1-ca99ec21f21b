package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.ListDropdownUserMapper
import com.ctrip.car.businessim.bff.service.core.mapper.ListOrderForIMCustomizeAreaMapper
import com.ctrip.car.businessim.bff.service.entity.ListDropdownUserRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.DropDownUserDto
import com.ctrip.car.customer.businessim.contract.dto.StoreRangeDto
import com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownUserResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class ListDropdownUserServiceTest extends Specification {

    def mapper = Mock(ListDropdownUserMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def listDropdownUserService = new ListDropdownUserService(
            proxy: proxy,
            mapper: mapper)
    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
         (mapper.from(_ as ListDropdownUserRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownUserRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), 1l, "keyword", "totalBusinessIMAgentCacheKey"))

        expect:
        listDropdownUserService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                      || expectedResult
        new ListDropdownUserRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new com.ctrip.car.businessim.bff.service.dto.StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), 1l, "keyword", "totalBusinessIMAgentCacheKey") || new com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownUserRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), 1l, "keyword", "totalBusinessIMAgentCacheKey")
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
         (proxy.listDropdownUser(_ as com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownUserRequestType))>>(new ListDropdownUserResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new DropDownUserDto(1l, "userName", "account")]))

        expect:
        listDropdownUserService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                    || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownUserRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), 1l, "keyword", "totalBusinessIMAgentCacheKey") || new ListDropdownUserResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new DropDownUserDto(1l, "userName", "account")])
    }

    @Unroll
    def "convert Response where request=#request and response=#response and request2=#request2 then expect: #expectedResult"() {
        given:
         (mapper.to(_ as ListDropdownUserResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.ListDropdownUserResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new com.ctrip.car.businessim.bff.service.dto.DropDownUserDto(1l, "userName", "account")]))

        expect:
        listDropdownUserService.convertResponse(response, request, request2) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                    | response                                                                                                                                                             | request2                                                                                                                                                                                                                                                                                                                                                                                     || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownUserRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), 1l, "keyword", "totalBusinessIMAgentCacheKey") | new ListDropdownUserResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new DropDownUserDto(1l, "userName", "account")]) | new ListDropdownUserRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new com.ctrip.car.businessim.bff.service.dto.StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), 1l, "keyword", "totalBusinessIMAgentCacheKey") || new com.ctrip.car.businessim.bff.service.entity.ListDropdownUserResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new com.ctrip.car.businessim.bff.service.dto.DropDownUserDto(1l, "userName", "account")])
    }


}

