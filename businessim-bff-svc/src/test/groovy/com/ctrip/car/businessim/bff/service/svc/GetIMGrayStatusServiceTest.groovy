package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.GetIMGrayStatusMapper
import com.ctrip.car.businessim.bff.service.core.mapper.GetIMSessionReportMapper
import com.ctrip.car.businessim.bff.service.entity.GetIMGrayStatusRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.GetIMGrayStatusResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class GetIMGrayStatusServiceTest extends Specification {


    def mapper = Mock(GetIMGrayStatusMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def getIMGrayStatusService = new GetIMGrayStatusService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        (mapper.from( _ as GetIMGrayStatusRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.GetIMGrayStatusRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l))

        expect:
        getIMGrayStatusService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                     || expectedResult
        new GetIMGrayStatusRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) || new com.ctrip.car.customer.businessim.contract.servicetype.GetIMGrayStatusRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l)
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        (proxy.getIMGrayStatus( _ as com.ctrip.car.customer.businessim.contract.servicetype.GetIMGrayStatusRequestType))>>(new GetIMGrayStatusResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), 0))

        expect:
        getIMGrayStatusService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                            || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetIMGrayStatusRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) || new GetIMGrayStatusResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), 0)
    }

    @Unroll
    def "convert Response where request=#request and response=#response and request2=#request2 then expect: #expectedResult"() {
        given:
        (mapper.to( _ as GetIMGrayStatusResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.GetIMGrayStatusResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), 0))

        expect:
        getIMGrayStatusService.convertResponse(response, request, request2) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                            | response                                                                                                             | request2                                                                                                                                                                                                                                    || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetIMGrayStatusRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) | new GetIMGrayStatusResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), 0) | new GetIMGrayStatusRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) || new com.ctrip.car.businessim.bff.service.entity.GetIMGrayStatusResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), 0)
    }


}

