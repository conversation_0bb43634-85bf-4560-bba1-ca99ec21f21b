package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.GetB2OImGrayStatusMapper
import com.ctrip.car.businessim.bff.service.entity.GetB2OImGrayStatusRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.GetB2OImGrayStatusResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.Specification
import spock.lang.Unroll

class GetB2OImGrayStatusServiceTest extends Specification {


    def mapper = Mock(GetB2OImGrayStatusMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def getB2OImGrayStatusService = new GetB2OImGrayStatusService(
            proxy: proxy,
            mapper: mapper)
    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
         (mapper.from(_ as GetB2OImGrayStatusRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.GetB2OImGrayStatusRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")))


        expect:
        getB2OImGrayStatusService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                      || expectedResult
        new GetB2OImGrayStatusRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) || new com.ctrip.car.customer.businessim.contract.servicetype.GetB2OImGrayStatusRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"))
    }

    @Unroll
    def "execute SOA where request2=#request2 then expect: #expectedResult"() {
        given:
        (proxy.getB2OImGrayStatus( _ as com.ctrip.car.customer.businessim.contract.servicetype.GetB2OImGrayStatusRequestType))>>(new GetB2OImGrayStatusResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l),1))

        expect:
        getB2OImGrayStatusService.executeSOA(request2) == expectedResult

        where:
        request2                                                                                                                                                                                                                                                                                                            || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetB2OImGrayStatusRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) || new GetB2OImGrayStatusResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l),1)
    }

    @Unroll
    def "convert Response where request=#request and response2=#response2 and request2=#request2 then expect: #expectedResult"() {
        given:
        (mapper.to( _ as GetB2OImGrayStatusResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.GetB2OImGrayStatusResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l),1))

        expect:
        getB2OImGrayStatusService.convertResponse(response2, request2, request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                      | response2                                                                                                                                                                       | request2                                                                                                                                                                                                                                                                                                            || expectedResult
        new GetB2OImGrayStatusRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) | new GetB2OImGrayStatusResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l),1) | new com.ctrip.car.customer.businessim.contract.servicetype.GetB2OImGrayStatusRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) || new com.ctrip.car.businessim.bff.service.entity.GetB2OImGrayStatusResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l),1)
    }


}

