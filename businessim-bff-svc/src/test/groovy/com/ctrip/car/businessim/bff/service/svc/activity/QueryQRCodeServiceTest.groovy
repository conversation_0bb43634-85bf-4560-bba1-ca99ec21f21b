package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.car.businessim.bff.service.core.mapper.activity.QueryQRCodeMapper
import com.ctrip.car.businessim.bff.service.core.mapper.activity.QueryStoreListMapper
import com.ctrip.car.businessim.bff.service.entity.QueryQRCodeBffRequestType
import com.ctrip.car.businessim.bff.service.entity.QueryQRCodeBffResponseType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryQRCodeRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryQRCodeResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class QueryQRCodeServiceTest extends Specification {


    def mapper = Mock(QueryQRCodeMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def queryQRCodeService = new QueryQRCodeService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
         (mapper.from(_ as QueryQRCodeBffRequestType))>>(new QueryQRCodeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "qrCode"))

        expect:
        queryQRCodeService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                          || expectedResult
        new QueryQRCodeBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "qrCode") || new QueryQRCodeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "qrCode")
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
         (proxy.queryQRCode(_ as QueryQRCodeRequestType))>>(new QueryQRCodeResponseType(1l, "promoterName", 1l, "vendorName", 1l, 0 as BigDecimal, 0 as BigDecimal, 0, "storeAddress", "shareContent", new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()))

        expect:
        queryQRCodeService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                       || expectedResult
        new QueryQRCodeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "qrCode") || new QueryQRCodeResponseType(1l, "promoterName", 1l, "vendorName", 1l, 0 as BigDecimal, 0 as BigDecimal, 0, "storeAddress", "shareContent", new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType())
    }

    @Unroll
    def "convert Response where response=#response and var3=#var3 and var2=#var2 then expect: #expectedResult"() {
        given:
         (mapper.to(_ as QueryQRCodeResponseType))>>(new QueryQRCodeBffResponseType(1l, "promoterName", 1l, "vendorName", 1l, 0 as BigDecimal, 0 as BigDecimal, 0, "storeAddress", new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()))

        expect:
        queryQRCodeService.convertResponse(response, var2, var3) == expectedResult

        where:
        response                                                                                                                                                                                                                     | var3                                                                                                                                                                                                                                             | var2                                                                                                                                                                                                                                          || expectedResult
        new QueryQRCodeResponseType(1l, "promoterName", 1l, "vendorName", 1l, 0 as BigDecimal, 0 as BigDecimal, 0, "storeAddress", "shareContent", new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()) | new QueryQRCodeBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "qrCode") | new QueryQRCodeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "qrCode") || new QueryQRCodeBffResponseType(1l, "promoterName", 1l, "vendorName", 1l, 0 as BigDecimal, 0 as BigDecimal, 0, "storeAddress", new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType())
    }


}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme