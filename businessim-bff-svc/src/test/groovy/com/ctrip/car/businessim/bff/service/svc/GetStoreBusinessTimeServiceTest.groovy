package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.GetStoreBusinessTimeMapper
import com.ctrip.car.businessim.bff.service.core.mapper.ListAgentAllocateRuleMapper
import com.ctrip.car.businessim.bff.service.entity.GetStoreBusinessTimeRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.StoreBusinessTimeDto
import com.ctrip.car.customer.businessim.contract.dto.StoreRangeDto
import com.ctrip.car.customer.businessim.contract.servicetype.GetStoreBusinessTimeResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class GetStoreBusinessTimeServiceTest extends Specification {



    def mapper = Mock(GetStoreBusinessTimeMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def getStoreBusinessTimeService = new GetStoreBusinessTimeService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
         (mapper.from(_ as GetStoreBusinessTimeRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.GetStoreBusinessTimeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"])))

        expect:
        getStoreBusinessTimeService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                           || expectedResult
        new GetStoreBusinessTimeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new com.ctrip.car.businessim.bff.service.dto.StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"])) || new com.ctrip.car.customer.businessim.contract.servicetype.GetStoreBusinessTimeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]))
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
         (proxy.getStoreBusinessTime(_ as com.ctrip.car.customer.businessim.contract.servicetype.GetStoreBusinessTimeRequestType))>>(new GetStoreBusinessTimeResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), new StoreBusinessTimeDto([0], "fromTime", "toTime")))

        expect:
        getStoreBusinessTimeService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                         || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetStoreBusinessTimeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"])) || new GetStoreBusinessTimeResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), new StoreBusinessTimeDto([0], "fromTime", "toTime"))
    }

    @Unroll
    def "convert Response where request=#request and response=#response and request2=#request2 then expect: #expectedResult"() {
        given:
         (mapper.to(_ as GetStoreBusinessTimeResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.GetStoreBusinessTimeResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), new com.ctrip.car.businessim.bff.service.dto.StoreBusinessTimeDto([0], "fromTime", "toTime")))

        expect:
        getStoreBusinessTimeService.convertResponse(response, request, request2) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                         | response                                                                                                                                                                    | request2                                                                                                                                                                                                                                                                                                                                          || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetStoreBusinessTimeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"])) | new GetStoreBusinessTimeResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), new StoreBusinessTimeDto([0], "fromTime", "toTime")) | new GetStoreBusinessTimeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new com.ctrip.car.businessim.bff.service.dto.StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"])) || new com.ctrip.car.businessim.bff.service.entity.GetStoreBusinessTimeResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), new com.ctrip.car.businessim.bff.service.dto.StoreBusinessTimeDto([0], "fromTime", "toTime"))
    }

}

