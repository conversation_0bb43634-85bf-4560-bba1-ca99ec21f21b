package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.car.businessim.bff.service.entity.SupplierModifyActivityRequestType
import com.ctrip.car.businessim.bff.service.entity.SupplierModifyActivityResponseType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.activity.ModifyActivityRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.ModifyActivityResponseType
import com.ctrip.car.top.BaseRequest
import org.junit.Assert
import org.mockito.Mockito

import spock.lang.Specification

import java.lang.reflect.Method

class SupplierModifyActivityServiceSpockTest extends Specification {

    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def testInstance = new SupplierModifyActivityService(
            proxy: proxy)

    def "test_validRequest"() {
        setup: ""

        when: ""
        SupplierModifyActivityService var10000 = this.testInstance;
        Method validRequestMethod = var10000.getClass().getDeclaredMethod("validRequest", SupplierModifyActivityRequestType.class);
        validRequestMethod.setAccessible(true);
        SupplierModifyActivityRequestType requestType = new SupplierModifyActivityRequestType();
        SupplierModifyActivityResponseType result1 = (SupplierModifyActivityResponseType) validRequestMethod.invoke(this.testInstance, requestType);

        //==============================================================================================================
        BaseRequest baseRequest = new BaseRequest();
        requestType.setBaseRequest(baseRequest);
        requestType.setActivityId(10086L);
        SupplierModifyActivityResponseType result2 = (SupplierModifyActivityResponseType) validRequestMethod.invoke(this.testInstance, requestType);

        //==============================================================================================================
        baseRequest.setUid("10001");
        SupplierModifyActivityResponseType result3 = (SupplierModifyActivityResponseType) validRequestMethod.invoke(this.testInstance, requestType);

        then: ""
        Assert.assertNotNull(result1);
        Assert.assertEquals("40001", result1.getBaseResponse().getCode());
        Assert.assertEquals("baseRequest is null; activityId is null; ", result1.getBaseResponse().getMessage());

        Assert.assertNotNull(result2);
        Assert.assertEquals("40001", result2.getBaseResponse().getCode());
        Assert.assertEquals("uid is null; ", result2.getBaseResponse().getMessage());

        Assert.assertNull(result3);
    }

    def "test_buildSoaRequest"() {
        setup: ""

        when: ""
        SupplierModifyActivityService var10000 = this.testInstance;
        Method buildSoaRequestMethod = var10000.getClass().getDeclaredMethod("buildSoaRequest", SupplierModifyActivityRequestType.class);
        buildSoaRequestMethod.setAccessible(true);
        SupplierModifyActivityRequestType requestType = new SupplierModifyActivityRequestType();
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setUid("UID");
        requestType.setBaseRequest(baseRequest);
        ModifyActivityRequestType result1 = (ModifyActivityRequestType)buildSoaRequestMethod.invoke(this.testInstance, requestType);

        //==============================================================================================================
        baseRequest.setUid("10086");
        ModifyActivityRequestType result2 = (ModifyActivityRequestType)buildSoaRequestMethod.invoke(this.testInstance, requestType);

        then: ""
        Assert.assertNotNull(result1);
        Assert.assertNull(result1.getUserId())

        Assert.assertNotNull(result2);
        Assert.assertEquals(10086, result2.getUserId())
    }

    def "test_buildBffResponse"() {
        setup: ""

        when: ""
        SupplierModifyActivityService var10000 = this.testInstance;
        Method buildBffResponseMethod = var10000.getClass().getDeclaredMethod("buildBffResponse", ModifyActivityResponseType.class);
        buildBffResponseMethod.setAccessible(true);
        ModifyActivityResponseType responseType = new ModifyActivityResponseType();
        Object result1 = buildBffResponseMethod.invoke(this.testInstance, responseType);

        then: ""
        Assert.assertNotNull(result1);
    }

    def "test_business"() {
        setup: ""

        when: ""
        SupplierModifyActivityRequestType requestType = new SupplierModifyActivityRequestType();
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setUid("10086");
        requestType.setBaseRequest(baseRequest);
        CustomerBusinessIMServiceProxy var10000 = this.proxy;
         (var10000.modifyActivity((ModifyActivityRequestType)(_ as ModifyActivityRequestType)))>>((Object)null);
        SupplierModifyActivityService var5 = this.testInstance;
        SupplierModifyActivityResponseType result1 = var5.business(requestType);

        //==============================================================================================================
        ModifyActivityResponseType responseType = new ModifyActivityResponseType();
        (this.proxy.modifyActivity((ModifyActivityRequestType)(_ as ModifyActivityRequestType)))>>(responseType);
        SupplierModifyActivityResponseType result2 = this.testInstance.business(requestType);

        then: ""
        Assert.assertNotNull(result1);
        Assert.assertEquals("60000", result1.getBaseResponse().getCode());
        Assert.assertEquals("an error occurs when invoking third-service, fail to modify activity.", result1.getBaseResponse().getMessage());

        Assert.assertNotNull(result2);
        Assert.assertNotNull(result2.getBaseResponse());
    }

}