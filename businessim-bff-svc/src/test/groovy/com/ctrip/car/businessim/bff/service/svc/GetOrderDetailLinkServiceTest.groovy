package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.GetOrderDetailLinkMapper
import com.ctrip.car.businessim.bff.service.core.mapper.GetStoreBusinessTimeMapper
import com.ctrip.car.businessim.bff.service.entity.GetOrderDetailLinkRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.GetOrderDetailLinkResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class GetOrderDetailLinkServiceTest extends Specification {


    def mapper = Mock(GetOrderDetailLinkMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def getOrderDetailLinkService = new GetOrderDetailLinkService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
         (mapper.from(_ as GetOrderDetailLinkRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.GetOrderDetailLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l))

        expect:
        getOrderDetailLinkService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                        || expectedResult
        new GetOrderDetailLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) || new com.ctrip.car.customer.businessim.contract.servicetype.GetOrderDetailLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l)
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
         (proxy.getOrderDetailLink(_ as com.ctrip.car.customer.businessim.contract.servicetype.GetOrderDetailLinkRequestType))>>(new GetOrderDetailLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "orderDetailLink"))

        expect:
        getOrderDetailLinkService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                               || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetOrderDetailLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) || new GetOrderDetailLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "orderDetailLink")
    }

    @Unroll
    def "convert Response where request=#request and response=#response and request2=#request2 then expect: #expectedResult"() {
        given:
         (mapper.to(_ as GetOrderDetailLinkResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.GetOrderDetailLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "orderDetailLink"))

        expect:
        getOrderDetailLinkService.convertResponse(response, request, request2) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                               | response                                                                                                                                | request2                                                                                                                                                                                                                                       || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetOrderDetailLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) | new GetOrderDetailLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "orderDetailLink") | new GetOrderDetailLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) || new com.ctrip.car.businessim.bff.service.entity.GetOrderDetailLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "orderDetailLink")
    }


}

