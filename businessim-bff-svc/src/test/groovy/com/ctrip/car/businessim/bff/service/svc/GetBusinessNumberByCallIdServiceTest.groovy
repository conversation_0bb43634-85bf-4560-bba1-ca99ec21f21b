package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.GetBusinessNumberByCallIdMapper
import com.ctrip.car.businessim.bff.service.core.mapper.GetDecryptDataMapper
import com.ctrip.car.businessim.bff.service.entity.GetBusinessNumberByCallIdRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.GetBusinessNumberByCallIdResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class GetBusinessNumberByCallIdServiceTest extends Specification {


    def mapper = Mock(GetBusinessNumberByCallIdMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def getBusinessNumberByCallIdService = new GetBusinessNumberByCallIdService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        (mapper.from( _ as GetBusinessNumberByCallIdRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.GetBusinessNumberByCallIdRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "callId"))

        expect:
        getBusinessNumberByCallIdService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                     || expectedResult
        new GetBusinessNumberByCallIdRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "callId") || new com.ctrip.car.customer.businessim.contract.servicetype.GetBusinessNumberByCallIdRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "callId")
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        (proxy.getBusinessNumberByCallId( _ as com.ctrip.car.customer.businessim.contract.servicetype.GetBusinessNumberByCallIdRequestType))>>(new GetBusinessNumberByCallIdResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "decryptKey"))

        expect:
        getBusinessNumberByCallIdService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                            || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetBusinessNumberByCallIdRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "callId") || new GetBusinessNumberByCallIdResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "decryptKey")
    }

    @Unroll
    def "convert Response where request=#request and response=#response and request2=#request2 then expect: #expectedResult"() {
        given:
        (mapper.to( _ as GetBusinessNumberByCallIdResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.GetBusinessNumberByCallIdResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "decryptKey"))

        expect:
        getBusinessNumberByCallIdService.convertResponse(response, request, request2) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                            | response                                                                                                                                  | request2                                                                                                                                                                                                                                                    || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetBusinessNumberByCallIdRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "callId") | new GetBusinessNumberByCallIdResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "decryptKey") | new GetBusinessNumberByCallIdRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "callId") || new com.ctrip.car.businessim.bff.service.entity.GetBusinessNumberByCallIdResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "decryptKey")
    }


}

