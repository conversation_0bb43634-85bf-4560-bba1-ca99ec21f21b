package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.ListServiceDataIMAgentMapper
import com.ctrip.car.businessim.bff.service.core.mapper.UpdateAgentAllocateRuleMapper
import com.ctrip.car.businessim.bff.service.entity.ListServiceDataIMAgentRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.IMAgentDto
import com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataIMAgentResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class ListServiceDataIMAgentServiceTest extends Specification {


    def mapper = Mock(ListServiceDataIMAgentMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def listServiceDataIMAgentService = new ListServiceDataIMAgentService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
         (mapper.from(_ as ListServiceDataIMAgentRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataIMAgentRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), [1l], "keyword"))

        expect:
        listServiceDataIMAgentService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                             || expectedResult
        new ListServiceDataIMAgentRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), [1l], 1l, "keyword") || new com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataIMAgentRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), [1l], "keyword")
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
         (proxy.listServiceDataIMAgent(_ as com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataIMAgentRequestType))>>(new ListServiceDataIMAgentResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new IMAgentDto(1l, "account", "name", "nickName", 0, Boolean.TRUE)], 0))

        expect:
        listServiceDataIMAgentService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataIMAgentRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), [1l], "keyword") || new ListServiceDataIMAgentResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new IMAgentDto(1l, "account", "name", "nickName", 0, Boolean.TRUE)], 0)
    }

    @Unroll
    def "convert Response where request=#request and response=#response and request2=#request2 then expect: #expectedResult"() {
        given:
         (mapper.to(_ as ListServiceDataIMAgentResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.ListServiceDataIMAgentResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new com.ctrip.car.businessim.bff.service.dto.IMAgentDto(1l, "account", "name", "nickName", 0, Boolean.TRUE)], 0))

        expect:
        listServiceDataIMAgentService.convertResponse(response, request, request2) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                | response                                                                                                                                                                                          | request2                                                                                                                                                                                                                                                            || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataIMAgentRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), [1l], "keyword") | new ListServiceDataIMAgentResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new IMAgentDto(1l, "account", "name", "nickName", 0, Boolean.TRUE)], 0) | new ListServiceDataIMAgentRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), [1l], 1l, "keyword") || new com.ctrip.car.businessim.bff.service.entity.ListServiceDataIMAgentResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new com.ctrip.car.businessim.bff.service.dto.IMAgentDto(1l, "account", "name", "nickName", 0, Boolean.TRUE)], 0)
    }


}

