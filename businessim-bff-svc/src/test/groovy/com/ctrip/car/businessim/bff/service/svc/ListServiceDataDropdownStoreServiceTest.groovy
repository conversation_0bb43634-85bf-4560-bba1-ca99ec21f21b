package com.ctrip.car.businessim.bff.service.svc
import com.ctrip.car.businessim.bff.service.core.mapper.ListServiceDataDropdownStoreMapper
import com.ctrip.car.businessim.bff.service.entity.ListServiceDataDropdownStoreRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.DropdownStoreDto
import com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataDropdownStoreResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*


class ListServiceDataDropdownStoreServiceTest extends Specification {

    def mapper = Mock(ListServiceDataDropdownStoreMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def listServiceDataDropdownStoreService = new ListServiceDataDropdownStoreService(
            proxy: proxy,
            mapper: mapper)


    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        (mapper.from(_ as ListServiceDataDropdownStoreRequestType)) >> (new com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataDropdownStoreRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "keyword"))

        expect:
        listServiceDataDropdownStoreService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                             || expectedResult
        new ListServiceDataDropdownStoreRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "keyword") || new com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataDropdownStoreRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "keyword")
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        (proxy.listServiceDataDropdownStore(_ as com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataDropdownStoreRequestType)) >> (new ListServiceDataDropdownStoreResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new DropdownStoreDto(1l, "storeName")]))

        expect:
        listServiceDataDropdownStoreService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                    || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataDropdownStoreRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "keyword") || new ListServiceDataDropdownStoreResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new DropdownStoreDto(1l, "storeName")])
    }

    @Unroll
    def "convert Response where request=#request and response=#response and request2=#request2 then expect: #expectedResult"() {
        given:
        (mapper.to(_ as ListServiceDataDropdownStoreResponseType)) >> (new com.ctrip.car.businessim.bff.service.entity.ListServiceDataDropdownStoreResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new com.ctrip.car.businessim.bff.service.dto.DropdownStoreDto(1l, "storeName")]))

        expect:
        listServiceDataDropdownStoreService.convertResponse(response, request, request2) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                    | response                                                                                                                                                                | request2                                                                                                                                                                                                                                                            || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataDropdownStoreRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "keyword") | new ListServiceDataDropdownStoreResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new DropdownStoreDto(1l, "storeName")]) | new ListServiceDataDropdownStoreRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "keyword") || new com.ctrip.car.businessim.bff.service.entity.ListServiceDataDropdownStoreResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new com.ctrip.car.businessim.bff.service.dto.DropdownStoreDto(1l, "storeName")])
    }


}

