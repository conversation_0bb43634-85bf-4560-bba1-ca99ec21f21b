package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.GetB2ONewLinkAndParameterMapper
import com.ctrip.car.businessim.bff.service.entity.GetB2ONewLinkAndParameterRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.CarParam
import com.ctrip.car.customer.businessim.contract.dto.OrderParam
import com.ctrip.car.customer.businessim.contract.dto.StoreParam
import com.ctrip.car.customer.businessim.contract.servicetype.GetB2ONewLinkAndParameterResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.Specification
import spock.lang.Unroll

class GetB2ONewLinkAndParameterServiceTest extends Specification {


    def mapper = Mock(GetB2ONewLinkAndParameterMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def getB2ONewLinkAndParameterService = new GetB2ONewLinkAndParameterService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
         (mapper.from(_ as GetB2ONewLinkAndParameterRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.GetB2ONewLinkAndParameterRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "","",1,new OrderParam(),new StoreParam(),new CarParam()))

        expect:
        getB2ONewLinkAndParameterService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                        || expectedResult
        new GetB2ONewLinkAndParameterRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "","",1, new com.ctrip.car.businessim.bff.service.dto.OrderParam(),new com.ctrip.car.businessim.bff.service.dto.StoreParam(),new com.ctrip.car.businessim.bff.service.dto.CarParam()) || new com.ctrip.car.customer.businessim.contract.servicetype.GetB2ONewLinkAndParameterRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "","",1,new OrderParam(),new StoreParam(),new CarParam())
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
         (proxy.getB2ONewLinkAndParameter(_ as com.ctrip.car.customer.businessim.contract.servicetype.GetB2ONewLinkAndParameterRequestType))>>(new GetB2ONewLinkAndParameterResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l),1,1,"","",new HashMap<String, String>()))

        expect:
        getB2ONewLinkAndParameterService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                               || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetB2ONewLinkAndParameterRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "","",1,new OrderParam(),new StoreParam(),new CarParam()) || new GetB2ONewLinkAndParameterResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l),1,1,"","",new HashMap<String, String>())
    }

    @Unroll
    def "convert Response where request=#request and response=#response and request2=#request2 then expect: #expectedResult"() {
        given:
         (mapper.to(_ as GetB2ONewLinkAndParameterResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.GetB2ONewLinkAndParameterResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l),1,1,"","",new HashMap<String, String>()))

        expect:
        getB2ONewLinkAndParameterService.convertResponse(response, request, request2) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                               | response                                                                                                                                | request2                                                                                                                                                                                                                                       || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetB2ONewLinkAndParameterRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "", "", 1, new OrderParam(), new StoreParam(), new CarParam()) | new GetB2ONewLinkAndParameterResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), 1, 1, "", "", new HashMap<String, String>()) | new com.ctrip.car.businessim.bff.service.entity.GetB2ONewLinkAndParameterRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "", "", 1, new com.ctrip.car.businessim.bff.service.dto.OrderParam(), new com.ctrip.car.businessim.bff.service.dto.StoreParam(), new com.ctrip.car.businessim.bff.service.dto.CarParam()) || new com.ctrip.car.businessim.bff.service.entity.GetB2ONewLinkAndParameterResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), 1, 1, "", "", new HashMap<String, String>())
    }


}

