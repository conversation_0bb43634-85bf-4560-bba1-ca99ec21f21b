package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.basebiz.offlinebase.utils.QconfigUtils
import com.ctrip.car.businessim.bff.service.entity.SupplierQueryProductInfoRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.activity.DataSourceInfo
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStandardProductInfoRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStandardProductInfoResponseType
import com.ctrip.car.top.BaseRequest
import com.google.common.collect.Lists
import org.apache.commons.collections4.CollectionUtils
import spock.lang.Specification

class SupplierQueryProductInfoServiceTest extends Specification {

    def proxy = Mock(CustomerBusinessIMServiceProxy.class)

    private testInstance = new SupplierQueryProductInfoService(proxy: proxy)

    def "test_validRequest"() {
        setup: ""

        when: ""
        def result1 = testInstance.validRequest(new SupplierQueryProductInfoRequestType())
        def result2 = testInstance.validRequest(new SupplierQueryProductInfoRequestType(baseRequest: new BaseRequest()))
        def result3 = testInstance.validRequest(new SupplierQueryProductInfoRequestType(baseRequest: new BaseRequest(uid: "10000")))
        def result4 = testInstance.validRequest(new SupplierQueryProductInfoRequestType(baseRequest: new BaseRequest(uid: "10000"), templateId: -1))
        def result5 = testInstance.validRequest(new SupplierQueryProductInfoRequestType(baseRequest: new BaseRequest(uid: "10000"), templateId: 1L))
        def result6 = testInstance.validRequest(new SupplierQueryProductInfoRequestType(baseRequest: new BaseRequest(uid: "10000"), templateId: 1L, vendorId: -1L))
        def result7 = testInstance.validRequest(new SupplierQueryProductInfoRequestType(baseRequest: new BaseRequest(uid: "10000"), templateId: 1L, vendorId: 1L))

        then: ""
        Objects.nonNull(result1)
        Objects.equals(result1.getBaseResponse().getCode(), "40001")
        Objects.equals(result1.getBaseResponse().getMessage(), "baseRequest is null; templateId is null; vendorId is null; ")

        Objects.nonNull(result2)
        Objects.equals(result2.getBaseResponse().getCode(), "40001")
        Objects.equals(result2.getBaseResponse().getMessage(), "uid is null; templateId is null; vendorId is null; ")

        Objects.nonNull(result3)
        Objects.equals(result3.getBaseResponse().getCode(), "40001")
        Objects.equals(result3.getBaseResponse().getMessage(), "templateId is null; vendorId is null; ")

        Objects.nonNull(result4)
        Objects.equals(result4.getBaseResponse().getCode(), "40001")
        Objects.equals(result4.getBaseResponse().getMessage(), "templateId is null; vendorId is null; ")

        Objects.nonNull(result5)
        Objects.equals(result5.getBaseResponse().getCode(), "40001")
        Objects.equals(result5.getBaseResponse().getMessage(), "vendorId is null; ")

        Objects.nonNull(result6)
        Objects.equals(result6.getBaseResponse().getCode(), "40001")
        Objects.equals(result6.getBaseResponse().getMessage(), "vendorId is null; ")

        Objects.isNull(result7)
    }

    def "test_business"() {
        setup: ""

        and: ""
        proxy.queryProductInfoList(_ as QueryStandardProductInfoRequestType)\
        >> null\
        >> new QueryStandardProductInfoResponseType(infos: new ArrayList<DataSourceInfo>())\
        >> new QueryStandardProductInfoResponseType(infos: Lists.newArrayList(new DataSourceInfo(dataKey: "1", displayName: "name")))

        when: ""
        def result1 = testInstance.business(new SupplierQueryProductInfoRequestType())
        def result2 = testInstance.business(new SupplierQueryProductInfoRequestType(keyWords: "keyWord", baseRequest: new BaseRequest(uid: "A")))
        def result3 = testInstance.business(new SupplierQueryProductInfoRequestType(keyWords: "keyWord", baseRequest: new BaseRequest(uid: "1")))
        def result4 = testInstance.business(new SupplierQueryProductInfoRequestType(keyWords: "keyWord", baseRequest: new BaseRequest(uid: "1")))

        then: ""
        Objects.nonNull(result1)
        CollectionUtils.isEmpty(result1.getProductInfoList())
        Objects.equals(result1.getBaseResponse().getCode(), "000000")

        Objects.nonNull(result2)
        Objects.equals(result2.getBaseResponse().getCode(), "60000")
        Objects.equals(result2.getBaseResponse().getMessage(), "an error occurs when invoking third-service, fail to quit activity.")

        Objects.nonNull(result3)

        Objects.nonNull(result4)
        Objects.equals(result4.getProductInfoList().get(0).getProductId(), 1L)
        Objects.equals(result4.getProductInfoList().get(0).getProductName(), "name")
    }

}
