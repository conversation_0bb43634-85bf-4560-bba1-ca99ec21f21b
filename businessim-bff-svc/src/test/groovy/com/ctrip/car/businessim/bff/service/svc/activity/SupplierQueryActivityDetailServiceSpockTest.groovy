package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.car.businessim.bff.service.core.config.Config
import com.ctrip.car.businessim.bff.service.core.dto.HolidayDto
import com.ctrip.car.businessim.bff.service.core.enums.ActivityStatusEnum
import com.ctrip.car.businessim.bff.service.core.enums.AttendStatusEnum
import com.ctrip.car.businessim.bff.service.core.util.QConfigUtil
import com.ctrip.car.businessim.bff.service.entity.SupplierQueryActivityDetailRequestType
import com.ctrip.car.businessim.bff.service.proxy.dto.VehicleGroupDTO
import com.ctrip.car.businessim.bff.service.proxy.soa.CarCommodityVendorServiceProxy
import com.ctrip.car.businessim.bff.service.proxy.soa.CarcommoditycommonserviceProxy
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.businessim.bff.service.proxy.soa.SDMarketingServiceProxy
import com.ctrip.car.businessim.bff.service.proxy.soa.StoreServiceProxy
import com.ctrip.car.commodity.common.service.datasync.StandardProductDto
import com.ctrip.car.commodity.store.dto.StoreShortInfoDto
import com.ctrip.car.commodity.vendor.service.contract.dto.VendorInfo
import com.ctrip.car.customer.businessim.contract.dto.activity.ActDeductionStrategyDto
import com.ctrip.car.customer.businessim.contract.dto.activity.ActDto
import com.ctrip.car.customer.businessim.contract.dto.activity.ActTempContentDto
import com.ctrip.car.customer.businessim.contract.dto.activity.ActTemplateDto
import com.ctrip.car.customer.businessim.contract.dto.activity.CityDto
import com.ctrip.car.customer.businessim.contract.dto.activity.CustomContentDto
import com.ctrip.car.customer.businessim.contract.dto.activity.CustomSupplierConditionDto
import com.ctrip.car.customer.businessim.contract.dto.activity.DataSourceInfo
import com.ctrip.car.customer.businessim.contract.dto.activity.LimitDateDto
import com.ctrip.car.customer.businessim.contract.dto.activity.LimitRangeDto
import com.ctrip.car.customer.businessim.contract.dto.activity.ShareDetailDto
import com.ctrip.car.customer.businessim.contract.dto.activity.SupplierConditionDto
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStandardProductInfoRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStandardProductInfoResponseType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryVendorActivityDetailRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryVendorActivityDetailResponseType
import com.ctrip.car.top.BaseRequest
import com.google.common.collect.Lists
import org.mockito.MockedStatic
import org.mockito.Mockito

import spock.lang.Specification
import spock.lang.Unroll

import java.lang.reflect.Method

class SupplierQueryActivityDetailServiceSpockTest extends Specification {

    def config = Mock(Config.class)

    def storeServiceProxy = Mock(StoreServiceProxy.class)

    def carCommodityVendorServiceProxy = Mock(CarCommodityVendorServiceProxy.class)

    def sdMarketingServiceProxy = Mock(SDMarketingServiceProxy.class)

    def proxy = Mock(CustomerBusinessIMServiceProxy.class)

    def carcommoditycommonserviceProxy = Mock(CarcommoditycommonserviceProxy.class)

    def service = new SupplierQueryActivityDetailService(
            proxy: proxy,
            sdMarketingServiceProxy: sdMarketingServiceProxy,
            carCommodityVendorServiceProxy: carCommodityVendorServiceProxy,
            storeServiceProxy: storeServiceProxy,
            config: config,
            carcommoditycommonserviceProxy: carcommoditycommonserviceProxy
    )

    @Unroll
    def "test_business"() {
        setup: ""

        and: ""
        proxy.queryVendorActivityDetail(_ as QueryVendorActivityDetailRequestType) >> null

        and: ""
        carCommodityVendorServiceProxy.queryVendorInfoByVendorId(_ as Long)
                >> new VendorInfo()

        when: ""
        def result1 = service.business(new SupplierQueryActivityDetailRequestType(baseRequest: new BaseRequest(uid: "10000"), activityId: 10000L))

        then: ""
        Objects.nonNull(result1)
        Objects.equals(result1.getActivityId(), 10000L)
    }

    @Unroll
    def "test_validRequest"() {
        setup: ""

        when: ""
        def result1 = service.validRequest(new SupplierQueryActivityDetailRequestType())
        def result2 = service.validRequest(new SupplierQueryActivityDetailRequestType(baseRequest: new BaseRequest()))
        def result3 = service.validRequest(new SupplierQueryActivityDetailRequestType(baseRequest: new BaseRequest(uid: "10000")))
        def result4 = service.validRequest(new SupplierQueryActivityDetailRequestType(baseRequest: new BaseRequest(uid: "10000"), templateId: 1L))

        then: ""
        Objects.nonNull(result1)
        Objects.equals(result1.getBaseResponse().getCode(), "40001")
        Objects.equals(result1.getBaseResponse().getMessage(), "baseRequest is null; templateId is null; ")

        Objects.nonNull(result2)
        Objects.equals(result2.getBaseResponse().getCode(), "40001")
        Objects.equals(result2.getBaseResponse().getMessage(), "uid is null; templateId is null; ")

        Objects.nonNull(result3)
        Objects.equals(result3.getBaseResponse().getCode(), "40001")
        Objects.equals(result3.getBaseResponse().getMessage(), "templateId is null; ")

        Objects.isNull(result4)
    }

    @Unroll
    def "test_buildSoaRequest"() {
        setup: ""
        Method buildSoaRequestMethod = service.getClass().getDeclaredMethod("buildSoaRequest", SupplierQueryActivityDetailRequestType.class);
        buildSoaRequestMethod.setAccessible(true);

        when: ""
        def result1 = (QueryVendorActivityDetailRequestType) buildSoaRequestMethod.invoke(service, new SupplierQueryActivityDetailRequestType(baseRequest: new BaseRequest(uid: "A"), activityId: 10001L))
        def result2 = (QueryVendorActivityDetailRequestType) buildSoaRequestMethod.invoke(service, new SupplierQueryActivityDetailRequestType(baseRequest: new BaseRequest(uid: "1"), activityId: 10002L))

        then: ""
        Objects.nonNull(result1)
        Objects.isNull(result1.getUserId())
        Objects.equals(result1.getActivityId(), 10001L)

        Objects.nonNull(result2)
        Objects.equals(result2.getUserId(), 1L)
        Objects.equals(result2.getActivityId(), 10002L)
    }

    @Unroll
    def "test_buildBffResponse"() {
        setup: ""
        SupplierQueryActivityDetailRequestType requestType = new SupplierQueryActivityDetailRequestType()
        requestType.setVendorId(1L)
        requestType.setBaseRequest(new BaseRequest())
        requestType.getBaseRequest().setUid("1")
        QueryVendorActivityDetailResponseType responseType = new QueryVendorActivityDetailResponseType()
        responseType.setRegisterStatus(1)
        responseType.setActTemplate(new ActTemplateDto())
        responseType.getActTemplate().setStatus(1)
        responseType.getActTemplate().setRegisterStartTime(Calendar.getInstance())
        responseType.getActTemplate().setRegisterEndTime(Calendar.getInstance())
        responseType.getActTemplate().setActivityStartTime(Calendar.getInstance())
        responseType.getActTemplate().setActivityEndTime(Calendar.getInstance())
        responseType.getActTemplate().setActTempContent(new ActTempContentDto())
        responseType.getActTemplate().getActTempContent().setSupplierCondition(new SupplierConditionDto())
        responseType.getActTemplate().getActTempContent().getSupplierCondition().setVehicleGroupIds(Lists.newArrayList(2))
        responseType.getActTemplate().getActTempContent().getSupplierCondition().setCityIds(Lists.newArrayList(2))
        responseType.getActTemplate().getActTempContent().getSupplierCondition().setStoreType(Lists.newArrayList(1))
        responseType.getActTemplate().getActTempContent().getSupplierCondition().setPickUpTimeLimitType(1)
        responseType.getActTemplate().getActTempContent().getSupplierCondition().setExcludeVehicleGroup(false)
        responseType.getActTemplate().getActTempContent().getSupplierCondition().setCityLimit(false)
        responseType.getActTemplate().getActTempContent().getSupplierCondition().setStandardPIdLimit(false)
        responseType.getActTemplate().getActTempContent().getSupplierCondition().setExcludeCity(true)

        and: ""
        CityDto cityDTO = new CityDto()
        cityDTO.setId(2L)
        cityDTO.setName("test")
        (proxy.queryCarCity(_ as List))>>(Lists.newArrayList(cityDTO))

        VehicleGroupDTO groupDTO = new VehicleGroupDTO()
        groupDTO.setCode(2)
        groupDTO.setName("test")
        (sdMarketingServiceProxy.queryAllVehicleGroup())>>(Lists.newArrayList(groupDTO))

        (proxy.queryVendorVehicleGroup(_ as Long, _ as String, _ as BaseRequest))>>(Lists.newArrayList(2))

        StoreShortInfoDto storeDTO = new StoreShortInfoDto()
        storeDTO.setCityId(2)
        (storeServiceProxy.getStoreInfo(_ as Long))>>(Lists.newArrayList(storeDTO))

        (storeServiceProxy.getAllVendorCity(_ as Long))>>(Lists.newArrayList(2))

        when: ""
        def result = service.buildBffResponse(responseType, requestType)

        then: ""
        Objects.nonNull(result)
        Objects.equals(result.getActivityDetail().getSignUpStatus(), 1)
      //  Objects.equals(result.getActivityDetail().getNotice(), "1. 优惠仅可减免租车费;2. 仅在线预付产品可参与活动(不含无忧租一口价);3. 提交报名即视作参加活动并认可活动规则;4. 禁止为参加活动临时调价的行为。")
      //  Objects.equals(result.getActivityDetail().getActivityLimitation(), "城市（test）；车型组（test）；")
    }

    @Unroll
    def "test_buildBffResponse_1"() {
        setup: ""
        SupplierQueryActivityDetailRequestType requestType = new SupplierQueryActivityDetailRequestType()
        requestType.setVendorId(1L)
        requestType.setBaseRequest(new BaseRequest())
        requestType.getBaseRequest().setUid("1")
        QueryVendorActivityDetailResponseType responseType = new QueryVendorActivityDetailResponseType()
        responseType.setRegisterStatus(1)
        responseType.setActTemplate(new ActTemplateDto())
        responseType.getActTemplate().setStatus(1)
        responseType.getActTemplate().setRegisterStartTime(Calendar.getInstance())
        responseType.getActTemplate().setRegisterEndTime(Calendar.getInstance())
        responseType.getActTemplate().setActivityStartTime(Calendar.getInstance())
        responseType.getActTemplate().setActivityEndTime(Calendar.getInstance())
        responseType.getActTemplate().setActTempContent(new ActTempContentDto())
        responseType.getActTemplate().getActTempContent().setSupplierCondition(new SupplierConditionDto())
        responseType.getActTemplate().getActTempContent().getSupplierCondition().setVehicleGroupIds(Lists.newArrayList(2))
        responseType.getActTemplate().getActTempContent().getSupplierCondition().setCityIds(Lists.newArrayList(2))
        responseType.getActTemplate().getActTempContent().getSupplierCondition().setStoreType(Lists.newArrayList(1))
        responseType.getActTemplate().getActTempContent().getSupplierCondition().setPickUpTimeLimitType(1)
        responseType.getActTemplate().getActTempContent().getSupplierCondition().setExcludeVehicleGroup(false)
        responseType.getActTemplate().getActTempContent().getSupplierCondition().setCityLimit(false)
        responseType.getActTemplate().getActTempContent().getSupplierCondition().setStandardPIdLimit(false)

        and: ""
        CityDto cityDTO = new CityDto()
        cityDTO.setId(2L)
        cityDTO.setName("test")
        (proxy.queryCarCity(_ as List))>>(Lists.newArrayList())

        VehicleGroupDTO groupDTO = new VehicleGroupDTO()
        groupDTO.setCode(2)
        groupDTO.setName("test")
        (sdMarketingServiceProxy.queryAllVehicleGroup())>>(Lists.newArrayList(groupDTO))

        (proxy.queryVendorVehicleGroup(_ as Long, _ as String, Mockito.any(BaseRequest.class)))>>(Lists.newArrayList(2))

        StoreShortInfoDto storeDTO = new StoreShortInfoDto()
        storeDTO.setCityId(2)
        (storeServiceProxy.getStoreInfo(_ as Long))>>(Lists.newArrayList(storeDTO))

        (storeServiceProxy.getAllVendorCity(_ as Long))>>(Lists.newArrayList(2))

        when: ""
        def result = service.buildBffResponse(responseType, requestType)

        then: ""
        Objects.nonNull(result)
        Objects.equals(result.getActivityDetail().getSignUpStatus(), 1)
       // Objects.equals(result.getActivityDetail().getNotice(), "1. 优惠仅可减免租车费;2. 仅在线预付产品可参与活动(不含无忧租一口价);3. 提交报名即视作参加活动并认可活动规则;4. 禁止为参加活动临时调价的行为。")
      //  Objects.equals(result.getActivityDetail().getActivityLimitation(), "车型组（test）；")
    }

    @Unroll
    def "test_buildBffResponse_2"() {
        setup: ""
        SupplierQueryActivityDetailRequestType requestType = new SupplierQueryActivityDetailRequestType()
        requestType.setVendorId(1L)
        requestType.setBaseRequest(new BaseRequest(uid: "1"))
        QueryVendorActivityDetailResponseType responseType = new QueryVendorActivityDetailResponseType()
        responseType.setRegisterStatus(1)
        responseType.setActTemplate(new ActTemplateDto(
                productType: 1,
                status: 1,
                registerStartTime: Calendar.getInstance(),
                registerEndTime: Calendar.getInstance(),
                activityStartTime: Calendar.getInstance(),
                activityEndTime: Calendar.getInstance(),
                actTempContent: new ActTempContentDto(
                        supplierCondition: new SupplierConditionDto(
                                cityIds: Lists.newArrayList(2),
                                storeType: Lists.newArrayList(1),
                                pickUpTimeLimitType: 1,
                                excludeVehicleGroup: false,
                                cityLimit: false,
                                standardPIdLimit: false,
                                payModes: Lists.newArrayList(1, 2),
                                tenancyRange: Lists.newArrayList(new LimitRangeDto(floor: 1, upline: 2)),
                                pickUpTimeRange: Lists.newArrayList(new LimitDateDto(start: Calendar.getInstance(), end: Calendar.getInstance())),
                                allowHoliday: true,

                        ),
                        deductionStrategy: new ActDeductionStrategyDto(
                                includeFees: Lists.newArrayList("A"),
                                deductionType: 0,
                                shareWithCoupon: true
                        ),
                        shareDetail: new ShareDetailDto(
                                costShare: 0,
                                shareType: 0,
                                percentage: BigDecimal.ONE,
                                fixedAmount: BigDecimal.ONE
                        ),
                ),
                repetitionPeriod: Lists.newArrayList(1),
                excludeDate: Lists.newArrayList(new LimitDateDto(start: Calendar.getInstance(), end: Calendar.getInstance()))
        ))
        responseType.setActInfo(new ActDto(
                vendorId: 1L,
                status: 1,
                cityIds: Lists.newArrayList(1),
                standardPIds: Lists.newArrayList(1L),
                customContent: new CustomContentDto(supplierCondition: new CustomSupplierConditionDto(true))
        ))

        and: ""
        CityDto cityDTO = new CityDto()
        cityDTO.setId(2L)
        cityDTO.setName("test")
        (proxy.queryCarCity(_ as List))>>(Lists.newArrayList(cityDTO))

        and: ""
        carCommodityVendorServiceProxy.queryVendorInfoByVendorId(_ as Long)
          >> new VendorInfo()

        and: ""
        proxy.queryCarCity(_ as List)
          >> Lists.newArrayList(new CityDto())

        and: ""
        proxy.queryProductInfoList(_ as QueryStandardProductInfoRequestType)
          >> new QueryStandardProductInfoResponseType(infos: Lists.newArrayList(new DataSourceInfo(dataKey: "1", displayName: "name")))

        and: ""
        config.getHolidayList()\
          >> Lists.newArrayList(new HolidayDto(holidayName: "name", start: new Date(), end: new Date()))

        VehicleGroupDTO groupDTO = new VehicleGroupDTO()
        groupDTO.setCode(2)
        groupDTO.setName("test")
        (sdMarketingServiceProxy.queryAllVehicleGroup())>>(Lists.newArrayList(groupDTO))

        (proxy.queryVendorVehicleGroup(_ as Long, _ as String, Mockito.any(BaseRequest.class)))>>(Lists.newArrayList(2))

        StoreShortInfoDto storeDTO = new StoreShortInfoDto()
        storeDTO.setCityId(2)
        (storeServiceProxy.getStoreInfo(_ as Long))>>(Lists.newArrayList(storeDTO))

        (storeServiceProxy.getAllVendorCity(_ as Long))>>(Lists.newArrayList(2))
        (proxy.queryVendorPricePlanCity(_ as Long, _ as BaseRequest) >> (Arrays.asList(43,28,11)))
        when: ""
        def result = service.buildBffResponse(responseType, requestType)

        then: ""
        Objects.nonNull(result)
        Objects.equals(result.getActivityDetail().getSignUpStatus(), 1)
     //   Objects.equals(result.getActivityDetail().getNotice(), "1. 优惠仅可减免租车费;2. 仅在线预付产品可参与活动(不含无忧租一口价);3. 提交报名即视作参加活动并认可活动规则;4. 禁止为参加活动临时调价的行为。")
      //  Objects.equals(result.getActivityDetail().getActivityLimitation(), "城市（test）；车型组（test）；")
    }

    @Unroll
    def "test_buildBffResponse_3"() {
        setup: ""
        SupplierQueryActivityDetailRequestType requestType = new SupplierQueryActivityDetailRequestType()
        requestType.setVendorId(1L)
        requestType.setBaseRequest(new BaseRequest(uid: "1"))
        QueryVendorActivityDetailResponseType responseType = new QueryVendorActivityDetailResponseType()
        responseType.setRegisterStatus(1)
        responseType.setActTemplate(new ActTemplateDto(
                productType: 1,
                status: 1,
                registerStartTime: Calendar.getInstance(),
                registerEndTime: Calendar.getInstance(),
                activityStartTime: Calendar.getInstance(),
                activityEndTime: Calendar.getInstance(),
                actTempContent: new ActTempContentDto(
                        supplierCondition: new SupplierConditionDto(
                                cityIds: Lists.newArrayList(43),
                                storeType: Lists.newArrayList(1),
                                pickUpTimeLimitType: 1,
                                excludeVehicleGroup: false,
                                cityLimit: false,
                                standardPIdLimit: false,
                                payModes: Lists.newArrayList(1, 2),
                                tenancyRange: Lists.newArrayList(new LimitRangeDto(floor: 1, upline: 2)),
                                pickUpTimeRange: Lists.newArrayList(new LimitDateDto(start: Calendar.getInstance(), end: Calendar.getInstance())),
                                allowHoliday: true,

                        ),
                        deductionStrategy: new ActDeductionStrategyDto(
                                includeFees: Lists.newArrayList("A"),
                                deductionType: 1,
                                shareWithCoupon: true,
                                deductionAmountLimit: BigDecimal.TEN
                        ),
                        shareDetail: new ShareDetailDto(
                                costShare: 0,
                                shareType: 0,
                                percentage: BigDecimal.ONE,
                                fixedAmount: BigDecimal.ONE
                        ),
                ),
                repetitionPeriod: Lists.newArrayList(1),
                excludeDate: Lists.newArrayList(new LimitDateDto(start: Calendar.getInstance(), end: Calendar.getInstance()))
        ))
        responseType.setActInfo(new ActDto(
                vendorId: 1L,
                status: 1,
                cityIds: Lists.newArrayList(1),
                standardPIds: Lists.newArrayList(1L),
                customContent: new CustomContentDto(supplierCondition: new CustomSupplierConditionDto(true))
        ))

        and: ""
        CityDto cityDTO = new CityDto()
        cityDTO.setId(2L)
        cityDTO.setName("test")
        (proxy.queryCarCity(_ as List))>>(Lists.newArrayList(cityDTO))

        and: ""
        carCommodityVendorServiceProxy.queryVendorInfoByVendorId(_ as Long)\
          >> new VendorInfo()

        and: ""
        proxy.queryCarCity(_ as List)\
          >> Lists.newArrayList(new CityDto())

        and: ""
        proxy.queryProductInfoList(_ as QueryStandardProductInfoRequestType)\
          >> new QueryStandardProductInfoResponseType(infos: Lists.newArrayList(new DataSourceInfo(dataKey: "1", displayName: "name")))

        and: ""
        config.getHolidayList()\
          >> Lists.newArrayList(new HolidayDto(holidayName: "name", start: new Date(), end: new Date()))

        VehicleGroupDTO groupDTO = new VehicleGroupDTO()
        groupDTO.setCode(2)
        groupDTO.setName("test")
        (sdMarketingServiceProxy.queryAllVehicleGroup())>>(Lists.newArrayList(groupDTO))

        (proxy.queryVendorVehicleGroup(_ as Long, _ as String, Mockito.any(BaseRequest.class)))>>(Lists.newArrayList(2))

        StoreShortInfoDto storeDTO = new StoreShortInfoDto()
        storeDTO.setCityId(2)
        (storeServiceProxy.getStoreInfo(_ as Long))>>(Lists.newArrayList(storeDTO))

        (storeServiceProxy.getAllVendorCity(_ as Long))>>(Lists.newArrayList(2))
        (proxy.queryVendorPricePlanCity(_ as Long, _ as BaseRequest) >>> [(Arrays.asList(43,28,11)), null])
        when: ""
        def result = service.buildBffResponse(responseType, requestType)
        def result2 = service.buildBffResponse(responseType, requestType)
        then: ""
        Objects.nonNull(result)
        Objects.equals(result.getActivityDetail().getDeductionInfo().getDeductionType(), 1)

        Objects.nonNull(result2)
        Objects.equals(result2.getActivityDetail().getDeductionInfo().getDeductionType(), 1)
    }

    @Unroll
    def "test_buildBffResponse_4"() {
        setup: ""
        SupplierQueryActivityDetailRequestType requestType = new SupplierQueryActivityDetailRequestType()
        requestType.setVendorId(1L)
        requestType.setBaseRequest(new BaseRequest(uid: "1"))
        QueryVendorActivityDetailResponseType responseType = new QueryVendorActivityDetailResponseType()
        responseType.setRegisterStatus(1)
        responseType.setActTemplate(null)
        responseType.setActInfo(new ActDto(
                vendorId: 1L,
                status: 1,
                cityIds: Lists.newArrayList(1),
                standardPIds: Lists.newArrayList(1L),
                customContent: new CustomContentDto(supplierCondition: new CustomSupplierConditionDto(true))
        ))

        and: ""
        CityDto cityDTO = new CityDto()
        cityDTO.setId(2L)
        cityDTO.setName("test")
        (proxy.queryCarCity(_ as List))>>(Lists.newArrayList(cityDTO))

        and: ""
        carCommodityVendorServiceProxy.queryVendorInfoByVendorId(_ as Long)
          >> new VendorInfo()

        and: ""
        proxy.queryCarCity(_ as List)
          >> Lists.newArrayList(new CityDto())

        and: ""
        proxy.queryProductInfoList(_ as QueryStandardProductInfoRequestType)
          >> new QueryStandardProductInfoResponseType(infos: Lists.newArrayList(new DataSourceInfo(dataKey: "1", displayName: "name")))

        and: ""
        config.getHolidayList()\
          >> Lists.newArrayList(new HolidayDto(holidayName: "name", start: new Date(), end: new Date()))

        VehicleGroupDTO groupDTO = new VehicleGroupDTO()
        groupDTO.setCode(2)
        groupDTO.setName("test")
        (sdMarketingServiceProxy.queryAllVehicleGroup())>>(Lists.newArrayList(groupDTO))

        (proxy.queryVendorVehicleGroup(_ as Long, _ as String, Mockito.any(BaseRequest.class)))>>(Lists.newArrayList(2))

        StoreShortInfoDto storeDTO = new StoreShortInfoDto()
        storeDTO.setCityId(2)
        (storeServiceProxy.getStoreInfo(_ as Long))>>(Lists.newArrayList(storeDTO))

        (storeServiceProxy.getAllVendorCity(_ as Long))>>(Lists.newArrayList(2))

        when: ""
        def result = service.buildBffResponse(responseType, requestType)

        then: ""
        Objects.nonNull(result)
        Objects.equals(result.getActivityDetail(), null)
    }

    @Unroll
    def "test_buildBffResponse_5"() {
        setup: ""
        SupplierQueryActivityDetailRequestType requestType = new SupplierQueryActivityDetailRequestType()
        requestType.setVendorId(1L)
        requestType.setBaseRequest(new BaseRequest(uid: "1"))
        QueryVendorActivityDetailResponseType responseType = new QueryVendorActivityDetailResponseType()
        responseType.setRegisterStatus(1)
        responseType.setActTemplate(new ActTemplateDto(
                productType: 1,
                status: 1,
                registerStartTime: Calendar.getInstance(),
                registerEndTime: Calendar.getInstance(),
                activityStartTime: Calendar.getInstance(),
                activityEndTime: Calendar.getInstance(),
                actTempContent: new ActTempContentDto(
                        supplierCondition: new SupplierConditionDto(
                                vehicleGroupIds: Lists.newArrayList(2),
                                storeType: Lists.newArrayList(1),
                                pickUpTimeLimitType: 1,
                                excludeVehicleGroup: false,
                                cityLimit: false,
                                standardPIdLimit: false,
                                payModes: Lists.newArrayList(1, 2),
                                tenancyRange: Lists.newArrayList(new LimitRangeDto(floor: 1, upline: 2)),
                                pickUpTimeRange: Lists.newArrayList(new LimitDateDto(start: Calendar.getInstance(), end: Calendar.getInstance())),
                                allowHoliday: true,

                        ),
                        deductionStrategy: new ActDeductionStrategyDto(
                                includeFees: Lists.newArrayList("A"),
                                deductionType: 1,
                                shareWithCoupon: true,
                                deductionAmountLimit: BigDecimal.TEN
                        ),
                        shareDetail: new ShareDetailDto(
                                costShare: 0,
                                shareType: 0,
                                percentage: BigDecimal.ONE,
                                fixedAmount: BigDecimal.ONE
                        ),
                ),
                repetitionPeriod: Lists.newArrayList(1),
                excludeDate: Lists.newArrayList(new LimitDateDto(start: Calendar.getInstance(), end: Calendar.getInstance()))
        ))
        responseType.setActInfo(new ActDto(
                vendorId: 1L,
                status: 1,
                cityIds: Lists.newArrayList(1),
                standardPIds: Lists.newArrayList(1L),
                customContent: new CustomContentDto(supplierCondition: new CustomSupplierConditionDto(true))
        ))

        QueryVendorActivityDetailResponseType responseType2 = new QueryVendorActivityDetailResponseType()
        responseType2.setRegisterStatus(1)
        responseType2.setActTemplate(new ActTemplateDto(
                productType: 1,
                status: 1,
                registerStartTime: Calendar.getInstance(),
                registerEndTime: Calendar.getInstance(),
                activityStartTime: Calendar.getInstance(),
                activityEndTime: Calendar.getInstance(),
                actTempContent: new ActTempContentDto(
                        supplierCondition: new SupplierConditionDto(
                                vehicleGroupIds: Lists.newArrayList(2),
                                storeType: Lists.newArrayList(1),
                                pickUpTimeLimitType: 1,
                                excludeVehicleGroup: false,
                                cityIds: [1,10],
                                excludeCity: true,
                                cityLimit: false,
                                standardPIdLimit: false,
                                payModes: Lists.newArrayList(1, 2),
                                tenancyRange: Lists.newArrayList(new LimitRangeDto(floor: 1, upline: 2)),
                                pickUpTimeRange: Lists.newArrayList(new LimitDateDto(start: Calendar.getInstance(), end: Calendar.getInstance())),
                                allowHoliday: true,

                        ),
                        deductionStrategy: new ActDeductionStrategyDto(
                                includeFees: Lists.newArrayList("A"),
                                deductionType: 1,
                                shareWithCoupon: true,
                                deductionAmountLimit: BigDecimal.TEN
                        ),
                        shareDetail: new ShareDetailDto(
                                costShare: 0,
                                shareType: 0,
                                percentage: BigDecimal.ONE,
                                fixedAmount: BigDecimal.ONE
                        ),
                ),
                repetitionPeriod: Lists.newArrayList(1),
                excludeDate: Lists.newArrayList(new LimitDateDto(start: Calendar.getInstance(), end: Calendar.getInstance()))
        ))
        responseType2.setActInfo(new ActDto(
                vendorId: 1L,
                status: 1,
                cityIds: Lists.newArrayList(1),
                standardPIds: Lists.newArrayList(1L),
                customContent: new CustomContentDto(supplierCondition: new CustomSupplierConditionDto(true))
        ))

        QueryVendorActivityDetailResponseType responseType3 = new QueryVendorActivityDetailResponseType()
        responseType3.setRegisterStatus(1)
        responseType3.setActTemplate(new ActTemplateDto(
                productType: 1,
                status: 1,
                registerStartTime: Calendar.getInstance(),
                registerEndTime: Calendar.getInstance(),
                activityStartTime: Calendar.getInstance(),
                activityEndTime: Calendar.getInstance(),
                actTempContent: new ActTempContentDto(
                        supplierCondition: new SupplierConditionDto(
                                vehicleGroupIds: Lists.newArrayList(2),
                                storeType: Lists.newArrayList(1),
                                pickUpTimeLimitType: 1,
                                excludeVehicleGroup: false,
                                cityIds: [43,28,11],
                                excludeCity: true,
                                cityLimit: false,
                                standardPIdLimit: false,
                                payModes: Lists.newArrayList(1, 2),
                                tenancyRange: Lists.newArrayList(new LimitRangeDto(floor: 1, upline: 2)),
                                pickUpTimeRange: Lists.newArrayList(new LimitDateDto(start: Calendar.getInstance(), end: Calendar.getInstance())),
                                allowHoliday: true,

                        ),
                        deductionStrategy: new ActDeductionStrategyDto(
                                includeFees: Lists.newArrayList("A"),
                                deductionType: 1,
                                shareWithCoupon: true,
                                deductionAmountLimit: BigDecimal.TEN
                        ),
                        shareDetail: new ShareDetailDto(
                                costShare: 0,
                                shareType: 0,
                                percentage: BigDecimal.ONE,
                                fixedAmount: BigDecimal.ONE
                        ),
                ),
                groupId: 100002,
                repetitionPeriod: Lists.newArrayList(1),
                excludeDate: Lists.newArrayList(new LimitDateDto(start: Calendar.getInstance(), end: Calendar.getInstance()))
        ))
        responseType3.setActInfo(new ActDto(
                vendorId: 1L,
                status: 1,
                cityIds: Lists.newArrayList(1),
                standardPIds: Lists.newArrayList(1L),
                customContent: new CustomContentDto(supplierCondition: new CustomSupplierConditionDto(true))
        ))

        QueryVendorActivityDetailResponseType responseType4 = new QueryVendorActivityDetailResponseType()
        responseType4.setRegisterStatus(1)
        responseType4.setActTemplate(new ActTemplateDto(
                productType: 1,
                status: 1,
                registerStartTime: Calendar.getInstance(),
                registerEndTime: Calendar.getInstance(),
                activityStartTime: Calendar.getInstance(),
                activityEndTime: Calendar.getInstance(),
                actTempContent: new ActTempContentDto(
                        supplierCondition: new SupplierConditionDto(
                                vehicleGroupIds: Lists.newArrayList(2),
                                storeType: Lists.newArrayList(1),
                                pickUpTimeLimitType: 1,
                                excludeVehicleGroup: false,
                                cityIds: [43,28,11],
                                excludeCity: true,
                                cityLimit: false,
                                standardPIdLimit: false,
                                payModes: Lists.newArrayList(1, 2),
                                tenancyRange: Lists.newArrayList(new LimitRangeDto(floor: 1, upline: 2)),
                                pickUpTimeRange: Lists.newArrayList(new LimitDateDto(start: Calendar.getInstance(), end: Calendar.getInstance())),
                                allowHoliday: true,

                        ),
                        deductionStrategy: new ActDeductionStrategyDto(
                                includeFees: Lists.newArrayList("A"),
                                deductionType: 1,
                                shareWithCoupon: true,
                                deductionAmountLimit: BigDecimal.TEN
                        ),
                        shareDetail: new ShareDetailDto(
                                costShare: 0,
                                shareType: 0,
                                percentage: BigDecimal.ONE,
                                fixedAmount: BigDecimal.ONE
                        ),
                ),
                groupId: 100002,
                repetitionPeriod: Lists.newArrayList(1),
                excludeDate: Lists.newArrayList(new LimitDateDto(start: Calendar.getInstance(), end: Calendar.getInstance()))
        ))
        responseType4.setActInfo(null)

        and: ""
        CityDto cityDTO = new CityDto()
        cityDTO.setId(2L)
        cityDTO.setName("test")
        (proxy.queryCarCity(_ as List))>>(Lists.newArrayList(cityDTO))

        and: ""
        carCommodityVendorServiceProxy.queryVendorInfoByVendorId(_ as Long)\
          >> new VendorInfo()

        and: ""
        proxy.queryCarCity(_ as List)\
          >> Lists.newArrayList(new CityDto())

        and: ""
        proxy.queryProductInfoList(_ as QueryStandardProductInfoRequestType)\
          >> new QueryStandardProductInfoResponseType(infos: Lists.newArrayList(new DataSourceInfo(dataKey: "1", displayName: "name")))

        and: ""
        config.getHolidayList()\
          >> Lists.newArrayList(new HolidayDto(holidayName: "name", start: new Date(), end: new Date()))

        VehicleGroupDTO groupDTO = new VehicleGroupDTO()
        groupDTO.setCode(2)
        groupDTO.setName("test")
        (sdMarketingServiceProxy.queryAllVehicleGroup())>>(Lists.newArrayList(groupDTO))

        (proxy.queryVendorVehicleGroup(_ as Long, _ as String, Mockito.any(BaseRequest.class)))>>(Lists.newArrayList(2))

        StoreShortInfoDto storeDTO = new StoreShortInfoDto()
        storeDTO.setCityId(2)
        (storeServiceProxy.getStoreInfo(_ as Long))>>(Lists.newArrayList(storeDTO))

        (storeServiceProxy.getAllVendorCity(_ as Long))>>(Lists.newArrayList(2))
        (proxy.queryVendorPricePlanCity(_ as Long, _ as BaseRequest) >> (Arrays.asList(43,28,11)))
        when: ""
        def result = service.buildBffResponse(responseType, requestType)
        def result2 = service.buildBffResponse(responseType2, requestType)
        def result3 = service.buildBffResponse(responseType3, requestType)
        def result4 = service.buildBffResponse(responseType4, requestType)
        then: ""
        Objects.nonNull(result)
        Objects.equals(result.getActivityDetail().getDeductionInfo().getDeductionType(), 1)

        Objects.nonNull(result2)
        Objects.equals(result2.getActivityDetail().getDeductionInfo().getDeductionType(), 1)

        Objects.nonNull(result3)
        Objects.equals(result3.getActivityDetail().getDeductionInfo().getDeductionType(), 1)

        Objects.nonNull(result3)
        Objects.equals(result3.getActivityDetail().getDeductionInfo().getDeductionType(), 1)
    }

    @Unroll
    def "test_enums"() {
        setup: ""

        when: ""
        def result1 = ActivityStatusEnum.valueOfCode(0)
        def result2 = ActivityStatusEnum.valueOfCode(99)
        def result3 = ActivityStatusEnum.valueOfDesc("test")
        def result4 = ActivityStatusEnum.valueOfDesc("1")

        def result5 = AttendStatusEnum.valueOfCode(0)
        def result6 = AttendStatusEnum.valueOfDesc("attended")


        then: ""
        Objects.equals(result1.getCode(), ActivityStatusEnum.VERIFYING.getCode())
        Objects.equals(result2.getCode(), Integer.MIN_VALUE)
        Objects.equals(result3.name(), "DEFAULT")
        Objects.equals(result4.getCode(), Integer.MIN_VALUE)

        Objects.equals(result5.getCode(), AttendStatusEnum.NOT_ATTEND.getCode())
        Objects.equals(result6.getCode(), AttendStatusEnum.ATTENDED.getCode())
    }

    @Unroll
    def "test_copyActDto"() {
        setup: ""

        when: ""
        def acyInfo = new ActDto()
        acyInfo.setActId(1L)
        def result = service.copyActDto(acyInfo)

        then: ""
        Objects.equals(result.getActId(), 1L)
    }

    @Unroll
    def "test_buildBffResponse standardProductCondition"() {
        setup: ""
        SupplierQueryActivityDetailRequestType requestType = new SupplierQueryActivityDetailRequestType()
        requestType.setVendorId(1L)
        requestType.setBaseRequest(new BaseRequest(uid: "1"))

        QueryVendorActivityDetailResponseType responseType = new QueryVendorActivityDetailResponseType()
        responseType.setRegisterStatus(1)
        responseType.setActTemplate(new ActTemplateDto(
                status: 1,
                actTempContent: new ActTempContentDto(
                        supplierCondition: new SupplierConditionDto(
                                pickUpTimeLimitType: 0,
                                cityLimit: false,
                                standardPIdLimit: false,
                                standardProductIds: Lists.newArrayList(1L),
                                excludeStandardProduct: false
                        ),
                        deductionStrategy: new ActDeductionStrategyDto(
                                includeFees: Lists.newArrayList("A"),
                                deductionType: 15,
                                shareWithCoupon: true,
                                deductionAmountLimit: BigDecimal.TEN
                        )
                ),
        ))

        and: ""
        sdMarketingServiceProxy.queryAllVehicleGroup() >> Lists.newArrayList()

        and: ""
        carcommoditycommonserviceProxy.queryStandardProduct(_ as List) >> Lists.newArrayList(new StandardProductDto(), new StandardProductDto())

        when: ""
        def result = service.buildBffResponse(responseType, requestType)

        then: ""
        Objects.nonNull(result)
    }

}