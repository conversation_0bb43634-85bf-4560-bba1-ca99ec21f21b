package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.car.businessim.bff.service.core.mapper.activity.AddQRCodeMapper
import com.ctrip.car.businessim.bff.service.core.mapper.activity.CheckShopMapper
import com.ctrip.car.businessim.bff.service.entity.AddQRCodeBffRequestType
import com.ctrip.car.businessim.bff.service.entity.AddQRCodeBffResponseType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.activity.AddQRCodeRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.AddQRCodeResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class AddQRCodeServiceTest extends Specification {


    def mapper = Mock(AddQRCodeMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def addQRCodeService = new AddQRCodeService(
            proxy: proxy,
            mapper: mapper)
    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        (mapper.from(_ as AddQRCodeBffRequestType))>>(new AddQRCodeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, 1l, "expireTime", "remark"))

        expect:
        addQRCodeService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                              || expectedResult
        new AddQRCodeBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, 1l, "expireTime", "remark") || new AddQRCodeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, 1l, "expireTime", "remark")
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        (proxy.addQRCode(_ as AddQRCodeRequestType))>>(new AddQRCodeResponseType(1l, new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()))

        expect:
        addQRCodeService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                           || expectedResult
        new AddQRCodeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, 1l, "expireTime", "remark") || new AddQRCodeResponseType(1l, new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType())
    }

    @Unroll
    def "convert Response where response=#response and var3=#var3 and var2=#var2 then expect: #expectedResult"() {
        given:
        (mapper.to(_ as AddQRCodeResponseType))>>(new AddQRCodeBffResponseType(1l, new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()))

        expect:
        addQRCodeService.convertResponse(response, var2, var3) == expectedResult

        where:
        response                                                                                                        | var3                                                                                                                                                                                                                                                                 | var2                                                                                                                                                                                                                                                              || expectedResult
        new AddQRCodeResponseType(1l, new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()) | new AddQRCodeBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, 1l, "expireTime", "remark") | new AddQRCodeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, 1l, "expireTime", "remark") || new AddQRCodeBffResponseType(1l, new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType())
    }


}

