package com.ctrip.car.businessim.bff.service.svc


import com.ctrip.car.businessim.bff.service.core.mapper.QueryLastSessionLinkMapper
import com.ctrip.car.businessim.bff.service.entity.QueryLastSessionLinkRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.QueryLastSessionLinkResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.Specification
import spock.lang.Unroll

class QueryLastSessionLinkServiceTest extends Specification {


    def mapper = Mock(QueryLastSessionLinkMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def queryLastSessionLink = new QueryLastSessionLinkService(
            proxy: proxy,
            mapper: mapper)
    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
         (mapper.from(_ as QueryLastSessionLinkRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.QueryLastSessionLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")))


        expect:
        queryLastSessionLink.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                      || expectedResult
        new QueryLastSessionLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) || new com.ctrip.car.customer.businessim.contract.servicetype.QueryLastSessionLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"))
    }

    @Unroll
    def "execute SOA where request2=#request2 then expect: #expectedResult"() {
        given:
        (proxy.queryLastSessionLink( _ as com.ctrip.car.customer.businessim.contract.servicetype.QueryLastSessionLinkRequestType))>>(new QueryLastSessionLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l),1,"",new HashMap<String, String>()))

        expect:
        queryLastSessionLink.executeSOA(request2) == expectedResult

        where:
        request2                                                                                                                                                                                                                                                                                                            || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.QueryLastSessionLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) || new QueryLastSessionLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l),1,"",new HashMap<String, String>())
    }

    @Unroll
    def "convert Response where request=#request and response2=#response2 and request2=#request2 then expect: #expectedResult"() {
        given:
        (mapper.to( _ as QueryLastSessionLinkResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.QueryLastSessionLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l),1,"",new HashMap<String, String>()))

        expect:
        queryLastSessionLink.convertResponse(response2, request2, request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                      | response2                                                                                                                                                                       | request2                                                                                                                                                                                                                                                                                                            || expectedResult
        new QueryLastSessionLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) | new QueryLastSessionLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l),1,"",new HashMap<String, String>()) | new com.ctrip.car.customer.businessim.contract.servicetype.QueryLastSessionLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) || new com.ctrip.car.businessim.bff.service.entity.QueryLastSessionLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l),1,"",new HashMap<String, String>())
    }


}

