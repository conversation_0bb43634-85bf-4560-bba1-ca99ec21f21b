package com.ctrip.car.businessim.bff.service.svc


import com.ctrip.car.businessim.bff.service.core.mapper.GetConsultRecordsLinkMapper
import com.ctrip.car.businessim.bff.service.entity.GetConsultRecordsLinkRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.GetConsultRecordsLinkResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.Specification
import spock.lang.Unroll

class GetConsultRecordsLinkServiceTest extends Specification {


    def mapper = Mock(GetConsultRecordsLinkMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def getConsultRecordsLink = new GetConsultRecordsLinkService(
            proxy: proxy,
            mapper: mapper)
    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
         (mapper.from(_ as GetConsultRecordsLinkRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.GetConsultRecordsLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")))


        expect:
        getConsultRecordsLink.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                      || expectedResult
        new GetConsultRecordsLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) || new com.ctrip.car.customer.businessim.contract.servicetype.GetConsultRecordsLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"))
    }

    @Unroll
    def "execute SOA where request2=#request2 then expect: #expectedResult"() {
        given:
        (proxy.getConsultRecordsLink( _ as com.ctrip.car.customer.businessim.contract.servicetype.GetConsultRecordsLinkRequestType))>>(new GetConsultRecordsLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l),"",new HashMap<String, String>()))

        expect:
        getConsultRecordsLink.executeSOA(request2) == expectedResult

        where:
        request2                                                                                                                                                                                                                                                                                                            || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetConsultRecordsLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) || new GetConsultRecordsLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l),"",new HashMap<String, String>())
    }

    @Unroll
    def "convert Response where request=#request and response2=#response2 and request2=#request2 then expect: #expectedResult"() {
        given:
        (mapper.to( _ as GetConsultRecordsLinkResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.GetConsultRecordsLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l),"",new HashMap<String, String>()))

        expect:
        getConsultRecordsLink.convertResponse(response2, request2, request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                      | response2                                                                                                                                                                       | request2                                                                                                                                                                                                                                                                                                            || expectedResult
        new GetConsultRecordsLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) | new GetConsultRecordsLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l),"",new HashMap<String, String>()) | new com.ctrip.car.customer.businessim.contract.servicetype.GetConsultRecordsLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) || new com.ctrip.car.businessim.bff.service.entity.GetConsultRecordsLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l),"",new HashMap<String, String>())
    }


}

