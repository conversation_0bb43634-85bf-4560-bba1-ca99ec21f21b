package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.car.businessim.bff.service.entity.SupplierQueryActivityTemplateListRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.businessim.bff.service.proxy.soa.StoreServiceProxy
import com.ctrip.car.commodity.store.dto.StoreShortInfoDto
import com.ctrip.car.customer.businessim.contract.dto.activity.ActivityListItem
import com.ctrip.car.customer.businessim.contract.dto.activity.DataSourceInfo
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryActivityListRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryActivityListResponseType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStandardProductInfoRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStandardProductInfoResponseType
import com.ctrip.car.top.BaseRequest
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

import java.lang.reflect.Method

class SupplierQueryActivityTemplateListServiceSpockTest extends Specification {

    def storeServiceProxy = Mock(StoreServiceProxy.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def customerBusinessIMServiceProxy = Mock(CustomerBusinessIMServiceProxy.class)
    def service = new SupplierQueryActivityTemplateListService(
            proxy: proxy,
            storeServiceProxy: storeServiceProxy,
            customerBusinessIMServiceProxy: customerBusinessIMServiceProxy)

    @Unroll
    def "test_business"() {
        setup: ""

        and: ""
        proxy.queryActivityList(_ as QueryActivityListRequestType)\
         >> null

        when: ""
        def result1 = service.business(new SupplierQueryActivityTemplateListRequestType(baseRequest: new BaseRequest(uid: "10000")))

        then: ""
        Objects.nonNull(result1)
        Objects.equals(result1.getBaseResponse().getCode(), "000000")
        Objects.equals(result1.getSortTypeName().isEmpty(),false)
    }

    @Unroll
    def "test_buildSoaRequest"() {
        setup: ""
        Method buildSoaRequestMethod = service.getClass().getDeclaredMethod("buildSoaRequest", SupplierQueryActivityTemplateListRequestType.class);
        buildSoaRequestMethod.setAccessible(true);

        when: ""
        def result2 = (QueryActivityListRequestType) buildSoaRequestMethod.invoke(service, new SupplierQueryActivityTemplateListRequestType(baseRequest: new BaseRequest(uid: "A"), activityName: "Test"))
        def result3 = (QueryActivityListRequestType) buildSoaRequestMethod.invoke(service, new SupplierQueryActivityTemplateListRequestType(baseRequest: new BaseRequest(uid: "10000"), activityName: "Test"))

        then: ""
        Objects.nonNull(result2)
        Objects.equals(result2.getActivityName(), "Test")

        Objects.nonNull(result3)
        Objects.equals(result3.getActivityName(), "Test")
        Objects.equals(result3.getUserId(), 10000L)
    }

    @Unroll
    def "test_buildBffResponse"() {
        setup: ""
        SupplierQueryActivityTemplateListRequestType requestType = new SupplierQueryActivityTemplateListRequestType()
        requestType.setVendorId(1L)
        requestType.setBaseRequest(new BaseRequest())
        requestType.getBaseRequest().setUid("test")

        QueryActivityListResponseType responseType = new QueryActivityListResponseType()
        responseType.setTotal(1)
        ActivityListItem item = new ActivityListItem()
        item.setStoreLimit("test")
        item.setVehicleLimit("test")
        item.setCityLimit("test")
        item.setActivityLimit("Test")
        item.setStoreType(Lists.newArrayList(1))
        item.setVehicleGroupIds(Lists.newArrayList(2))
        item.setCityids(Lists.newArrayList(2))
        item.setExcludeCity(false)
        item.setExcludeVehicleGroup(false)
        item.setVendorId(1L)
        item.setStatus(1)
        item.setProductType(0)

        ActivityListItem item2 = new ActivityListItem()
        item2.setStoreLimit("test")
        item2.setVehicleLimit("test")
        item2.setCityLimit("test")
        item2.setActivityLimit("Test")
        item2.setStoreType(Lists.newArrayList(1))
        item2.setVehicleGroupIds(Lists.newArrayList(2))
        item2.setCityids(Lists.newArrayList(2))
        item2.setExcludeCity(false)
        item2.setExcludeVehicleGroup(false)
        item2.setVendorId(1L)
        item2.setStatus(1)
        item2.setProductType(1)

        ActivityListItem item3 = new ActivityListItem()
        item3.setStoreLimit("test")
        item3.setVehicleLimit("test")
        item3.setCityLimit("test")
        item3.setActivityLimit("Test")
        item3.setStoreType(Lists.newArrayList(1))
        item3.setVehicleGroupIds(Lists.newArrayList(2))
        item3.setCityids(Lists.newArrayList(2))
        item3.setExcludeCity(false)
        item3.setExcludeVehicleGroup(false)
        item3.setVendorId(1L)
        item3.setStatus(1)

        responseType.setActivityList(Lists.newArrayList(item2, item, item3))

        and: ""

        and: ""
        (proxy.queryVendorVehicleGroup(_ as Long, _ as String, _ as BaseRequest)) >> (Lists.newArrayList(2))
        StoreShortInfoDto storeDTO = new StoreShortInfoDto();
        storeDTO.setCityId(2);
        (storeServiceProxy.getStoreInfo(_ as Long)) >> (Lists.newArrayList(storeDTO))
        (storeServiceProxy.getAllVendorCity(_ as Long)) >> (Lists.newArrayList(2))
        (customerBusinessIMServiceProxy.queryVendorPricePlanCity(_ as Long, _ as BaseRequest) >> (null))
        when: ""
        def result = service.buildBffResponse(responseType, requestType)

        then: ""
        Objects.nonNull(result)
        Objects.equals(result.getCount(), 1)
        //Objects.equals(result.getTemplateInfoList().get(0).getSignUpStatusName(), "报名中")
        Objects.equals(result.getTemplateInfoList().get(0).getSignUpStatus(), 1)
        Objects.equals(result.getTemplateInfoList().get(0).getInformation(), "null; null")
    }

    @Unroll
    def "test_buildBffResponse2"() {
        setup: ""
        SupplierQueryActivityTemplateListRequestType requestType = new SupplierQueryActivityTemplateListRequestType()
        requestType.setVendorId(1L)
        requestType.setBaseRequest(new BaseRequest())
        requestType.getBaseRequest().setUid("test")

        QueryActivityListResponseType responseType = new QueryActivityListResponseType()
        responseType.setTotal(1)

        ActivityListItem item1 = new ActivityListItem()
        item1.setStoreLimit("test")
        item1.setVehicleLimit("test")
        item1.setCityLimit("test")
        item1.setActivityLimit("Test")
        item1.setStoreType(Lists.newArrayList(1))
        item1.setVehicleGroupIds(Lists.newArrayList(2))
        item1.setExcludeCity(false)
        item1.setExcludeVehicleGroup(false)
        item1.setVendorId(1L)
        item1.setStatus(1)
        item1.setProductType(1)


        ActivityListItem item2 = new ActivityListItem()
        item2.setStoreLimit("test")
        item2.setVehicleLimit("test")
        item2.setCityLimit("test")
        item2.setActivityLimit("Test")
        item2.setStoreType(Lists.newArrayList(1))
        item2.setVehicleGroupIds(Lists.newArrayList(2))
        item2.setCityids(Lists.newArrayList(2))
        item2.setExcludeCity(false)
        item2.setExcludeVehicleGroup(false)
        item2.setVendorId(1L)
        item2.setStatus(1)
        item2.setProductType(1)

        ActivityListItem item3 = new ActivityListItem()
        item3.setStoreLimit("test")
        item3.setVehicleLimit("test")
        item3.setCityLimit("test")
        item3.setActivityLimit("Test")
        item3.setStoreType(Lists.newArrayList(1))
        item3.setVehicleGroupIds(Lists.newArrayList(2))
        item3.setCityids(Lists.newArrayList(2))
        item3.setExcludeCity(true)
        item3.setExcludeVehicleGroup(false)
        item3.setVendorId(1L)
        item3.setStatus(1)
        item3.setProductType(1)

        ActivityListItem item4 = new ActivityListItem()
        item4.setStoreLimit("test")
        item4.setVehicleLimit("test")
        item4.setCityLimit("test")
        item4.setActivityLimit("Test")
        item4.setStoreType(Lists.newArrayList(1))
        item4.setVehicleGroupIds(Lists.newArrayList(2))
        item4.setCityids(Lists.newArrayList(43,28,11,2))
        item4.setExcludeCity(true)
        item4.setExcludeVehicleGroup(false)
        item4.setVendorId(1L)
        item4.setStatus(1)
        item4.setProductType(1)


        ActivityListItem item5 = new ActivityListItem()
        item5.setStoreLimit("test")
        item5.setVehicleLimit("test")
        item5.setCityLimit("test")
        item5.setActivityLimit("Test")
        item5.setStoreType(Lists.newArrayList(1))
        item5.setVehicleGroupIds(Lists.newArrayList(2))
        item5.setCityids(Lists.newArrayList(43,28,11,2))
        item5.setExcludeCity(false)
        item5.setExcludeVehicleGroup(false)
        item5.setVendorId(1L)
        item5.setStatus(1)
        item5.setProductType(1)

        responseType.setActivityList(Lists.newArrayList(item2, item3, item1, item4, item5))

        and: ""

        and: ""
        (proxy.queryVendorVehicleGroup(_ as Long, _ as String, _ as BaseRequest)) >> (Lists.newArrayList(2))
        StoreShortInfoDto storeDTO = new StoreShortInfoDto();
        storeDTO.setCityId(2);
        (storeServiceProxy.getStoreInfo(_ as Long)) >> (Lists.newArrayList(storeDTO))
        (storeServiceProxy.getAllVendorCity(_ as Long)) >> (Lists.newArrayList(2))
        (customerBusinessIMServiceProxy.queryVendorPricePlanCity(_ as Long, _ as BaseRequest) >> (Arrays.asList(43,28)))
        when: ""
        def result = service.buildBffResponse(responseType, requestType)

        then: ""
        Objects.nonNull(result)
        Objects.equals(result.getCount(), 1)
        //Objects.equals(result.getTemplateInfoList().get(0).getSignUpStatusName(), "报名中")
        Objects.equals(result.getTemplateInfoList().get(0).getSignUpStatus(), 1)
        Objects.equals(result.getTemplateInfoList().get(0).getInformation(), "null; null")
    }

    @Unroll
    def "test_buildBffResponse standardProductCondition"() {
        setup: ""
        SupplierQueryActivityTemplateListRequestType requestType = new SupplierQueryActivityTemplateListRequestType()
        requestType.setVendorId(1L)
        requestType.setBaseRequest(new BaseRequest())
        requestType.getBaseRequest().setUid("1")

        QueryActivityListResponseType responseType = new QueryActivityListResponseType()
        ActivityListItem item1 = new ActivityListItem()
        item1.setStatus(1)
        item1.setStandardProductIds(Lists.newArrayList(1L))
        item1.setExcludeStandardProduct(false)

        ActivityListItem item2 = new ActivityListItem()
        item2.setStatus(1)
        item2.setStandardProductIds(Lists.newArrayList(1L))
        item2.setExcludeStandardProduct(false)

        ActivityListItem item3 = new ActivityListItem()
        item3.setStatus(1)
        item3.setStandardProductIds(Lists.newArrayList(1L))
        item3.setExcludeStandardProduct(true)

        responseType.setActivityList(Lists.newArrayList(item1, item2, item3))

        and: ""
        proxy.queryProductInfoList(_ as QueryStandardProductInfoRequestType)\
                >> new QueryStandardProductInfoResponseType()\
                >> new QueryStandardProductInfoResponseType(infos: Lists.newArrayList(new DataSourceInfo()))

        when: ""
        def result = service.buildBffResponse(responseType, requestType)

        then: ""
        Objects.nonNull(result)
    }

}
