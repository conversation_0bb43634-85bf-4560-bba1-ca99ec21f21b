package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.ListBusinessIMAgentMapper
import com.ctrip.car.businessim.bff.service.entity.ListBusinessIMAgentRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.IMAgentDto
import com.ctrip.car.customer.businessim.contract.dto.StoreRangeDto
import com.ctrip.car.customer.businessim.contract.servicetype.ListBusinessIMAgentResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.Specification
import spock.lang.Unroll

class ListBusinessIMAgentServiceTest extends Specification {


    def mapper = Mock(ListBusinessIMAgentMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def listBusinessIMAgentService = new ListBusinessIMAgentService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        (mapper.from(_ as ListBusinessIMAgentRequestType)) >> (new com.ctrip.car.customer.businessim.contract.servicetype.ListBusinessIMAgentRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), "keyword", 0, 0, "totalBusinessIMAgentCacheKey"))

        expect:
        listBusinessIMAgentService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                           || expectedResult
        new ListBusinessIMAgentRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new com.ctrip.car.businessim.bff.service.dto.StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), "keyword", 0, 0, "totalBusinessIMAgentCacheKey") || new com.ctrip.car.customer.businessim.contract.servicetype.ListBusinessIMAgentRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), "keyword", 0, 0, "totalBusinessIMAgentCacheKey")
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        (proxy.listBusinessIMAgent(_ as com.ctrip.car.customer.businessim.contract.servicetype.ListBusinessIMAgentRequestType)) >> (new ListBusinessIMAgentResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new IMAgentDto(1l, "account", "name", "nickName", 0, Boolean.TRUE)], 0, 0, 0, 0, "totalBusinessIMAgentCacheKey"))

        expect:
        listBusinessIMAgentService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                         || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.ListBusinessIMAgentRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), "keyword", 0, 0, "totalBusinessIMAgentCacheKey") || new ListBusinessIMAgentResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new IMAgentDto(1l, "account", "name", "nickName", 0, Boolean.TRUE)], 0, 0, 0, 0, "totalBusinessIMAgentCacheKey")
    }

    @Unroll
    def "convert Response where request=#request and response=#response and request2=#request2 then expect: #expectedResult"() {
        given:
        (mapper.to(_ as ListBusinessIMAgentResponseType)) >> (new com.ctrip.car.businessim.bff.service.entity.ListBusinessIMAgentResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new com.ctrip.car.businessim.bff.service.dto.IMAgentDto(1l, "account", "name", "nickName", 0, Boolean.TRUE)], 0, 0, 0, 0, "totalBusinessIMAgentCacheKey"))

        expect:
        listBusinessIMAgentService.convertResponse(response, request, request2) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                         | response                                                                                                                                                                                                                                | request2 || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.ListBusinessIMAgentRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), "keyword", 0, 0, "totalBusinessIMAgentCacheKey") | new ListBusinessIMAgentResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new IMAgentDto(1l, "account", "name", "nickName", 0, Boolean.TRUE)], 0, 0, 0, 0, "totalBusinessIMAgentCacheKey") | new ListBusinessIMAgentRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new com.ctrip.car.businessim.bff.service.dto.StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), "keyword", 0, 0, "totalBusinessIMAgentCacheKey") || new com.ctrip.car.businessim.bff.service.entity.ListBusinessIMAgentResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new com.ctrip.car.businessim.bff.service.dto.IMAgentDto(1l, "account", "name", "nickName", 0, Boolean.TRUE)], 0, 0, 0, 0, "totalBusinessIMAgentCacheKey")
    }

}

