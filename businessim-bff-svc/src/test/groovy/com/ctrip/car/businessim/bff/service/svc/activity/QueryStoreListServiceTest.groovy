package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.car.businessim.bff.service.core.mapper.activity.QueryStoreListMapper
import com.ctrip.car.businessim.bff.service.core.mapper.activity.QueryStoreQRCodeListMapper
import com.ctrip.car.businessim.bff.service.dto.activity.ShopStoreInfo
import com.ctrip.car.businessim.bff.service.entity.QueryStoreListBffRequestType
import com.ctrip.car.businessim.bff.service.entity.QueryStoreListBffResponseType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.activity.ShopStoreDto
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStoreListRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStoreListResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class QueryStoreListServiceTest extends Specification {


    def mapper = Mock(QueryStoreListMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def queryStoreListService = new QueryStoreListService(
            proxy: proxy,
            mapper: mapper)
    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        (mapper.from(_ as QueryStoreListBffRequestType))>>(new QueryStoreListRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "keyword", 0, 0))

        expect:
        queryStoreListService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                    || expectedResult
        new QueryStoreListBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "keyword", 0, 0) || new QueryStoreListRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "keyword", 0, 0)
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        (proxy.queryStoreList(_ as QueryStoreListRequestType))>>(new QueryStoreListResponseType([new ShopStoreDto(1l, "storeName")], new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()))

        expect:
        queryStoreListService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                 || expectedResult
        new QueryStoreListRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "keyword", 0, 0) || new QueryStoreListResponseType([new ShopStoreDto(1l, "storeName")], new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType())
    }

    @Unroll
    def "convert Response where response=#response and var3=#var3 and var2=#var2 then expect: #expectedResult"() {
        given:
        (mapper.to(_ as  QueryStoreListResponseType))>>(new QueryStoreListBffResponseType([new ShopStoreInfo(1l, "storeName")], new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()))

        expect:
        queryStoreListService.convertResponse(response, var2, var3) == expectedResult

        where:
        response                                                                                                                                              | var3                                                                                                                                                                                                                                                       | var2                                                                                                                                                                                                                                                    || expectedResult
        new QueryStoreListResponseType([new ShopStoreDto(1l, "storeName")], new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()) | new QueryStoreListBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "keyword", 0, 0) | new QueryStoreListRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "keyword", 0, 0) || new QueryStoreListBffResponseType([new ShopStoreInfo(1l, "storeName")], new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType())
    }


}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme