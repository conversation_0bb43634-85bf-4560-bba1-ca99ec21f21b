package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.GetB2CNewLinkMapper
import com.ctrip.car.businessim.bff.service.core.mapper.GetB2OLinkAndParameterMapper
import com.ctrip.car.businessim.bff.service.entity.GetB2CNewLinkRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.GetB2CNewLinkResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class GetB2CNewLinkServiceTest extends Specification {

    def mapper = Mock(GetB2CNewLinkMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def getB2CNewLinkService = new GetB2CNewLinkService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        (mapper.from(_  as GetB2CNewLinkRequestType)) >> (new com.ctrip.car.customer.businessim.contract.servicetype.GetB2CNewLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")))

        expect:
        getB2CNewLinkService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                               || expectedResult
        new GetB2CNewLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) || new com.ctrip.car.customer.businessim.contract.servicetype.GetB2CNewLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"))
    }

    @Unroll
    def "execute SOA where request2=#request2 then expect: #expectedResult"() {
        given:
        (proxy.GetB2CNewLink(_  as com.ctrip.car.customer.businessim.contract.servicetype.GetB2CNewLinkRequestType)) >> (new GetB2CNewLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "pcIMDownLoadUrl"))

        expect:
        getB2CNewLinkService.executeSOA(request2) == expectedResult

        where:
        request2                                                                                                                                                                                                                                                                                     || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetB2CNewLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) || new GetB2CNewLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "pcIMDownLoadUrl")
    }

    @Unroll
    def "convert Response where request=#request and response2=#response2 and request2=#request2 then expect: #expectedResult"() {
        given:
        (mapper.to(_ as GetB2CNewLinkResponseType)) >> (new com.ctrip.car.businessim.bff.service.entity.GetB2CNewLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "pcIMDownLoadUrl"))

        expect:
        getB2CNewLinkService.convertResponse(response2, request2, request) == expectedResult

        where:
        request                                                                                                                                                                                                                               | response2                                                                                                                          | request2                                                                                                                                                                                                                                                                                     || expectedResult
        new GetB2CNewLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) | new GetB2CNewLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "pcIMDownLoadUrl") | new com.ctrip.car.customer.businessim.contract.servicetype.GetB2CNewLinkRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) || new com.ctrip.car.businessim.bff.service.entity.GetB2CNewLinkResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "pcIMDownLoadUrl")
    }

}

