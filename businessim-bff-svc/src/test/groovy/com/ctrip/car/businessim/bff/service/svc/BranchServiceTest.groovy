package com.ctrip.car.businessim.bff.service.svc

import spock.lang.Specification

class BranchServiceTest extends Specification {
    
    def testInstance = new BranchService()

    def "Test number #input returns #expected"() {
        expect:
        testInstance.processNumber(input) == expected

        where:
        input     | expected
        -1        | "Negative"
        0         | "Zero"
        2         | "Small Prime"
        101       | "Small Prime"
        1009      | "Large Prime"
        4         | "Quadruple"
        8         | "Power of Eight"
        6         | "Even"
    }

    def "Prime test for #number should be #result"() {
        expect:
        testInstance.isPrime(number) == result

        where:
        number | result
        2      | true
        3      | true
        4      | false
        17     | true
        100    | false
        997    | true
    }

    def "Special test for #number should be #result"() {
        expect:
        testInstance.isSpecial(number) == result

        where:
        number | result
        7      | true
        14     | true
        13     | true
        21     | true
        5      | false
        12     | false
    }

}
