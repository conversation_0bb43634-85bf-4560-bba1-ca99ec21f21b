package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.AddIMAgentMapper
import com.ctrip.car.businessim.bff.service.core.mapper.DownloadIMSessionMapper
import com.ctrip.car.businessim.bff.service.entity.AddIMAgentRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.IMAgentDto
import com.ctrip.car.customer.businessim.contract.dto.StoreRangeDto
import com.ctrip.car.customer.businessim.contract.servicetype.AddIMAgentResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class AddIMAgentServiceTest extends Specification {

    def mapper = Mock(AddIMAgentMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def addIMAgentService = new AddIMAgentService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
         (mapper.from(_ as AddIMAgentRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.AddIMAgentRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), "totalBusinessIMAgentCacheKey", new IMAgentDto(1l, "account", "name", "nickName", 0, Boolean.TRUE)))

        expect:
        addIMAgentService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                                                                                              || expectedResult
        new AddIMAgentRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new com.ctrip.car.businessim.bff.service.dto.StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), "totalBusinessIMAgentCacheKey", new com.ctrip.car.businessim.bff.service.dto.IMAgentDto(1l, "account", "name", "nickName", 0, Boolean.TRUE)) || new com.ctrip.car.customer.businessim.contract.servicetype.AddIMAgentRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), "totalBusinessIMAgentCacheKey", new IMAgentDto(1l, "account", "name", "nickName", 0, Boolean.TRUE))
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
         (proxy.addIMAgent(_ as  com.ctrip.car.customer.businessim.contract.servicetype.AddIMAgentRequestType))>>(new AddIMAgentResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l)))

        expect:
        addIMAgentService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                                                                   || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.AddIMAgentRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), "totalBusinessIMAgentCacheKey", new IMAgentDto(1l, "account", "name", "nickName", 0, Boolean.TRUE)) || new AddIMAgentResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l))
    }

    @Unroll
    def "convert Response where request=#request and response=#response and request2=#request2 then expect: #expectedResult"() {
        given:
         (mapper.to(_ as  AddIMAgentResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.AddIMAgentResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l)))

        expect:
        addIMAgentService.convertResponse(response, request, request2) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                                                                   | response                                                                                                     | request2 || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.AddIMAgentRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), "totalBusinessIMAgentCacheKey", new IMAgentDto(1l, "account", "name", "nickName", 0, Boolean.TRUE)) | new AddIMAgentResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l)) | new AddIMAgentRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new com.ctrip.car.businessim.bff.service.dto.StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), "totalBusinessIMAgentCacheKey", new com.ctrip.car.businessim.bff.service.dto.IMAgentDto(1l, "account", "name", "nickName", 0, Boolean.TRUE)) || new com.ctrip.car.businessim.bff.service.entity.AddIMAgentResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l))
    }


}

