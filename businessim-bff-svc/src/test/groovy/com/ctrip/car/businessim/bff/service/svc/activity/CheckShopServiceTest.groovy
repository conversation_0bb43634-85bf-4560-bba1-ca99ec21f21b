package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.car.businessim.bff.service.core.mapper.activity.CheckShopMapper
import com.ctrip.car.businessim.bff.service.core.mapper.activity.DeleteQRCodeMapper
import com.ctrip.car.businessim.bff.service.entity.CheckShopBffRequestType
import com.ctrip.car.businessim.bff.service.entity.CheckShopBffResponseType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.activity.CheckShopRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.CheckShopResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class CheckShopServiceTest extends Specification {


    def mapper = Mock(CheckShopMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def checkShopService = new CheckShopService(
            proxy: proxy,
            mapper: mapper)
    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
         (mapper.from(_ as CheckShopBffRequestType))>>(new CheckShopRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")))

        expect:
        checkShopService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                              || expectedResult
        new CheckShopBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) || new CheckShopRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"))
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
         (proxy.checkShop(_ as CheckShopRequestType))>>(new CheckShopResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()))

        expect:
        checkShopService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                           || expectedResult
        new CheckShopRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) || new CheckShopResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType())
    }

    @Unroll
    def "convert Response where response=#response and var3=#var3 and var2=#var2 then expect: #expectedResult"() {
        given:
         (mapper.to(_ as  CheckShopResponseType))>>(new CheckShopBffResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()))

        expect:
        checkShopService.convertResponse(response, var2, var3) == expectedResult

        where:
        response                                                                                                    | var3                                                                                                                                                                                                                                 | var2                                                                                                                                                                                                                              || expectedResult
        new CheckShopResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()) | new CheckShopBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) | new CheckShopRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) || new CheckShopBffResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType())
    }


}

