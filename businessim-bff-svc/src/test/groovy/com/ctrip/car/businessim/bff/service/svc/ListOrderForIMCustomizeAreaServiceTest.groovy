package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.ListOrderForIMCustomizeAreaMapper
import com.ctrip.car.businessim.bff.service.core.mapper.ListServiceDataIMAgentMapper
import com.ctrip.car.businessim.bff.service.entity.ListOrderForIMCustomizeAreaRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.OrderForIMCustomizeAreaDto
import com.ctrip.car.customer.businessim.contract.servicetype.ListOrderForIMCustomizeAreaResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class ListOrderForIMCustomizeAreaServiceTest extends Specification {


    def mapper = Mock(ListOrderForIMCustomizeAreaMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def listOrderForIMCustomizeAreaService = new ListOrderForIMCustomizeAreaService(
            proxy: proxy,
            mapper: mapper)

    def "test convert Request"() {
        given:
        (mapper.from(_ as ListOrderForIMCustomizeAreaRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.ListOrderForIMCustomizeAreaRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "oldStoreId", "customerUid", "restOrderCacheKey", "tag"))

        when:
        com.ctrip.car.customer.businessim.contract.servicetype.ListOrderForIMCustomizeAreaRequestType result = listOrderForIMCustomizeAreaService.convertRequest(new ListOrderForIMCustomizeAreaRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "oldStoreId", "customerUid", "restOrderCacheKey", "tag"))

        then:
        result == new com.ctrip.car.customer.businessim.contract.servicetype.ListOrderForIMCustomizeAreaRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "oldStoreId", "customerUid", "restOrderCacheKey", "tag")
    }

    def "test execute SOA"() {
        given:
        (proxy.listOrderForIMCustomizeArea(_ as com.ctrip.car.customer.businessim.contract.servicetype.ListOrderForIMCustomizeAreaRequestType))>>(new ListOrderForIMCustomizeAreaResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new OrderForIMCustomizeAreaDto(1l, "productName", "orderDetailLink", 0, "driverName", "pickupTime", "returnTime", 0,"vendorConfirmCode")], "restOrderCacheKey"))

        when:
        ListOrderForIMCustomizeAreaResponseType result = listOrderForIMCustomizeAreaService.executeSOA(new com.ctrip.car.customer.businessim.contract.servicetype.ListOrderForIMCustomizeAreaRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "oldStoreId", "customerUid", "restOrderCacheKey", "tag"))

        then:
        result == new ListOrderForIMCustomizeAreaResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new OrderForIMCustomizeAreaDto(1l, "productName", "orderDetailLink", 0, "driverName", "pickupTime", "returnTime", 0,"vendorConfirmCode")], "restOrderCacheKey")
    }

    def "test convert Response"() {
        given:
        (mapper.to( _ as ListOrderForIMCustomizeAreaResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.ListOrderForIMCustomizeAreaResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new com.ctrip.car.businessim.bff.service.dto.OrderForIMCustomizeAreaDto(1l, "productName", "orderDetailLink", 0, "driverName", "pickupTime", "returnTime", 0,"vendorConfirmCode")], "restOrderCacheKey"))

        when:
        com.ctrip.car.businessim.bff.service.entity.ListOrderForIMCustomizeAreaResponseType result = listOrderForIMCustomizeAreaService.convertResponse(new ListOrderForIMCustomizeAreaResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new OrderForIMCustomizeAreaDto(1l, "productName", "orderDetailLink", 0, "driverName", "pickupTime", "returnTime", 0,"vendorConfirmCode")], "restOrderCacheKey"), new com.ctrip.car.customer.businessim.contract.servicetype.ListOrderForIMCustomizeAreaRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "oldStoreId", "customerUid", "restOrderCacheKey", "tag"), new ListOrderForIMCustomizeAreaRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "oldStoreId", "customerUid", "restOrderCacheKey", "tag"))

        then:
        result == new com.ctrip.car.businessim.bff.service.entity.ListOrderForIMCustomizeAreaResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new com.ctrip.car.businessim.bff.service.dto.OrderForIMCustomizeAreaDto(1l, "productName", "orderDetailLink", 0, "driverName", "pickupTime", "returnTime", 0,"vendorConfirmCode")], "restOrderCacheKey")
    }


}

