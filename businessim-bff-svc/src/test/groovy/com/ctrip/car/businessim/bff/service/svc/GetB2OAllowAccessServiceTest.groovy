package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.GetB2OAllowAccessMapper
import com.ctrip.car.businessim.bff.service.core.mapper.GetB2OLinkAndParameterMapper
import com.ctrip.car.businessim.bff.service.entity.GetB2OAllowAccessRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.GetB2OAllowAccessResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class GetB2OAllowAccessServiceTest extends Specification {


    def mapper = Mock(GetB2OAllowAccessMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def getB2OAllowAccessService = new GetB2OAllowAccessService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
         (mapper.from(_ as GetB2OAllowAccessRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.GetB2OAllowAccessRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l))

        expect:
        getB2OAllowAccessService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                       || expectedResult
        new GetB2OAllowAccessRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) || new com.ctrip.car.customer.businessim.contract.servicetype.GetB2OAllowAccessRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l)
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
         (proxy.getB2OAllowAccess(_ as com.ctrip.car.customer.businessim.contract.servicetype.GetB2OAllowAccessRequestType))>>(new GetB2OAllowAccessResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), Boolean.TRUE))

        expect:
        getB2OAllowAccessService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                              || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetB2OAllowAccessRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) || new GetB2OAllowAccessResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), Boolean.TRUE)
    }

    @Unroll
    def "convert Response where request=#request and response=#response and request2=#request2 then expect: #expectedResult"() {
        given:
         (mapper.to(_ as GetB2OAllowAccessResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.GetB2OAllowAccessResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), Boolean.TRUE))

        expect:
        getB2OAllowAccessService.convertResponse(response, request, request2) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                              | response                                                                                                                          | request2                                                                                                                                                                                                                                      || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetB2OAllowAccessRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) | new GetB2OAllowAccessResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), Boolean.TRUE) | new GetB2OAllowAccessRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) || new com.ctrip.car.businessim.bff.service.entity.GetB2OAllowAccessResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), Boolean.TRUE)
    }


}

