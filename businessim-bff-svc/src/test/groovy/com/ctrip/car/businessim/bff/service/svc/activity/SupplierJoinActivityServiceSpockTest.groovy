package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.car.businessim.bff.service.dto.activity.CustomDeductionStrategy
import com.ctrip.car.businessim.bff.service.dto.activity.CustomDeductionStrategyItem
import com.ctrip.car.businessim.bff.service.dto.activity.CustomSupplierCondition
import com.ctrip.car.businessim.bff.service.entity.SupplierJoinActivityRequestType
import com.ctrip.car.businessim.bff.service.entity.SupplierJoinActivityResponseType
import com.ctrip.car.businessim.bff.service.proxy.soa.CarCommodityVendorServiceProxy
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.commodity.vendor.service.contract.dto.VendorInfo
import com.ctrip.car.customer.businessim.contract.servicetype.activity.JoinActivityRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.JoinActivityResponseType
import com.ctrip.car.top.BaseRequest
import org.junit.Assert
import spock.lang.Specification

import java.lang.reflect.Method

class SupplierJoinActivityServiceSpockTest extends Specification {

    def carCommodityVendorServiceProxy = Mock(CarCommodityVendorServiceProxy.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def testInstance = new SupplierJoinActivityService(
            proxy: proxy,
            carCommodityVendorServiceProxy: carCommodityVendorServiceProxy)


    def "test_validRequest"() {
        setup: ""

        when: ""
        SupplierJoinActivityService var10000 = this.testInstance;
        Method validRequestMethod = var10000.getClass().getDeclaredMethod("validRequest", SupplierJoinActivityRequestType.class);
        validRequestMethod.setAccessible(true);
        SupplierJoinActivityRequestType requestType = new SupplierJoinActivityRequestType();
        SupplierJoinActivityResponseType result1 = (SupplierJoinActivityResponseType) validRequestMethod.invoke(this.testInstance, requestType);

        //==============================================================================================================
        BaseRequest baseRequest = new BaseRequest();
        requestType.setBaseRequest(baseRequest);
        requestType.setTemplateId(-1L);
        SupplierJoinActivityResponseType result2 = (SupplierJoinActivityResponseType) validRequestMethod.invoke(this.testInstance, requestType);

        //==============================================================================================================
        baseRequest.setUid("10011");
        requestType.setTemplateId(10086L);
        SupplierJoinActivityResponseType result3 = (SupplierJoinActivityResponseType) validRequestMethod.invoke(this.testInstance, requestType);

        //==============================================================================================================
        baseRequest.setUid("10011");
        requestType.setTemplateId(10086L);
        requestType.setDeductionPlan(2);
        SupplierJoinActivityResponseType result4 = (SupplierJoinActivityResponseType) validRequestMethod.invoke(this.testInstance, requestType);

        then: ""
        Assert.assertNotNull(result1);
        Assert.assertEquals("40001", result1.getBaseResponse().getCode());
        Assert.assertEquals("baseRequest is null; templateId is null; ", result1.getBaseResponse().getMessage());

        Assert.assertNotNull(result2);
        Assert.assertEquals("40001", result2.getBaseResponse().getCode());
        Assert.assertEquals("uid is null; templateId is null; ", result2.getBaseResponse().getMessage());

        Assert.assertNull(result3);

        Assert.assertNull(result4);
    }

    def "test_buildSoaRequest"() {
        setup: ""

        when: ""
        SupplierJoinActivityService var10000 = this.testInstance;
        Method buildSoaRequestMethod = var10000.getClass().getDeclaredMethod("buildSoaRequest", SupplierJoinActivityRequestType.class);
        buildSoaRequestMethod.setAccessible(true);
        SupplierJoinActivityRequestType requestType = new SupplierJoinActivityRequestType();
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setUid("Uid");
        requestType.setBaseRequest(baseRequest);
        requestType.setVendorId(1L)
        requestType.setCustomSupplierCondition(new CustomSupplierCondition())
        requestType.getCustomSupplierCondition().setAllowHoliday(true)
        VendorInfo vendorInfo = new VendorInfo();
        CarCommodityVendorServiceProxy var6 = this.carCommodityVendorServiceProxy;
        (var6.queryVendorInfoByVendorId(_ as  Long))>>(vendorInfo);
        JoinActivityRequestType result1 = (JoinActivityRequestType) buildSoaRequestMethod.invoke(this.testInstance, requestType);

        //==============================================================================================================
        vendorInfo.setAccessWay("VBKKK");
       (var6.queryVendorInfoByVendorId(_ as  Long))>>(vendorInfo);
        baseRequest.setUid("10001");
        JoinActivityRequestType result2 = (JoinActivityRequestType) buildSoaRequestMethod.invoke(this.testInstance, requestType);

        //==============================================================================================================
        vendorInfo.setAccessWay("VBK");
        (var6.queryVendorInfoByVendorId(_ as  Long))>>(vendorInfo);
        JoinActivityRequestType result3 = (JoinActivityRequestType) buildSoaRequestMethod.invoke(this.testInstance, requestType);

        //==============================================================================================================
        requestType.setDeductionPlan(2);
        CustomDeductionStrategy s = new CustomDeductionStrategy();
        s.setDeductionAmount(new BigDecimal(1));
        s.setDeductionItemList(new ArrayList<CustomDeductionStrategyItem>());
        requestType.setCustomDeductionInfo(s);
        (var6.queryVendorInfoByVendorId(_ as  Long))>>(vendorInfo);
        JoinActivityRequestType result4 = (JoinActivityRequestType) buildSoaRequestMethod.invoke(this.testInstance, requestType);

        then: ""
        Assert.assertNotNull(result1);
        Assert.assertNotNull(result1.getBaseRequest());

        Assert.assertNotNull(result2);
        Assert.assertEquals(10001L, result2.getUserId());

        Assert.assertNotNull(result3);
        Assert.assertEquals(10001L, result3.getUserId());
        Assert.assertTrue("20230619142726-1-null", result3.getVendorCheckCode().endsWith("-1-null"));

        Assert.assertNotNull(result4);
    }

    def "test_buildBffResponse"() {
        setup: ""

        when: ""
        SupplierJoinActivityService var10000 = this.testInstance;
        Method buildBffResponseMethod = var10000.getClass().getDeclaredMethod("buildBffResponse", JoinActivityResponseType.class);
        buildBffResponseMethod.setAccessible(true);
        SupplierJoinActivityResponseType result1 = (SupplierJoinActivityResponseType) buildBffResponseMethod.invoke(this.testInstance, new JoinActivityResponseType());

        then: ""
        Assert.assertNotNull(result1);
        Assert.assertNull(result1.getBaseResponse());
    }

    def "test_business_ResultNull"() {
        setup: ""

        when: ""
        SupplierJoinActivityRequestType requestType = new SupplierJoinActivityRequestType();
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setUid("10001");
        requestType.setBaseRequest(baseRequest);
        VendorInfo vendorInfo = new VendorInfo();
        CarCommodityVendorServiceProxy var10000 = this.carCommodityVendorServiceProxy;
         (var10000.queryVendorInfoByVendorId(_ as  Long))>>(vendorInfo);
        CustomerBusinessIMServiceProxy var5 = this.proxy;
         (var5.joinActivity((JoinActivityRequestType)(_ as JoinActivityRequestType)))>>((Object)null);
        SupplierJoinActivityService var6 = this.testInstance;
        SupplierJoinActivityResponseType result1 = var6.business(requestType);

        then: ""
        Assert.assertNotNull(result1);
        Assert.assertEquals("60000", result1.getBaseResponse().getCode());
    }

    def "test_business"() {
        setup: ""

        when: ""
        SupplierJoinActivityRequestType requestType = new SupplierJoinActivityRequestType();
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setUid("10001");
        requestType.setBaseRequest(baseRequest);
        requestType.setVendorId(1L);
        VendorInfo vendorInfo = new VendorInfo();
        CarCommodityVendorServiceProxy var10000 = this.carCommodityVendorServiceProxy;
        (var10000.queryVendorInfoByVendorId(_ as Long))>>(vendorInfo);
        JoinActivityResponseType responseType = new JoinActivityResponseType();
        CustomerBusinessIMServiceProxy var6 = this.proxy;
        (var6.joinActivity((JoinActivityRequestType)(_ as JoinActivityRequestType)))>>(responseType);
        SupplierJoinActivityService var7 = this.testInstance;
        SupplierJoinActivityResponseType result1 = var7.business(requestType);

        then: ""
        Assert.assertNotNull(result1);
        Assert.assertEquals(1L, result1.getVendorId());
    }

}