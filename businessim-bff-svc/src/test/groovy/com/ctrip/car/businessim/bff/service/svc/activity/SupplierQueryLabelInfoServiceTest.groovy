package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.car.businessim.bff.service.entity.SupplierQueryLabelInfoRequestType
import com.ctrip.car.businessim.bff.service.entity.SupplierQueryLabelInfoResponseType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.activity.DataSourceInfo
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryLabeInfoByConditionRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryLabeInfoByConditionResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.google.common.collect.Lists
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.when

class SupplierQueryLabelInfoServiceTest extends Specification {

    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def supplierQueryLabelInfoService = new SupplierQueryLabelInfoService(
            proxy: proxy,
     )

    def "test_business"() {
        given:
         (proxy.queryLabelInfo(_ as QueryLabeInfoByConditionRequestType))
            >>(null)\
            >>(new QueryLabeInfoByConditionResponseType(baseResponse: new BaseResponse(), infos: Lists.newArrayList(new DataSourceInfo(dataKey: "1", displayName: "1"))))

        when:
        SupplierQueryLabelInfoResponseType result1 = supplierQueryLabelInfoService.business(new SupplierQueryLabelInfoRequestType(new BaseRequest()))
        SupplierQueryLabelInfoResponseType result2 = supplierQueryLabelInfoService.business(new SupplierQueryLabelInfoRequestType(new BaseRequest()))

        then:
        Objects.nonNull(result1)
        Objects.nonNull(result1.getBaseResponse())
        Objects.equals(result1.getBaseResponse().getCode(), "60000")
        Objects.equals(result1.getBaseResponse().getMessage(), "an error occurs when invoking third-service, fail to quit activity.")

        Objects.nonNull(result2)
        Objects.equals(result2.getLabelInfoList().get(0).getLabelId(), 1L)
        Objects.equals(result2.getLabelInfoList().get(0).getLabelName(), "1")
    }

    def "test_validRequest"() {
        when:
        SupplierQueryLabelInfoResponseType result1 = supplierQueryLabelInfoService.validRequest(new SupplierQueryLabelInfoRequestType())
        SupplierQueryLabelInfoResponseType result2 = supplierQueryLabelInfoService.validRequest(new SupplierQueryLabelInfoRequestType(new BaseRequest()))
        SupplierQueryLabelInfoResponseType result3 = supplierQueryLabelInfoService.validRequest(new SupplierQueryLabelInfoRequestType(new BaseRequest(uid: 10086L)))

        then:
        result1 != null
        Objects.equals(result1.getBaseResponse().getCode(), "40001")
        Objects.equals(result1.getBaseResponse().getMessage(), "baseRequest is null; ")

        result2 != null
        Objects.equals(result2.getBaseResponse().getCode(), "40001")
        Objects.equals(result2.getBaseResponse().getMessage(), "uid is null; ")

        result3 == null
    }

}
