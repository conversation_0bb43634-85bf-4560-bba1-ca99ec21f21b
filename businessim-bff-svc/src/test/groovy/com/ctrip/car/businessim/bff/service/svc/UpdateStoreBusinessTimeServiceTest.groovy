package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.UpdateStoreBusinessTimeMapper
import com.ctrip.car.businessim.bff.service.entity.UpdateStoreBusinessTimeRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CarCommodityVendorServiceProxy
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.businessim.bff.service.svc.activity.SupplierJoinActivityService
import com.ctrip.car.customer.businessim.contract.dto.StoreBusinessTimeDto
import com.ctrip.car.customer.businessim.contract.dto.StoreRangeDto
import com.ctrip.car.customer.businessim.contract.servicetype.UpdateStoreBusinessTimeResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class UpdateStoreBusinessTimeServiceTest extends Specification {

    def mapper = Mock(UpdateStoreBusinessTimeMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def updateStoreBusinessTimeService = new UpdateStoreBusinessTimeService(
            proxy: proxy,
            mapper: mapper)


    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        (mapper.from(_ as UpdateStoreBusinessTimeRequestType)) >> (new com.ctrip.car.customer.businessim.contract.servicetype.UpdateStoreBusinessTimeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), new StoreBusinessTimeDto([0], "fromTime", "toTime")))

        expect:
        updateStoreBusinessTimeService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                                                            || expectedResult
        new UpdateStoreBusinessTimeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new com.ctrip.car.businessim.bff.service.dto.StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), new com.ctrip.car.businessim.bff.service.dto.StoreBusinessTimeDto([0], "fromTime", "toTime")) || new com.ctrip.car.customer.businessim.contract.servicetype.UpdateStoreBusinessTimeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), new StoreBusinessTimeDto([0], "fromTime", "toTime"))
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        (proxy.updateStoreBusinessTime(_ as com.ctrip.car.customer.businessim.contract.servicetype.UpdateStoreBusinessTimeRequestType)) >> (new UpdateStoreBusinessTimeResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l)))

        expect:
        updateStoreBusinessTimeService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                                 || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.UpdateStoreBusinessTimeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), new StoreBusinessTimeDto([0], "fromTime", "toTime")) || new UpdateStoreBusinessTimeResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l))
    }

    @Unroll
    def "convert Response where request=#request and response=#response and request2=#request2 then expect: #expectedResult"() {
        given:
        (mapper.to(_ as UpdateStoreBusinessTimeResponseType)) >> (new com.ctrip.car.businessim.bff.service.entity.UpdateStoreBusinessTimeResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l)))

        expect:
        updateStoreBusinessTimeService.convertResponse(response, request, request2) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                                 | response                                                                                                                  | request2                                                                                                                                                                                                                                                                                                                                                                                                                                           || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.UpdateStoreBusinessTimeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), new StoreBusinessTimeDto([0], "fromTime", "toTime")) | new UpdateStoreBusinessTimeResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l)) | new UpdateStoreBusinessTimeRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), new com.ctrip.car.businessim.bff.service.dto.StoreRangeDto([1l], ["restDropdownStoreCacheKeyList"]), new com.ctrip.car.businessim.bff.service.dto.StoreBusinessTimeDto([0], "fromTime", "toTime")) || new com.ctrip.car.businessim.bff.service.entity.UpdateStoreBusinessTimeResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l))
    }

}

