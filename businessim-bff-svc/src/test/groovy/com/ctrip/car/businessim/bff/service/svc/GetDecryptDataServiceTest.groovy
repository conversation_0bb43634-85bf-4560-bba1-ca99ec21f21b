package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.GetDecryptDataMapper
import com.ctrip.car.businessim.bff.service.core.mapper.GetIMGrayStatusMapper
import com.ctrip.car.businessim.bff.service.entity.GetDecryptDataRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.GetDecryptDataResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class GetDecryptDataServiceTest extends Specification {


    def mapper = Mock(GetDecryptDataMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def getDecryptDataService = new GetDecryptDataService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        (mapper.from(  _ as GetDecryptDataRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.GetDecryptDataRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "encryptKey", 0))

        expect:
        getDecryptDataService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                 || expectedResult
        new GetDecryptDataRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "encryptKey", 0) || new com.ctrip.car.customer.businessim.contract.servicetype.GetDecryptDataRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "encryptKey", 0)
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        (proxy.getDecryptData( _ as com.ctrip.car.customer.businessim.contract.servicetype.GetDecryptDataRequestType))>>(new GetDecryptDataResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "decryptKey"))

        expect:
        getDecryptDataService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                        || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetDecryptDataRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "encryptKey", 0) || new GetDecryptDataResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "decryptKey")
    }

    @Unroll
    def "convert Response where request=#request and response=#response and request2=#request2 then expect: #expectedResult"() {
        given:
        (mapper.to( _ as GetDecryptDataResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.GetDecryptDataResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "decryptKey"))

        expect:
        getDecryptDataService.convertResponse(response, request, request2) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                        | response                                                                                                                       | request2                                                                                                                                                                                                                                                || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetDecryptDataRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "encryptKey", 0) | new GetDecryptDataResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "decryptKey") | new GetDecryptDataRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "encryptKey", 0) || new com.ctrip.car.businessim.bff.service.entity.GetDecryptDataResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "decryptKey")
    }


}

