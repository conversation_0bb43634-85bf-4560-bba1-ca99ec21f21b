package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.GetB2OLinkAndParameterMapper
import com.ctrip.car.businessim.bff.service.entity.GetB2OLinkAndParameterRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.GetB2OLinkAndParameterResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.Specification
import spock.lang.Unroll

class GetB2OLinkAndParameterServiceTest extends Specification {


    def mapper = Mock(GetB2OLinkAndParameterMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def getB2OLinkAndParameterService = new GetB2OLinkAndParameterService(
            proxy: proxy,
            mapper: mapper)
    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
         (mapper.from(_ as GetB2OLinkAndParameterRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.GetB2OLinkAndParameterRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, 0, 0, 1l))


        expect:
        getB2OLinkAndParameterService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                      || expectedResult
        new GetB2OLinkAndParameterRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, 0, 0, 1l) || new com.ctrip.car.customer.businessim.contract.servicetype.GetB2OLinkAndParameterRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, 0, 0, 1l)
    }

    @Unroll
    def "execute SOA where request2=#request2 then expect: #expectedResult"() {
        given:
        (proxy.getB2OLinkAndParameter( _ as com.ctrip.car.customer.businessim.contract.servicetype.GetB2OLinkAndParameterRequestType))>>(new GetB2OLinkAndParameterResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "pcJumpUrl", ["appJumpParameter": "appJumpParameter"]))

        expect:
        getB2OLinkAndParameterService.executeSOA(request2) == expectedResult

        where:
        request2                                                                                                                                                                                                                                                                                                            || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetB2OLinkAndParameterRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, 0, 0, 1l) || new GetB2OLinkAndParameterResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "pcJumpUrl", ["appJumpParameter": "appJumpParameter"])
    }

    @Unroll
    def "convert Response where request=#request and response2=#response2 and request2=#request2 then expect: #expectedResult"() {
        given:
        (mapper.to( _ as GetB2OLinkAndParameterResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.GetB2OLinkAndParameterResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "pcJumpUrl", ["appJumpParameter": "appJumpParameter"]))

        expect:
        getB2OLinkAndParameterService.convertResponse(response2, request2, request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                      | response2                                                                                                                                                                       | request2                                                                                                                                                                                                                                                                                                            || expectedResult
        new GetB2OLinkAndParameterRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, 0, 0, 1l) | new GetB2OLinkAndParameterResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "pcJumpUrl", ["appJumpParameter": "appJumpParameter"]) | new com.ctrip.car.customer.businessim.contract.servicetype.GetB2OLinkAndParameterRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, 0, 0, 1l) || new com.ctrip.car.businessim.bff.service.entity.GetB2OLinkAndParameterResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "pcJumpUrl", ["appJumpParameter": "appJumpParameter"])
    }


}

