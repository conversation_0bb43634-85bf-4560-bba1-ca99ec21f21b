package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.car.businessim.bff.service.core.mapper.AddIMAgentMapper
import com.ctrip.car.businessim.bff.service.core.mapper.activity.ShopOpenMapper
import com.ctrip.car.businessim.bff.service.entity.ShopOpenBffRequestType
import com.ctrip.car.businessim.bff.service.entity.ShopOpenBffResponseType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.businessim.bff.service.svc.AddIMAgentService
import com.ctrip.car.customer.businessim.contract.servicetype.activity.ShopOpenRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.ShopOpenResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class ShopOpenServiceTest extends Specification {


    def mapper = Mock(ShopOpenMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def shopOpenService = new ShopOpenService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
         (mapper.from(_ as ShopOpenBffRequestType))>>(new ShopOpenRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")))

        expect:
        shopOpenService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                             || expectedResult
        new ShopOpenBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) || new ShopOpenRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"))
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
         (proxy.shopOpen(_ as ShopOpenRequestType))>> (new ShopOpenResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()))

        expect:
        shopOpenService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                          || expectedResult
        new ShopOpenRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) || new ShopOpenResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType())
    }

    @Unroll
    def "convert Response where response=#response and var3=#var3 and var2=#var2 then expect: #expectedResult"() {
        given:
         (mapper.to(_ as ShopOpenResponseType))>>(new ShopOpenBffResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()))

        expect:
        shopOpenService.convertResponse(response, var2, var3) == expectedResult

        where:
        response                                                                                                   | var3                                                                                                                                                                                                                                | var2                                                                                                                                                                                                                             || expectedResult
        new ShopOpenResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()) | new ShopOpenBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) | new ShopOpenRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName")) || new ShopOpenBffResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType())
    }


}

