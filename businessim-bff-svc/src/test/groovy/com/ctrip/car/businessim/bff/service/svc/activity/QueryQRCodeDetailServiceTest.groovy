package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.car.businessim.bff.service.core.config.Config
import com.ctrip.car.businessim.bff.service.core.mapper.activity.QueryQRCodeDetailMapper
import com.ctrip.car.businessim.bff.service.core.mapper.activity.QueryQRCodeMapper
import com.ctrip.car.businessim.bff.service.dto.activity.QRCodeUrlInfo
import com.ctrip.car.businessim.bff.service.entity.QueryQRCodeDetailBffRequestType
import com.ctrip.car.businessim.bff.service.entity.QueryQRCodeDetailBffResponseType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.activity.QRCodeUrlItem
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryQRCodeDetailRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryQRCodeDetailResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class QueryQRCodeDetailServiceTest extends Specification {


    def qConfig = Mock(Config.class)
    def mapper = Mock(QueryQRCodeDetailMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def queryQRCodeDetailService = new QueryQRCodeDetailService(
            proxy: proxy,
            qConfig: qConfig,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        (mapper.from(_ as QueryQRCodeDetailBffRequestType))>>(new QueryQRCodeDetailRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l))

        expect:
        queryQRCodeDetailService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                          || expectedResult
        new QueryQRCodeDetailBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) || new QueryQRCodeDetailRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l)
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        (proxy.queryQRCodeDetail(_ as  QueryQRCodeDetailRequestType))>>(new QueryQRCodeDetailResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType(), 1l, "qrCode", 1l, 1l, "storeName", 1l, "promoterName", "expireTime", Boolean.TRUE, Boolean.TRUE, "remark", [new QRCodeUrlItem(0, "url")]))

        expect:
        queryQRCodeDetailService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                       || expectedResult
        new QueryQRCodeDetailRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) || new QueryQRCodeDetailResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType(), 1l, "qrCode", 1l, 1l, "storeName", 1l, "promoterName", "expireTime", Boolean.TRUE, Boolean.TRUE, "remark", [new QRCodeUrlItem(0, "url")])
    }

    @Unroll
    def "convert Response where response=#response and var3=#var3 and var2=#var2 then expect: #expectedResult"() {
        given:
        (mapper.to(_ as QueryQRCodeDetailResponseType))>>(new QueryQRCodeDetailBffResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType(), 1l, "qrCode", 1l, 1l, "storeName", 1l, "promoterName", "expireTime", Boolean.TRUE, Boolean.TRUE, "remark", [new QRCodeUrlInfo(0, "url")], "shareContent"))
        (qConfig.getShareContent())>>("shareContent")

        expect:
        queryQRCodeDetailService.convertResponse(response, var2, var3) == expectedResult

        where:
        response                                                                                                                                                                                                                                                      | var3                                                                                                                                                                                                                                             | var2                                                                                                                                                                                                                                          || expectedResult
        new QueryQRCodeDetailResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType(), 1l, "qrCode", 1l, 1l, "storeName", 1l, "promoterName", "expireTime", Boolean.TRUE, Boolean.TRUE, "remark", [new QRCodeUrlItem(0, "url")]) | new QueryQRCodeDetailBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) | new QueryQRCodeDetailRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) || new QueryQRCodeDetailBffResponseType(new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType(), 1l, "qrCode", 1l, 1l, "storeName", 1l, "promoterName", "expireTime", Boolean.TRUE, Boolean.TRUE, "remark", [new QRCodeUrlInfo(0, "url")], "shareContent")
    }


}

