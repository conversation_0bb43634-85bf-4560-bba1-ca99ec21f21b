package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.DownloadIVRCallMapper
import com.ctrip.car.businessim.bff.service.core.mapper.GetB2OAllowAccessMapper
import com.ctrip.car.businessim.bff.service.entity.DownloadIVRCallRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.DownloadIVRCallResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class DownloadIVRCallServiceTest extends Specification {


    def mapper = Mock(DownloadIVRCallMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def downloadIVRCallService = new DownloadIVRCallService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        (mapper.from(_ as DownloadIVRCallRequestType)) >> (new com.ctrip.car.customer.businessim.contract.servicetype.DownloadIVRCallRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "callCreateStartTime", "callCreateEndTime", [1l], "phoneNumber", [0], [0],"callId",[1]))

        expect:
        downloadIVRCallService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                            || expectedResult
        new DownloadIVRCallRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "callCreateStartTime", "callCreateEndTime", [1l], "phoneNumber", [0], [0],"callId",[1]) || new com.ctrip.car.customer.businessim.contract.servicetype.DownloadIVRCallRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "callCreateStartTime", "callCreateEndTime", [1l], "phoneNumber", [0], [0],"callId",[1])
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        (proxy.downloadIVRCall(_ as com.ctrip.car.customer.businessim.contract.servicetype.DownloadIVRCallRequestType)) >> (new DownloadIVRCallResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "exportUrl", 1l))

        expect:
        downloadIVRCallService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                   || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.DownloadIVRCallRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "callCreateStartTime", "callCreateEndTime", [1l], "phoneNumber", [0], [0],"callId",[1]) || new DownloadIVRCallResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "exportUrl", 1l)
    }

    @Unroll
    def "convert Response where request=#request and response=#response and request2=#request2 then expect: #expectedResult"() {
        given:
        (mapper.to(_ as DownloadIVRCallResponseType)) >> (new com.ctrip.car.businessim.bff.service.entity.DownloadIVRCallResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "exportUrl", 1l))

        expect:
        downloadIVRCallService.convertResponse(response, request, request2) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                   | response                                                                                                                           | request2                                                                                                                                                                                                                                                                                                           || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.DownloadIVRCallRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "callCreateStartTime", "callCreateEndTime", [1l], "phoneNumber", [0], [0],"callId",[1]) | new DownloadIVRCallResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "exportUrl", 1l) | new DownloadIVRCallRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "callCreateStartTime", "callCreateEndTime", [1l], "phoneNumber", [0], [0],"callId",[1]) || new com.ctrip.car.businessim.bff.service.entity.DownloadIVRCallResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), "exportUrl", 1l)
    }


}

