package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.ListAgentAllocateRuleMapper
import com.ctrip.car.businessim.bff.service.core.mapper.ListBusinessIMAgentMapper
import com.ctrip.car.businessim.bff.service.entity.ListAgentAllocateRuleRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.VendorAgentAllocateRuleDto
import com.ctrip.car.customer.businessim.contract.servicetype.ListAgentAllocateRuleResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class ListAgentAllocateRuleServiceTest extends Specification {


    def mapper = Mock(ListAgentAllocateRuleMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def listAgentAllocateRuleService = new ListAgentAllocateRuleService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where listAgentAllocateRuleRequestType=#listAgentAllocateRuleRequestType then expect: #expectedResult"() {
        given:
        (mapper.from(_ as ListAgentAllocateRuleRequestType)) >> (new com.ctrip.car.customer.businessim.contract.servicetype.ListAgentAllocateRuleRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l))

        expect:
        listAgentAllocateRuleService.convertRequest(listAgentAllocateRuleRequestType) == expectedResult

        where:
        listAgentAllocateRuleRequestType                                                                                                                                                                                                                  || expectedResult
        new ListAgentAllocateRuleRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) || new com.ctrip.car.customer.businessim.contract.servicetype.ListAgentAllocateRuleRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l)
    }

    @Unroll
    def "execute SOA where listAgentAllocateRuleRequestType=#listAgentAllocateRuleRequestType then expect: #expectedResult"() {
        given:
        (proxy.listAgentAllocateRule(_ as com.ctrip.car.customer.businessim.contract.servicetype.ListAgentAllocateRuleRequestType)) >> (new ListAgentAllocateRuleResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new VendorAgentAllocateRuleDto(1l, "vendorName", 0)]))

        expect:
        listAgentAllocateRuleService.executeSOA(listAgentAllocateRuleRequestType) == expectedResult

        where:
        listAgentAllocateRuleRequestType                                                                                                                                                                                                                                                                         || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.ListAgentAllocateRuleRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) || new ListAgentAllocateRuleResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new VendorAgentAllocateRuleDto(1l, "vendorName", 0)])
    }

    @Unroll
    def "convert Response where listAgentAllocateRuleResponseType=#listAgentAllocateRuleResponseType and listAgentAllocateRuleRequestType=#listAgentAllocateRuleRequestType and listAgentAllocateRuleRequestType2=#listAgentAllocateRuleRequestType2 then expect: #expectedResult"() {
        given:
        (mapper.to(_ as ListAgentAllocateRuleResponseType)) >> (new com.ctrip.car.businessim.bff.service.entity.ListAgentAllocateRuleResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new com.ctrip.car.businessim.bff.service.dto.VendorAgentAllocateRuleDto(1l, "vendorName", 0)]))

        expect:
        listAgentAllocateRuleService.convertResponse(listAgentAllocateRuleResponseType, listAgentAllocateRuleRequestType, listAgentAllocateRuleRequestType2) == expectedResult

        where:
        listAgentAllocateRuleResponseType                                                                                                                                              | listAgentAllocateRuleRequestType                                                                                                                                                                                                                                                                         | listAgentAllocateRuleRequestType2                                                                                                                                                                                                                 || expectedResult
        new ListAgentAllocateRuleResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new VendorAgentAllocateRuleDto(1l, "vendorName", 0)]) | new com.ctrip.car.customer.businessim.contract.servicetype.ListAgentAllocateRuleRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) | new ListAgentAllocateRuleRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l) || new com.ctrip.car.businessim.bff.service.entity.ListAgentAllocateRuleResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new com.ctrip.car.businessim.bff.service.dto.VendorAgentAllocateRuleDto(1l, "vendorName", 0)])
    }


}

