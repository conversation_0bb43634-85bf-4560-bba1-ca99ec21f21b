package com.ctrip.car.businessim.bff.service.svc.activity

import com.ctrip.car.businessim.bff.service.core.mapper.activity.AddPromoterMapper
import com.ctrip.car.businessim.bff.service.core.mapper.activity.AddQRCodeMapper
import com.ctrip.car.businessim.bff.service.entity.AddPromoterBffRequestType
import com.ctrip.car.businessim.bff.service.entity.AddPromoterBffResponseType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.activity.AddPromoterRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.activity.AddPromoterResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class AddPromoterServiceTest extends Specification {
    def mapper = Mock(AddPromoterMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def addPromoterService = new AddPromoterService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        mapper.from(_ as AddPromoterBffRequestType)>>(new AddPromoterRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "promoterName"))

        expect:
        addPromoterService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                    || expectedResult
        new AddPromoterBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "promoterName") || new AddPromoterRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "promoterName")
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        proxy.addPromoter(_ as AddPromoterRequestType)>>(new AddPromoterResponseType(1l, new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()))

        expect:
        addPromoterService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                 || expectedResult
        new AddPromoterRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "promoterName") || new AddPromoterResponseType(1l, new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType())
    }

    @Unroll
    def "convert Response where response=#response and var3=#var3 and var2=#var2 then expect: #expectedResult"() {
        given:
        mapper.to(_ as AddPromoterResponseType)>>(new AddPromoterBffResponseType(1l, new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()))

        expect:
        addPromoterService.convertResponse(response, var2, var3) == expectedResult

        where:
        response                                                                                                          | var3                                                                                                                                                                                                                                                       | var2                                                                                                                                                                                                                                                    || expectedResult
        new AddPromoterResponseType(1l, new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType()) | new AddPromoterBffRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "promoterName") | new AddPromoterRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), 1l, "promoterName") || new AddPromoterBffResponseType(1l, new BaseResponse("code", "message", "returnValue", 1l), new ResponseStatusType())
    }


}

