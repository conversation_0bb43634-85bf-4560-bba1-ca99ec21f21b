package com.ctrip.car.businessim.bff.service.svc

import com.ctrip.car.businessim.bff.service.core.mapper.GetIVRCallReportMapper
import com.ctrip.car.businessim.bff.service.core.mapper.GetOrderDetailLinkMapper
import com.ctrip.car.businessim.bff.service.entity.GetIVRCallReportRequestType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.dto.IVRPhoneRateDto
import com.ctrip.car.customer.businessim.contract.dto.IVRPhoneRateLevelDto
import com.ctrip.car.customer.businessim.contract.dto.VendorIvrPhoneDto
import com.ctrip.car.customer.businessim.contract.servicetype.GetIVRCallReportResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class GetIVRCallReportServiceTest extends Specification {


    def mapper = Mock(GetIVRCallReportMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def getIVRCallReportService = new GetIVRCallReportService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        (mapper.from( _ as GetIVRCallReportRequestType))>>(new com.ctrip.car.customer.businessim.contract.servicetype.GetIVRCallReportRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "callCreateStartTime", "callCreateEndTime", [1l], "phoneNumber", [0], [0], 0, 0,"callId",[1]))

        expect:
        getIVRCallReportService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                   || expectedResult
        new GetIVRCallReportRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "callCreateStartTime", "callCreateEndTime", [1l], "phoneNumber", [0], [0], 0, 0,"callId",[1]) || new com.ctrip.car.customer.businessim.contract.servicetype.GetIVRCallReportRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "callCreateStartTime", "callCreateEndTime", [1l], "phoneNumber", [0], [0], 0, 0,"callId",[1])
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        (proxy.getIVRCallReport( _ as com.ctrip.car.customer.businessim.contract.servicetype.GetIVRCallReportRequestType))>>(new GetIVRCallReportResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new VendorIvrPhoneDto("callId", "callCreateDate", "callSaleType", 1l, "callInitiator", "customerVirtualNumber", "businessNumber", "callStatus", "missedCallReason", "ringDuration", "callStartTime", "callEndTime", "callDuration", "callVendorReview", "presaleConversionStatus", 1l, "storeName",null)], 1l, new IVRPhoneRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new IVRPhoneRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new IVRPhoneRateLevelDto(0, 0, 0, 0)))

        expect:
        getIVRCallReportService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                          || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetIVRCallReportRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "callCreateStartTime", "callCreateEndTime", [1l], "phoneNumber", [0], [0], 0, 0,"callId",[1]) || new GetIVRCallReportResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new VendorIvrPhoneDto("callId", "callCreateDate", "callSaleType", 1l, "callInitiator", "customerVirtualNumber", "businessNumber", "callStatus", "missedCallReason", "ringDuration", "callStartTime", "callEndTime", "callDuration", "callVendorReview", "presaleConversionStatus", 1l, "storeName",null)], 1l, new IVRPhoneRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new IVRPhoneRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new IVRPhoneRateLevelDto(0, 0, 0, 0))
    }

    @Unroll
    def "convert Response where request=#request and response=#response and request2=#request2 then expect: #expectedResult"() {
        given:
        (mapper.to( _ as GetIVRCallReportResponseType))>>(new com.ctrip.car.businessim.bff.service.entity.GetIVRCallReportResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new com.ctrip.car.businessim.bff.service.dto.VendorIvrPhoneDto("callId", "callCreateDate", "callSaleType", 1l, "callInitiator", "customerVirtualNumber", "businessNumber", "callStatus", "missedCallReason", "ringDuration", "callStartTime", "callEndTime", "callDuration", "callVendorReview", "presaleConversionStatus", 1l, "storeName",null)], 1l, new com.ctrip.car.businessim.bff.service.dto.IVRPhoneRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new com.ctrip.car.businessim.bff.service.dto.IVRPhoneRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new com.ctrip.car.businessim.bff.service.dto.IVRPhoneRateLevelDto(0, 0, 0, 0)))

        expect:
        getIVRCallReportService.convertResponse(response, request, request2) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                          | response                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | request2                                                                                                                                                                                                                                                                                                                                 || expectedResult
        new com.ctrip.car.customer.businessim.contract.servicetype.GetIVRCallReportRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "callCreateStartTime", "callCreateEndTime", [1l], "phoneNumber", [0], [0], 0, 0,"callId",[1]) | new GetIVRCallReportResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new VendorIvrPhoneDto("callId", "callCreateDate", "callSaleType", 1l, "callInitiator", "customerVirtualNumber", "businessNumber", "callStatus", "missedCallReason", "ringDuration", "callStartTime", "callEndTime", "callDuration", "callVendorReview", "presaleConversionStatus", 1l, "storeName",null)], 1l, new IVRPhoneRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new IVRPhoneRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new IVRPhoneRateLevelDto(0, 0, 0, 0)) | new GetIVRCallReportRequestType(new BaseRequest("host", "language", "locale", 0, 0, "sf", "cid", "ip", "requestId", "uid", 1l, 1l, [1l], ["roles"], [new KeyValueDTO("key", "value")], "clientType", [1l], "proxyUid", "proxyUserName"), "callCreateStartTime", "callCreateEndTime", [1l], "phoneNumber", [0], [0], 0, 0,"callId",[1]) || new com.ctrip.car.businessim.bff.service.entity.GetIVRCallReportResponseType(new ResponseStatusType(), new BaseResponse("code", "message", "returnValue", 1l), [new com.ctrip.car.businessim.bff.service.dto.VendorIvrPhoneDto("callId", "callCreateDate", "callSaleType", 1l, "callInitiator", "customerVirtualNumber", "businessNumber", "callStatus", "missedCallReason", "ringDuration", "callStartTime", "callEndTime", "callDuration", "callVendorReview", "presaleConversionStatus", 1l, "storeName",null)], 1l, new com.ctrip.car.businessim.bff.service.dto.IVRPhoneRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new com.ctrip.car.businessim.bff.service.dto.IVRPhoneRateDto(0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal), new com.ctrip.car.businessim.bff.service.dto.IVRPhoneRateLevelDto(0, 0, 0, 0))
    }

}

