package com.ctrip.car.businessim.bff.service.svc.activity;

public abstract class ActivityBaseService<REQUEST, RESPONSE> {

    public RESPONSE service(REQUEST request) {
        RESPONSE response = this.validRequest(request);
        if (response != null) {
            return response;
        }
        return this.business(request);
    }

    protected abstract RESPONSE business(REQUEST request);

    protected abstract RESPONSE validRequest(REQUEST request);

}
