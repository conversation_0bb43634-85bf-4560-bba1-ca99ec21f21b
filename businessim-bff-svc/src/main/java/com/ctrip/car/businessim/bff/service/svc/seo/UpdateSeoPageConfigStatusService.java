package com.ctrip.car.businessim.bff.service.svc.seo;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.seo.UpdateSeoPageConfigUpdateMapper;
import com.ctrip.car.businessim.bff.service.dto.seo.UpdateSeoPageConfigStatusBffRequestType;
import com.ctrip.car.businessim.bff.service.dto.seo.UpdateSeoPageConfigStatusBffResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.seo.UpdateSeoPageConfigStatusRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.seo.UpdateSeoPageConfigStatusResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UpdateSeoPageConfigStatusService extends CarSoaService<UpdateSeoPageConfigStatusBffRequestType, UpdateSeoPageConfigStatusBffResponseType, UpdateSeoPageConfigStatusRequestType, UpdateSeoPageConfigStatusResponseType> {

    @Autowired
    private UpdateSeoPageConfigUpdateMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public UpdateSeoPageConfigStatusRequestType convertRequest(UpdateSeoPageConfigStatusBffRequestType request) {
        return mapper.from(request);
    }

    @Override
    public UpdateSeoPageConfigStatusResponseType executeSOA(UpdateSeoPageConfigStatusRequestType request) {
        return proxy.updateSeoPageConfigStatus(request);
    }

    @Override
    public UpdateSeoPageConfigStatusBffResponseType convertResponse(UpdateSeoPageConfigStatusResponseType response, UpdateSeoPageConfigStatusRequestType var2, UpdateSeoPageConfigStatusBffRequestType var3) {
        return mapper.to(response);
    }
}
