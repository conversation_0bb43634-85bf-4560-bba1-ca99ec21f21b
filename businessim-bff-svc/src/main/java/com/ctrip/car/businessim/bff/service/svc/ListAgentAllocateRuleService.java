package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.ListAgentAllocateRuleMapper;
import com.ctrip.car.businessim.bff.service.entity.ListAgentAllocateRuleRequestType;
import com.ctrip.car.businessim.bff.service.entity.ListAgentAllocateRuleResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/12/09
*/
@Service
public class ListAgentAllocateRuleService extends CarSoaService<ListAgentAllocateRuleRequestType, ListAgentAllocateRuleResponseType, com.ctrip.car.customer.businessim.contract.servicetype.ListAgentAllocateRuleRequestType, com.ctrip.car.customer.businessim.contract.servicetype.ListAgentAllocateRuleResponseType> {

    @Autowired
    private ListAgentAllocateRuleMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.ListAgentAllocateRuleRequestType convertRequest(ListAgentAllocateRuleRequestType listAgentAllocateRuleRequestType) {
        return mapper.from(listAgentAllocateRuleRequestType);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.ListAgentAllocateRuleResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.ListAgentAllocateRuleRequestType listAgentAllocateRuleRequestType) {
        return proxy.listAgentAllocateRule(listAgentAllocateRuleRequestType);
    }

    @Override
    public ListAgentAllocateRuleResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.ListAgentAllocateRuleResponseType listAgentAllocateRuleResponseType, com.ctrip.car.customer.businessim.contract.servicetype.ListAgentAllocateRuleRequestType listAgentAllocateRuleRequestType, ListAgentAllocateRuleRequestType listAgentAllocateRuleRequestType2) {
        return mapper.to(listAgentAllocateRuleResponseType);
    }
}
