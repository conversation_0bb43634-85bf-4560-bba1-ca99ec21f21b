package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.GetDecryptDataMapper;
import com.ctrip.car.businessim.bff.service.entity.GetDecryptDataRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetDecryptDataResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/06
*/
@Service
public class GetDecryptDataService extends CarSoaService<GetDecryptDataRequestType, GetDecryptDataResponseType, com.ctrip.car.customer.businessim.contract.servicetype.GetDecryptDataRequestType, com.ctrip.car.customer.businessim.contract.servicetype.GetDecryptDataResponseType> {

    @Autowired
    private GetDecryptDataMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetDecryptDataRequestType convertRequest(GetDecryptDataRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetDecryptDataResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.GetDecryptDataRequestType request) {
        return proxy.getDecryptData(request);
    }

    @Override
    public GetDecryptDataResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.GetDecryptDataResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.GetDecryptDataRequestType request, GetDecryptDataRequestType request2) {
        return mapper.to(response);
    }
}
