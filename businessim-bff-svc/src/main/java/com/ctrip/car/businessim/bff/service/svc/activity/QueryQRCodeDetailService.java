package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.config.Config;
import com.ctrip.car.businessim.bff.service.core.mapper.activity.QueryQRCodeDetailMapper;
import com.ctrip.car.businessim.bff.service.entity.QueryQRCodeDetailBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.QueryQRCodeDetailBffResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryQRCodeDetailRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryQRCodeDetailResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class QueryQRCodeDetailService extends CarSoaService<QueryQRCodeDetailBffRequestType, QueryQRCodeDetailBffResponseType, QueryQRCodeDetailRequestType, QueryQRCodeDetailResponseType> {

    @Autowired
    private QueryQRCodeDetailMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Autowired
    private Config qConfig;

    @Override
    public QueryQRCodeDetailRequestType convertRequest(QueryQRCodeDetailBffRequestType request) {
        return mapper.from(request);
    }

    @Override
    public QueryQRCodeDetailResponseType executeSOA(QueryQRCodeDetailRequestType request) {
        return proxy.queryQRCodeDetail(request);
    }

    @Override
    public QueryQRCodeDetailBffResponseType convertResponse(QueryQRCodeDetailResponseType response, QueryQRCodeDetailRequestType var2, QueryQRCodeDetailBffRequestType var3) {
        QueryQRCodeDetailBffResponseType res = mapper.to(response);
        res.setShareContent(qConfig.getShareContent());
        return res;
    }
}
