package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.QueryLastSessionLinkMapper;
import com.ctrip.car.businessim.bff.service.entity.QueryLastSessionLinkRequestType;
import com.ctrip.car.businessim.bff.service.entity.QueryLastSessionLinkResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/12/16
*/
@Service
public class QueryLastSessionLinkService extends CarSoaService<QueryLastSessionLinkRequestType, QueryLastSessionLinkResponseType, com.ctrip.car.customer.businessim.contract.servicetype.QueryLastSessionLinkRequestType, com.ctrip.car.customer.businessim.contract.servicetype.QueryLastSessionLinkResponseType> {

    @Autowired
    private QueryLastSessionLinkMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.QueryLastSessionLinkRequestType convertRequest(QueryLastSessionLinkRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.QueryLastSessionLinkResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.QueryLastSessionLinkRequestType request2) {
        return proxy.queryLastSessionLink(request2);
    }

    @Override
    public QueryLastSessionLinkResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.QueryLastSessionLinkResponseType response2, com.ctrip.car.customer.businessim.contract.servicetype.QueryLastSessionLinkRequestType request2, QueryLastSessionLinkRequestType request) {
        return mapper.to(response2);
    }
}
