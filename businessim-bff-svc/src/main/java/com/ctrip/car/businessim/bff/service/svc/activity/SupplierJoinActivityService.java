package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.core.util.CalendarFormatUtil;
import com.ctrip.car.businessim.bff.service.core.util.QConfigUtil;
import com.ctrip.car.businessim.bff.service.core.util.ResponseUtil;
import com.ctrip.car.businessim.bff.service.dto.activity.CustomDeductionStrategyItem;
import com.ctrip.car.businessim.bff.service.entity.SupplierJoinActivityRequestType;
import com.ctrip.car.businessim.bff.service.entity.SupplierJoinActivityResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CarCommodityVendorServiceProxy;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.dto.activity.CustomContentDto;
import com.ctrip.car.customer.businessim.contract.dto.activity.CustomDeductionStrategyDto;
import com.ctrip.car.customer.businessim.contract.dto.activity.CustomDeductionStrategyItemDto;
import com.ctrip.car.customer.businessim.contract.dto.activity.CustomSupplierConditionDto;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.JoinActivityRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.JoinActivityResponseType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class SupplierJoinActivityService extends ActivityBaseService<SupplierJoinActivityRequestType, SupplierJoinActivityResponseType> {

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Autowired
    private CarCommodityVendorServiceProxy carCommodityVendorServiceProxy;

    @Override
    protected SupplierJoinActivityResponseType business(SupplierJoinActivityRequestType request) {
        JoinActivityRequestType joinActivityRequestType = this.buildSoaRequest(request);
        JoinActivityResponseType joinActivityResponseType = proxy.joinActivity(joinActivityRequestType);
        if (joinActivityResponseType != null) {
            SupplierJoinActivityResponseType responseType = this.buildBffResponse(joinActivityResponseType);
            responseType.setVendorId(request.getVendorId());
            return responseType;
        } else {
            SupplierJoinActivityResponseType response = new SupplierJoinActivityResponseType();
            response.setBaseResponse(ResponseUtil.thirdServiceError("an error occurs when invoking third-service, fail to join activity."));
            return response;
        }
    }

    private JoinActivityRequestType buildSoaRequest(SupplierJoinActivityRequestType request) {
        JoinActivityRequestType soaRequest = new JoinActivityRequestType();
        soaRequest.setBaseRequest(request.getBaseRequest());
        soaRequest.setTemplateId(request.getTemplateId());
        soaRequest.setCityIds(request.getCityIds());
        soaRequest.setStandardPIds(request.getProductIds());
        soaRequest.setVendorId(request.getVendorId());
        com.ctrip.car.commodity.vendor.service.contract.dto.VendorInfo vendor = carCommodityVendorServiceProxy.queryVendorInfoByVendorId(request.getVendorId());
        if (vendor != null && "VBK".equalsIgnoreCase(vendor.getAccessWay())) {
            String generateVendorCheckCode = CalendarFormatUtil.format_yyyyMMddHHmmss(Calendar.getInstance()) + "-" + request.getVendorId() + "-" + request.getTemplateId();
            soaRequest.setVendorCheckCode(generateVendorCheckCode);
            soaRequest.setVendorCheckCodeList(Collections.singletonList(generateVendorCheckCode));
        } else {
            soaRequest.setVendorCheckCode(request.getSupplierCheckCode());
            soaRequest.setVendorCheckCodeList(request.getVendorCheckCodeList());
        }
        if (NumberUtils.isDigits(request.getBaseRequest().getUid())) {
            soaRequest.setUserId(Long.parseLong(request.getBaseRequest().getUid()));
        }
        soaRequest.setProxyUid(request.getBaseRequest().getProxyUid());
        //todo 要区分是灵活还是建议力度
        if (request.getDeductionPlan() != null && request.getDeductionPlan() == 2) {
            CustomContentDto customContent =new CustomContentDto();
            CustomDeductionStrategyDto deductionStrategy = new CustomDeductionStrategyDto();
            deductionStrategy.setDeductionAmount(request.getCustomDeductionInfo().getDeductionAmount());
            if (CollectionUtils.isNotEmpty(request.getCustomDeductionInfo().getDeductionItemList())) {
                List<CustomDeductionStrategyItemDto> items = request.getCustomDeductionInfo().getDeductionItemList().stream().map(d -> {
                    CustomDeductionStrategyItemDto customDeductionStrategyItemDto = new CustomDeductionStrategyItemDto();
                    customDeductionStrategyItemDto.setStartValue(d.getStartValue());
                    customDeductionStrategyItemDto.setDeductionAmount(d.getDeductionAmount());
                    return customDeductionStrategyItemDto;
                }).collect(Collectors.toList());
                deductionStrategy.setDeductionStrategyList(items);
            }
            customContent.setDeductionStrategy(deductionStrategy);
            soaRequest.setCustomContent(customContent);
        }
        if (Objects.nonNull(request.getCustomSupplierCondition())) {
            if (Objects.isNull(soaRequest.getCustomContent())) {
                soaRequest.setCustomContent(new CustomContentDto());
            }
            soaRequest.getCustomContent().setSupplierCondition(new CustomSupplierConditionDto());
            soaRequest.getCustomContent().getSupplierCondition().setAllowHoliday(request.getCustomSupplierCondition().isAllowHoliday());
        }

        return soaRequest;
    }

    private SupplierJoinActivityResponseType buildBffResponse(JoinActivityResponseType soaResponse) {
        SupplierJoinActivityResponseType bffResponse = new SupplierJoinActivityResponseType();
        bffResponse.setBaseResponse(soaResponse.getBaseResponse());
        bffResponse.setActivityId(soaResponse.getActivityId());
        return bffResponse;
    }

    protected SupplierJoinActivityResponseType validRequest(SupplierJoinActivityRequestType request) {
        String errorMsg = "";
        if (request.getBaseRequest() == null) {
            errorMsg += "baseRequest is null; ";
        } else {
            if (request.getBaseRequest().getUid() == null) {
                errorMsg += "uid is null; ";
            }
        }
        if (request.getTemplateId() == null || request.getTemplateId() < 0L) {
            errorMsg += "templateId is null; ";
        }
        //todo 优惠力度校验，灵活的时候必填
        if (request.getDeductionPlan()!=null && request.getDeductionPlan() == 2) {
            //优惠力度必填
            if (Arrays.asList(0,1).contains(request.getDeductionType())){
                if (request.getCustomDeductionInfo() == null || request.getCustomDeductionInfo().getDeductionAmount() == null) {
                    errorMsg = QConfigUtil.getChineseOrDefault("ErrorMsg_Deduction", "");
                }
                //超过可设置范围
                if (request.getCustomDeductionInfo().getDeductionAmount().compareTo(request.getCustomDeductionInfo().getMaxAmount()) > 0 || request.getCustomDeductionInfo().getDeductionAmount().compareTo(request.getCustomDeductionInfo().getMinAmount()) < 0) {
                    errorMsg = QConfigUtil.getChineseOrDefault("ErrorMsg_Range", "");
                }
            }
            if (Arrays.asList(11,12).contains(request.getDeductionType())){
                if (request.getCustomDeductionInfo() == null ||  CollectionUtils.isEmpty(request.getCustomDeductionInfo().getDeductionItemList()) ||
                        request.getCustomDeductionInfo().getDeductionItemList().stream().anyMatch(d -> d.getDeductionAmount() == null)) {
                    errorMsg = QConfigUtil.getChineseOrDefault("ErrorMsg_Deduction", "");
                }
                //超过可设置范围
                boolean deductionRange = request.getCustomDeductionInfo().getDeductionItemList().stream().anyMatch(d -> d.getDeductionAmount().compareTo(d.getMaxAmount()) > 0 || d.getDeductionAmount().compareTo(d.getMinAmount()) < 0);
                if (deductionRange) {
                    errorMsg = QConfigUtil.getChineseOrDefault("ErrorMsg_Range", "");
                }
                boolean deductionSort = false;
                for (int i = 0; i < request.getCustomDeductionInfo().getDeductionItemList().size(); i++) {
                    CustomDeductionStrategyItem deductionInfoItemNext = request.getCustomDeductionInfo().getDeductionItemList().get(i);
                    if (i > 0) {
                        CustomDeductionStrategyItem deductionInfoItem = request.getCustomDeductionInfo().getDeductionItemList().get(i - 1);
                        if (deductionInfoItem.getDeductionAmount().compareTo(deductionInfoItemNext.getDeductionAmount()) > 0) {
                            deductionSort = true;
                            break;
                        }
                    }
                }
                if (deductionSort) {
                    errorMsg = QConfigUtil.getChineseOrDefault("ErrorMsg_Sort", "");
                }
            }
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            SupplierJoinActivityResponseType response = new SupplierJoinActivityResponseType();
            response.setBaseResponse(ResponseUtil.parameterError(errorMsg));
            return response;
        } else {
            return null;
        }
    }

}
