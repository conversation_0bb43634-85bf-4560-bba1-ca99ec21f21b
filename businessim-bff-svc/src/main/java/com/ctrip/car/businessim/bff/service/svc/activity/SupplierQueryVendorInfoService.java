package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.core.util.ResponseUtil;
import com.ctrip.car.businessim.bff.service.dto.activity.VendorInfo;
import com.ctrip.car.businessim.bff.service.entity.SupplierQueryVendorInfoRequestType;
import com.ctrip.car.businessim.bff.service.entity.SupplierQueryVendorInfoResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryVendorInfoByUserIdRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryVendorInfoByUserIdResponseType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.stream.Collectors;

@Service
public class SupplierQueryVendorInfoService extends ActivityBaseService<SupplierQueryVendorInfoRequestType, SupplierQueryVendorInfoResponseType> {

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    protected SupplierQueryVendorInfoResponseType business(SupplierQueryVendorInfoRequestType request) {
        QueryVendorInfoByUserIdRequestType queryVendorInfoByUserIdRequestType = this.buildSoaRequest(request);
        QueryVendorInfoByUserIdResponseType soaResponse = proxy.queryVendorInfoByUserId(queryVendorInfoByUserIdRequestType);
        if (soaResponse != null) {
            return this.buildBffResponse(soaResponse);
        } else {
            SupplierQueryVendorInfoResponseType response = new SupplierQueryVendorInfoResponseType();
            response.setBaseResponse(ResponseUtil.thirdServiceError("an error occurs when invoking third-service, fail to quit activity."));
            return response;
        }
    }

    private QueryVendorInfoByUserIdRequestType buildSoaRequest(SupplierQueryVendorInfoRequestType request) {
        QueryVendorInfoByUserIdRequestType soaRequest = new QueryVendorInfoByUserIdRequestType();
        soaRequest.setBaseRequest(request.getBaseRequest());
        if (NumberUtils.isDigits(request.getBaseRequest().getUid())) {
            soaRequest.setUserId(Long.parseLong(request.getBaseRequest().getUid()));
        }
        return soaRequest;
    }

    private SupplierQueryVendorInfoResponseType buildBffResponse(QueryVendorInfoByUserIdResponseType soaResponse) {
        SupplierQueryVendorInfoResponseType bffResponse = new SupplierQueryVendorInfoResponseType();
        if (CollectionUtils.isNotEmpty(soaResponse.getInfos())) {
            bffResponse.setVendorInfoList(
                    soaResponse.getInfos().stream()
                            .map(dataSourceInfo -> {
                                VendorInfo vendorInfo = new VendorInfo();
                                vendorInfo.setVendorId(Long.parseLong(dataSourceInfo.getDataKey()));
                                vendorInfo.setVendorName(dataSourceInfo.getDataKey() + "-" + dataSourceInfo.getDisplayName());
                                return vendorInfo;
                            })
                            .collect(Collectors.toList()));
        } else {
            bffResponse.setVendorInfoList(new ArrayList<>());
        }
        bffResponse.setBaseResponse(soaResponse.getBaseResponse());
        return bffResponse;
    }

    @Override
    protected SupplierQueryVendorInfoResponseType validRequest(SupplierQueryVendorInfoRequestType request) {
        String errorMsg = "";
        if (request.getBaseRequest() == null) {
            errorMsg += "baseRequest is null; ";
        } else {
            if (request.getBaseRequest().getUid() == null) {
                errorMsg += "uid is null; ";
            }
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            SupplierQueryVendorInfoResponseType response = new SupplierQueryVendorInfoResponseType();
            response.setBaseResponse(ResponseUtil.parameterError(errorMsg));
            return response;
        } else {
            return null;
        }
    }

}