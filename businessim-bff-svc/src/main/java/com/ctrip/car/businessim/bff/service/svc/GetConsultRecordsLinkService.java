package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.GetConsultRecordsLinkMapper;
import com.ctrip.car.businessim.bff.service.entity.GetConsultRecordsLinkRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetConsultRecordsLinkResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/12/16
*/
@Service
public class GetConsultRecordsLinkService extends CarSoaService<GetConsultRecordsLinkRequestType, GetConsultRecordsLinkResponseType, com.ctrip.car.customer.businessim.contract.servicetype.GetConsultRecordsLinkRequestType, com.ctrip.car.customer.businessim.contract.servicetype.GetConsultRecordsLinkResponseType> {

    @Autowired
    private GetConsultRecordsLinkMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetConsultRecordsLinkRequestType convertRequest(GetConsultRecordsLinkRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetConsultRecordsLinkResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.GetConsultRecordsLinkRequestType request2) {
        return proxy.getConsultRecordsLink(request2);
    }

    @Override
    public GetConsultRecordsLinkResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.GetConsultRecordsLinkResponseType response2, com.ctrip.car.customer.businessim.contract.servicetype.GetConsultRecordsLinkRequestType request2, GetConsultRecordsLinkRequestType request) {
        return mapper.to(response2);
    }
}
