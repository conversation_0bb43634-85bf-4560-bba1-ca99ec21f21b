package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.GetStoreBusinessTimeMapper;
import com.ctrip.car.businessim.bff.service.entity.GetStoreBusinessTimeRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetStoreBusinessTimeResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/06
*/
@Service
public class GetStoreBusinessTimeService extends CarSoaService<GetStoreBusinessTimeRequestType, GetStoreBusinessTimeResponseType, com.ctrip.car.customer.businessim.contract.servicetype.GetStoreBusinessTimeRequestType, com.ctrip.car.customer.businessim.contract.servicetype.GetStoreBusinessTimeResponseType> {

    @Autowired
    private GetStoreBusinessTimeMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetStoreBusinessTimeRequestType convertRequest(GetStoreBusinessTimeRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetStoreBusinessTimeResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.GetStoreBusinessTimeRequestType request) {
        return proxy.getStoreBusinessTime(request);
    }

    @Override
    public GetStoreBusinessTimeResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.GetStoreBusinessTimeResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.GetStoreBusinessTimeRequestType request, GetStoreBusinessTimeRequestType request2) {
        return mapper.to(response);
    }
}
