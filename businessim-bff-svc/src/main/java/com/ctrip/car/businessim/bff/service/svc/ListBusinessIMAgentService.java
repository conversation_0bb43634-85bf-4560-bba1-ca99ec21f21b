package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.ListBusinessIMAgentMapper;
import com.ctrip.car.businessim.bff.service.entity.ListBusinessIMAgentRequestType;
import com.ctrip.car.businessim.bff.service.entity.ListBusinessIMAgentResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/06
*/
@Service
public class ListBusinessIMAgentService extends CarSoaService<ListBusinessIMAgentRequestType, ListBusinessIMAgentResponseType, com.ctrip.car.customer.businessim.contract.servicetype.ListBusinessIMAgentRequestType, com.ctrip.car.customer.businessim.contract.servicetype.ListBusinessIMAgentResponseType> {

    @Autowired
    private ListBusinessIMAgentMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.ListBusinessIMAgentRequestType convertRequest(ListBusinessIMAgentRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.ListBusinessIMAgentResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.ListBusinessIMAgentRequestType request) {
        return proxy.listBusinessIMAgent(request);
    }

    @Override
    public ListBusinessIMAgentResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.ListBusinessIMAgentResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.ListBusinessIMAgentRequestType request, ListBusinessIMAgentRequestType request2) {
        return mapper.to(response);
    }
}
