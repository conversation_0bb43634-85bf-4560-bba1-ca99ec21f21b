package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.core.util.ResponseUtil;
import com.ctrip.car.businessim.bff.service.dto.activity.LabelInfo;
import com.ctrip.car.businessim.bff.service.entity.SupplierQueryLabelInfoRequestType;
import com.ctrip.car.businessim.bff.service.entity.SupplierQueryLabelInfoResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryLabeInfoByConditionRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryLabeInfoByConditionResponseType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.stream.Collectors;

@Service
public class SupplierQueryLabelInfoService extends ActivityBaseService<SupplierQueryLabelInfoRequestType, SupplierQueryLabelInfoResponseType> {

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    protected SupplierQueryLabelInfoResponseType business(SupplierQueryLabelInfoRequestType request) {
        QueryLabeInfoByConditionRequestType queryOpenCityInfoByVendorIdsRequestType = this.buildSoaRequest(request);
        QueryLabeInfoByConditionResponseType soaResponse = proxy.queryLabelInfo(queryOpenCityInfoByVendorIdsRequestType);
        if (soaResponse != null) {
            return this.buildBffResponse(soaResponse);
        } else {
            SupplierQueryLabelInfoResponseType response = new SupplierQueryLabelInfoResponseType();
            response.setBaseResponse(ResponseUtil.thirdServiceError("an error occurs when invoking third-service, fail to quit activity."));
            return response;
        }
    }

    private QueryLabeInfoByConditionRequestType buildSoaRequest(SupplierQueryLabelInfoRequestType request) {
        QueryLabeInfoByConditionRequestType soaRequest = new QueryLabeInfoByConditionRequestType();
        soaRequest.setBaseRequest(request.getBaseRequest());
        soaRequest.setVendorId(request.getBaseRequest().getVendorId());
        if (NumberUtils.isDigits(request.getBaseRequest().getUid())) {
            soaRequest.setUserId(Long.parseLong(request.getBaseRequest().getUid()));
        }
        return soaRequest;
    }

    private SupplierQueryLabelInfoResponseType buildBffResponse(QueryLabeInfoByConditionResponseType soaResponse) {
        SupplierQueryLabelInfoResponseType bffResponse = new SupplierQueryLabelInfoResponseType();
        if (CollectionUtils.isNotEmpty(soaResponse.getInfos())) {
            bffResponse.setLabelInfoList(
                    soaResponse.getInfos().stream()
                            .map(dataSourceInfo -> {
                                LabelInfo labelInfo = new LabelInfo();
                                labelInfo.setLabelId(Long.parseLong(dataSourceInfo.getDataKey()));
                                labelInfo.setLabelName(dataSourceInfo.getDisplayName());
                                return labelInfo;
                            })
                            .collect(Collectors.toList()));
        } else {
            bffResponse.setLabelInfoList(new ArrayList<>());
        }
        bffResponse.setBaseResponse(soaResponse.getBaseResponse());
        return bffResponse;
    }

    @Override
    protected SupplierQueryLabelInfoResponseType validRequest(SupplierQueryLabelInfoRequestType request) {
        String errorMsg = "";
        if (request.getBaseRequest() == null) {
            errorMsg += "baseRequest is null; ";
        } else {
            if (request.getBaseRequest().getUid() == null) {
                errorMsg += "uid is null; ";
            }
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            SupplierQueryLabelInfoResponseType response = new SupplierQueryLabelInfoResponseType();
            response.setBaseResponse(ResponseUtil.parameterError(errorMsg));
            return response;
        } else {
            return null;
        }
    }

}
