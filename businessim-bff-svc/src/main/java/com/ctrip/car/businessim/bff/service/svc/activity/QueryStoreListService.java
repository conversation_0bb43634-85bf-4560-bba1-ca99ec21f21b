package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.activity.QueryStoreListMapper;
import com.ctrip.car.businessim.bff.service.entity.QueryStoreListBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.QueryStoreListBffResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStoreListRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStoreListResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class QueryStoreListService extends CarSoaService<QueryStoreListBffRequestType, QueryStoreListBffResponseType, QueryStoreListRequestType, QueryStoreListResponseType> {

    @Autowired
    private QueryStoreListMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public QueryStoreListRequestType convertRequest(QueryStoreListBffRequestType request) {
        return mapper.from(request);
    }

    @Override
    public QueryStoreListResponseType executeSOA(QueryStoreListRequestType request) {
        return proxy.queryStoreList(request);
    }

    @Override
    public QueryStoreListBffResponseType convertResponse(QueryStoreListResponseType response, QueryStoreListRequestType var2, QueryStoreListBffRequestType var3) {
        return mapper.to(response);
    }
}
