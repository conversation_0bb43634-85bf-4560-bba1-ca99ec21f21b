package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.activity.AddQRCodeMapper;
import com.ctrip.car.businessim.bff.service.entity.AddQRCodeBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.AddQRCodeBffResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.AddQRCodeRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.AddQRCodeResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AddQRCodeService extends CarSoaService<AddQRCodeBffRequestType, AddQRCodeBffResponseType, AddQRCodeRequestType, AddQRCodeResponseType>{

    @Autowired
    private AddQRCodeMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public AddQRCodeRequestType convertRequest(AddQRCodeBffRequestType request) {
        return mapper.from(request);
    }

    @Override
    public AddQRCodeResponseType executeSOA(AddQRCodeRequestType request) {
        return proxy.addQRCode(request);
    }

    @Override
    public AddQRCodeBffResponseType convertResponse(AddQRCodeResponseType response, AddQRCodeRequestType var2, AddQRCodeBffRequestType var3) {
        return mapper.to(response);
    }
}
