package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.core.util.ResponseUtil;
import com.ctrip.car.businessim.bff.service.entity.SupplierModifyActivityRequestType;
import com.ctrip.car.businessim.bff.service.entity.SupplierModifyActivityResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.ModifyActivityRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.ModifyActivityResponseType;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SupplierModifyActivityService extends ActivityBaseService<SupplierModifyActivityRequestType, SupplierModifyActivityResponseType> {

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    protected SupplierModifyActivityResponseType business(SupplierModifyActivityRequestType request) {
        ModifyActivityRequestType modifyActivityRequestType = this.buildSoaRequest(request);
        ModifyActivityResponseType modifyActivityResponseType = proxy.modifyActivity(modifyActivityRequestType);
        if (modifyActivityResponseType != null) {
            SupplierModifyActivityResponseType responseType = this.buildBffResponse(modifyActivityResponseType);
            responseType.setActivityId(request.getActivityId());
            responseType.setVendorId(request.getVendorId());
            return responseType;
        } else {
            SupplierModifyActivityResponseType response = new SupplierModifyActivityResponseType();
            response.setBaseResponse(ResponseUtil.thirdServiceError("an error occurs when invoking third-service, fail to modify activity."));
            return response;
        }
    }

    private ModifyActivityRequestType buildSoaRequest(SupplierModifyActivityRequestType request) {
        ModifyActivityRequestType soaRequest = new ModifyActivityRequestType();
        soaRequest.setBaseRequest(request.getBaseRequest());
        soaRequest.setActivityId(request.getActivityId());
        soaRequest.setVendorId(request.getVendorId());
        if (NumberUtils.isDigits(request.getBaseRequest().getUid())) {
            soaRequest.setUserId(Long.parseLong(request.getBaseRequest().getUid()));
        }
        soaRequest.setCityIds(request.getCityIds());
        soaRequest.setStandardPIds(request.getProductIds());
        soaRequest.setVendorCheckCode(request.getSupplierCheckCode());
        soaRequest.setProxyUid(request.getBaseRequest().getProxyUid());
        soaRequest.setVendorCheckCodeList(request.getVendorCheckCodeList());
        return soaRequest;
    }

    private SupplierModifyActivityResponseType buildBffResponse(ModifyActivityResponseType soaResponse) {
        SupplierModifyActivityResponseType bffResponse = new SupplierModifyActivityResponseType();
        bffResponse.setBaseResponse(soaResponse.getBaseResponse());
        return bffResponse;
    }

    @Override
    protected SupplierModifyActivityResponseType validRequest(SupplierModifyActivityRequestType request) {
        String errorMsg = "";
        if (request.getBaseRequest() == null) {
            errorMsg += "baseRequest is null; ";
        } else {
            if (request.getBaseRequest().getUid() == null) {
                errorMsg += "uid is null; ";
            }
        }
        if (request.getActivityId() == null) {
            errorMsg += "activityId is null; ";
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            SupplierModifyActivityResponseType response = new SupplierModifyActivityResponseType();
            response.setBaseResponse(ResponseUtil.parameterError(errorMsg));
            return response;
        } else {
            return null;
        }
    }

}
