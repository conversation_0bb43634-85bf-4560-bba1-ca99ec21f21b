package com.ctrip.car.businessim.bff.service.svc;

public class BranchService {
    
    public String processNumber(int number) {
        if (number < 0) {
            return "Negative";
        } else if (number == 0) {
            return "Zero";
        } else if (isPrime(number)) {
            return number > 1000 ? "Large Prime" : "Small Prime";
        } else if (number % 2 == 0) {
            if (number % 4 == 0) {
                return number % 8 == 0 ? "Power of Eight" : "Quadruple";
            }
            return "Even";
        }
        return "";
    }

    protected boolean isPrime(int n) {
        if (n <= 1) return false;
        for (int i = 2; i <= Math.sqrt(n); i++) {
            if (n % i == 0) return false;
        }
        return true;
    }

    protected boolean isSpecial(int n) {
        return n % 7 == 0 || n % 13 == 0;
    }

}
