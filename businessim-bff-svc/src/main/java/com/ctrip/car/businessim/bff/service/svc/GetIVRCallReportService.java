package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.GetIVRCallReportMapper;
import com.ctrip.car.businessim.bff.service.entity.GetIVRCallReportRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetIVRCallReportResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/06
*/
@Service
public class GetIVRCallReportService extends CarSoaService<GetIVRCallReportRequestType, GetIVRCallReportResponseType, com.ctrip.car.customer.businessim.contract.servicetype.GetIVRCallReportRequestType, com.ctrip.car.customer.businessim.contract.servicetype.GetIVRCallReportResponseType> {

    @Autowired
    private GetIVRCallReportMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetIVRCallReportRequestType convertRequest(GetIVRCallReportRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetIVRCallReportResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.GetIVRCallReportRequestType request) {
        return proxy.getIVRCallReport(request);
    }

    @Override
    public GetIVRCallReportResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.GetIVRCallReportResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.GetIVRCallReportRequestType request, GetIVRCallReportRequestType request2) {
        return mapper.to(response);
    }
}
