package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.ListServiceDataDropdownStoreMapper;
import com.ctrip.car.businessim.bff.service.entity.ListServiceDataDropdownStoreRequestType;
import com.ctrip.car.businessim.bff.service.entity.ListServiceDataDropdownStoreResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/06
*/
@Service
public class ListServiceDataDropdownStoreService extends CarSoaService<ListServiceDataDropdownStoreRequestType, ListServiceDataDropdownStoreResponseType, com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataDropdownStoreRequestType, com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataDropdownStoreResponseType> {

    @Autowired
    private ListServiceDataDropdownStoreMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataDropdownStoreRequestType convertRequest(ListServiceDataDropdownStoreRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataDropdownStoreResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataDropdownStoreRequestType request) {
        return proxy.listServiceDataDropdownStore(request);
    }

    @Override
    public ListServiceDataDropdownStoreResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataDropdownStoreResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataDropdownStoreRequestType request, ListServiceDataDropdownStoreRequestType request2) {
        return mapper.to(response);
    }
}
