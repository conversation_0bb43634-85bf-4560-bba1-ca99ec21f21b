package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.DownloadIMSessionMapper;
import com.ctrip.car.businessim.bff.service.entity.DownloadIMSessionRequestType;
import com.ctrip.car.businessim.bff.service.entity.DownloadIMSessionResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/06
*/
@Service
public class DownloadIMSessionService extends CarSoaService<DownloadIMSessionRequestType, DownloadIMSessionResponseType, com.ctrip.car.customer.businessim.contract.servicetype.DownloadIMSessionRequestType, com.ctrip.car.customer.businessim.contract.servicetype.DownloadIMSessionResponseType> {

    @Autowired
    private DownloadIMSessionMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.DownloadIMSessionRequestType convertRequest(DownloadIMSessionRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.DownloadIMSessionResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.DownloadIMSessionRequestType request) {
        return proxy.downloadIMSession(request);
    }

    @Override
    public DownloadIMSessionResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.DownloadIMSessionResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.DownloadIMSessionRequestType request, DownloadIMSessionRequestType request2) {
        return mapper.to(response);
    }
}
