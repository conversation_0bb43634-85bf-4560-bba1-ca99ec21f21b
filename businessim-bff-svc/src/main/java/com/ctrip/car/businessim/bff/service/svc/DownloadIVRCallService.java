package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.DownloadIVRCallMapper;
import com.ctrip.car.businessim.bff.service.entity.DownloadIVRCallRequestType;
import com.ctrip.car.businessim.bff.service.entity.DownloadIVRCallResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/06
*/
@Service
public class DownloadIVRCallService extends CarSoaService<DownloadIVRCallRequestType, DownloadIVRCallResponseType, com.ctrip.car.customer.businessim.contract.servicetype.DownloadIVRCallRequestType, com.ctrip.car.customer.businessim.contract.servicetype.DownloadIVRCallResponseType> {

    @Autowired
    private DownloadIVRCallMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.DownloadIVRCallRequestType convertRequest(DownloadIVRCallRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.DownloadIVRCallResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.DownloadIVRCallRequestType request) {
        return proxy.downloadIVRCall(request);
    }

    @Override
    public DownloadIVRCallResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.DownloadIVRCallResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.DownloadIVRCallRequestType request, DownloadIVRCallRequestType request2) {
        return mapper.to(response);
    }
}
