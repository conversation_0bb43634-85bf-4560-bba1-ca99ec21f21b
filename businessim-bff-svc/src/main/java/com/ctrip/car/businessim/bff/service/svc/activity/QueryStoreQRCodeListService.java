package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.activity.QueryStoreQRCodeListMapper;
import com.ctrip.car.businessim.bff.service.entity.QueryStoreQRCodeListBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.QueryStoreQRCodeListBffResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStoreQRCodeListRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStoreQRCodeListResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class QueryStoreQRCodeListService extends CarSoaService<QueryStoreQRCodeListBffRequestType, QueryStoreQRCodeListBffResponseType, QueryStoreQRCodeListRequestType, QueryStoreQRCodeListResponseType>{

    @Autowired
    private QueryStoreQRCodeListMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public QueryStoreQRCodeListRequestType convertRequest(QueryStoreQRCodeListBffRequestType request) {
        return mapper.from(request);
    }

    @Override
    public QueryStoreQRCodeListResponseType executeSOA(QueryStoreQRCodeListRequestType request) {
        return proxy.queryStoreQRCodeList(request);
    }

    @Override
    public QueryStoreQRCodeListBffResponseType convertResponse(QueryStoreQRCodeListResponseType response, QueryStoreQRCodeListRequestType var2, QueryStoreQRCodeListBffRequestType var3) {
        return mapper.to(response);
    }
}
