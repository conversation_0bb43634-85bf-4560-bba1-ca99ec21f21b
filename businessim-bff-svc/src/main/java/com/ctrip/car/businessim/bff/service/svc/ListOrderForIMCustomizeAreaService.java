package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.ListOrderForIMCustomizeAreaMapper;
import com.ctrip.car.businessim.bff.service.entity.ListOrderForIMCustomizeAreaRequestType;
import com.ctrip.car.businessim.bff.service.entity.ListOrderForIMCustomizeAreaResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/06
*/
@Service
public class ListOrderForIMCustomizeAreaService extends CarSoaService<ListOrderForIMCustomizeAreaRequestType, ListOrderForIMCustomizeAreaResponseType, com.ctrip.car.customer.businessim.contract.servicetype.ListOrderForIMCustomizeAreaRequestType, com.ctrip.car.customer.businessim.contract.servicetype.ListOrderForIMCustomizeAreaResponseType> {

    @Autowired
    private ListOrderForIMCustomizeAreaMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.ListOrderForIMCustomizeAreaRequestType convertRequest(ListOrderForIMCustomizeAreaRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.ListOrderForIMCustomizeAreaResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.ListOrderForIMCustomizeAreaRequestType request) {
        return proxy.listOrderForIMCustomizeArea(request);
    }

    @Override
    public ListOrderForIMCustomizeAreaResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.ListOrderForIMCustomizeAreaResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.ListOrderForIMCustomizeAreaRequestType request, ListOrderForIMCustomizeAreaRequestType request2) {
        return mapper.to(response);
    }
}
