package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.activity.ShopOpenMapper;
import com.ctrip.car.businessim.bff.service.entity.ShopOpenBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.ShopOpenBffResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.ShopOpenRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.ShopOpenResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ShopOpenService extends CarSoaService<ShopOpenBffRequestType, ShopOpenBffResponseType, ShopOpenRequestType, ShopOpenResponseType> {

    @Autowired
    private ShopOpenMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public ShopOpenRequestType convertRequest(ShopOpenBffRequestType request) {
        return mapper.from(request);
    }

    @Override
    public ShopOpenResponseType executeSOA(ShopOpenRequestType request) {
        return proxy.shopOpen(request);
    }

    @Override
    public ShopOpenBffResponseType convertResponse(ShopOpenResponseType response, ShopOpenRequestType var2, ShopOpenBffRequestType var3) {
        return mapper.to(response);
    }
}
