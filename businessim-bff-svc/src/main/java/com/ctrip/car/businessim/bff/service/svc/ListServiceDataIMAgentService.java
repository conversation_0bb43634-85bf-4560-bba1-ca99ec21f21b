package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.ListServiceDataIMAgentMapper;
import com.ctrip.car.businessim.bff.service.entity.ListServiceDataIMAgentRequestType;
import com.ctrip.car.businessim.bff.service.entity.ListServiceDataIMAgentResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/06
*/
@Service
public class ListServiceDataIMAgentService extends CarSoaService<ListServiceDataIMAgentRequestType, ListServiceDataIMAgentResponseType, com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataIMAgentRequestType, com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataIMAgentResponseType> {

    @Autowired
    private ListServiceDataIMAgentMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataIMAgentRequestType convertRequest(ListServiceDataIMAgentRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataIMAgentResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataIMAgentRequestType request) {
        return proxy.listServiceDataIMAgent(request);
    }

    @Override
    public ListServiceDataIMAgentResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataIMAgentResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.ListServiceDataIMAgentRequestType request, ListServiceDataIMAgentRequestType request2) {
        return mapper.to(response);
    }
}
