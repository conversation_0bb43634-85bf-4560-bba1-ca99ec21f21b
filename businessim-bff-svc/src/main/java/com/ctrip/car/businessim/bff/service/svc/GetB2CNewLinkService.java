package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.GetB2CNewLinkMapper;
import com.ctrip.car.businessim.bff.service.entity.GetB2CNewLinkRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetB2CNewLinkResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class GetB2CNewLinkService extends CarSoaService<GetB2CNewLinkRequestType, GetB2CNewLinkResponseType, com.ctrip.car.customer.businessim.contract.servicetype.GetB2CNewLinkRequestType, com.ctrip.car.customer.businessim.contract.servicetype.GetB2CNewLinkResponseType> {

    @Autowired
    private GetB2CNewLinkMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetB2CNewLinkRequestType convertRequest(GetB2CNewLinkRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetB2CNewLinkResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.GetB2CNewLinkRequestType request2) {
        return proxy.GetB2CNewLink(request2);
    }

    @Override
    public GetB2CNewLinkResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.GetB2CNewLinkResponseType response2, com.ctrip.car.customer.businessim.contract.servicetype.GetB2CNewLinkRequestType request2, GetB2CNewLinkRequestType request) {
        return mapper.to(response2);
    }
}
