package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.GetB2OImGrayStatusMapper;
import com.ctrip.car.businessim.bff.service.entity.GetB2OImGrayStatusRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetB2OImGrayStatusResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/12/16
*/
@Service
public class GetB2OImGrayStatusService extends CarSoaService<GetB2OImGrayStatusRequestType, GetB2OImGrayStatusResponseType, com.ctrip.car.customer.businessim.contract.servicetype.GetB2OImGrayStatusRequestType, com.ctrip.car.customer.businessim.contract.servicetype.GetB2OImGrayStatusResponseType> {

    @Autowired
    private GetB2OImGrayStatusMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetB2OImGrayStatusRequestType convertRequest(GetB2OImGrayStatusRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetB2OImGrayStatusResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.GetB2OImGrayStatusRequestType request2) {
        return proxy.getB2OImGrayStatus(request2);
    }

    @Override
    public GetB2OImGrayStatusResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.GetB2OImGrayStatusResponseType response2, com.ctrip.car.customer.businessim.contract.servicetype.GetB2OImGrayStatusRequestType request2, GetB2OImGrayStatusRequestType request) {
        return mapper.to(response2);
    }
}
