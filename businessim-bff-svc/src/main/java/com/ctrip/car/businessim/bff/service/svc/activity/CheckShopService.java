package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.activity.CheckShopMapper;
import com.ctrip.car.businessim.bff.service.entity.CheckShopBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.CheckShopBffResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.CheckShopRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.CheckShopResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CheckShopService extends CarSoaService<CheckShopBffRequestType, CheckShopBffResponseType, CheckShopRequestType, CheckShopResponseType>{

    @Autowired
    private CheckShopMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public CheckShopRequestType convertRequest(CheckShopBffRequestType request) {
        return mapper.from(request);
    }

    @Override
    public CheckShopResponseType executeSOA(CheckShopRequestType request) {
        return proxy.checkShop(request);
    }

    @Override
    public CheckShopBffResponseType convertResponse(CheckShopResponseType response, CheckShopRequestType var2, CheckShopBffRequestType var3) {
        return mapper.to(response);
    }
}
