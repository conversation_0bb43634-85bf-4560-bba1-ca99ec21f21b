package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.GetBusinessNumberByCallIdMapper;
import com.ctrip.car.businessim.bff.service.entity.GetBusinessNumberByCallIdRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetBusinessNumberByCallIdResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/06
*/
@Service
public class GetBusinessNumberByCallIdService extends CarSoaService<GetBusinessNumberByCallIdRequestType, GetBusinessNumberByCallIdResponseType, com.ctrip.car.customer.businessim.contract.servicetype.GetBusinessNumberByCallIdRequestType, com.ctrip.car.customer.businessim.contract.servicetype.GetBusinessNumberByCallIdResponseType> {

    @Autowired
    private GetBusinessNumberByCallIdMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetBusinessNumberByCallIdRequestType convertRequest(GetBusinessNumberByCallIdRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetBusinessNumberByCallIdResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.GetBusinessNumberByCallIdRequestType request) {
        return proxy.getBusinessNumberByCallId(request);
    }

    @Override
    public GetBusinessNumberByCallIdResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.GetBusinessNumberByCallIdResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.GetBusinessNumberByCallIdRequestType request, GetBusinessNumberByCallIdRequestType request2) {
        return mapper.to(response);
    }
}
