package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.activity.AddPromoterMapper;
import com.ctrip.car.businessim.bff.service.entity.AddPromoterBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.AddPromoterBffResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.AddPromoterRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.AddPromoterResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AddPromoterService extends CarSoaService<AddPromoterBffRequestType, AddPromoterBffResponseType, AddPromoterRequestType, AddPromoterResponseType> {

    @Autowired
    private AddPromoterMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public AddPromoterRequestType convertRequest(AddPromoterBffRequestType request) {
        return mapper.from(request);
    }

    @Override
    public AddPromoterResponseType executeSOA(AddPromoterRequestType request) {
        return proxy.addPromoter(request);
    }

    @Override
    public AddPromoterBffResponseType convertResponse(AddPromoterResponseType response, AddPromoterRequestType var2, AddPromoterBffRequestType var3) {
        return mapper.to(response);
    }
}
