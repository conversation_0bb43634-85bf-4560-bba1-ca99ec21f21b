package com.ctrip.car.businessim.bff.service.svc.seo;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.seo.QuerySeoPageConfigMapper;
import com.ctrip.car.businessim.bff.service.dto.seo.QuerySeoPageConfigBffRequestType;
import com.ctrip.car.businessim.bff.service.dto.seo.QuerySeoPageConfigBffResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.seo.QuerySeoPageConfigRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.seo.QuerySeoPageConfigResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class QuerySeoPageConfigService extends CarSoaService<QuerySeoPageConfigBffRequestType, QuerySeoPageConfigBffResponseType, QuerySeoPageConfigRequestType, QuerySeoPageConfigResponseType> {

    @Autowired
    private QuerySeoPageConfigMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public QuerySeoPageConfigRequestType convertRequest(QuerySeoPageConfigBffRequestType request) {
        return mapper.from(request);
    }

    @Override
    public QuerySeoPageConfigResponseType executeSOA(QuerySeoPageConfigRequestType request) {
        return proxy.querySeoPageConfig(request);
    }

    @Override
    public QuerySeoPageConfigBffResponseType convertResponse(QuerySeoPageConfigResponseType response, QuerySeoPageConfigRequestType var2, QuerySeoPageConfigBffRequestType var3) {
        return mapper.to(response);
    }
}
