package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.GetB2ONewLinkAndParameterMapper;
import com.ctrip.car.businessim.bff.service.entity.GetB2ONewLinkAndParameterRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetB2ONewLinkAndParameterResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/12/16
*/
@Service
public class GetB2ONewLinkAndParameterService extends CarSoaService<GetB2ONewLinkAndParameterRequestType, GetB2ONewLinkAndParameterResponseType, com.ctrip.car.customer.businessim.contract.servicetype.GetB2ONewLinkAndParameterRequestType, com.ctrip.car.customer.businessim.contract.servicetype.GetB2ONewLinkAndParameterResponseType> {

    @Autowired
    private GetB2ONewLinkAndParameterMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetB2ONewLinkAndParameterRequestType convertRequest(GetB2ONewLinkAndParameterRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetB2ONewLinkAndParameterResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.GetB2ONewLinkAndParameterRequestType request2) {
        return proxy.getB2ONewLinkAndParameter(request2);
    }

    @Override
    public GetB2ONewLinkAndParameterResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.GetB2ONewLinkAndParameterResponseType response2, com.ctrip.car.customer.businessim.contract.servicetype.GetB2ONewLinkAndParameterRequestType request2, GetB2ONewLinkAndParameterRequestType request) {
        return mapper.to(response2);
    }
}
