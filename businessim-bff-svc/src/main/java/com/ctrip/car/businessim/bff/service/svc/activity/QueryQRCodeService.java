package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.activity.QueryQRCodeMapper;
import com.ctrip.car.businessim.bff.service.entity.QueryQRCodeBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.QueryQRCodeBffResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryQRCodeRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryQRCodeResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class QueryQRCodeService  extends CarSoaService<QueryQRCodeBffRequestType, QueryQRCodeBffResponseType, QueryQRCodeRequestType, QueryQRCodeResponseType>{

    @Autowired
    private QueryQRCodeMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public QueryQRCodeRequestType convertRequest(QueryQRCodeBffRequestType request) {
        return mapper.from(request);
    }

    @Override
    public QueryQRCodeResponseType executeSOA(QueryQRCodeRequestType request) {
        return proxy.queryQRCode(request);
    }

    @Override
    public QueryQRCodeBffResponseType convertResponse(QueryQRCodeResponseType response, QueryQRCodeRequestType var2, QueryQRCodeBffRequestType var3) {
        return mapper.to(response);
    }
}
