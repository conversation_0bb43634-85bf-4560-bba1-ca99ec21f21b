package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.GetB2OLinkAndParameterMapper;
import com.ctrip.car.businessim.bff.service.entity.GetB2OLinkAndParameterRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetB2OLinkAndParameterResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/12/16
*/
@Service
public class GetB2OLinkAndParameterService extends CarSoaService<GetB2OLinkAndParameterRequestType, GetB2OLinkAndParameterResponseType, com.ctrip.car.customer.businessim.contract.servicetype.GetB2OLinkAndParameterRequestType, com.ctrip.car.customer.businessim.contract.servicetype.GetB2OLinkAndParameterResponseType> {

    @Autowired
    private GetB2OLinkAndParameterMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetB2OLinkAndParameterRequestType convertRequest(GetB2OLinkAndParameterRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetB2OLinkAndParameterResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.GetB2OLinkAndParameterRequestType request2) {
        return proxy.getB2OLinkAndParameter(request2);
    }

    @Override
    public GetB2OLinkAndParameterResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.GetB2OLinkAndParameterResponseType response2, com.ctrip.car.customer.businessim.contract.servicetype.GetB2OLinkAndParameterRequestType request2, GetB2OLinkAndParameterRequestType request) {
        return mapper.to(response2);
    }
}
