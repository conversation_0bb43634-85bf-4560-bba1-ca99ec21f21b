package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.activity.QueryPromoterListMapper;
import com.ctrip.car.businessim.bff.service.entity.QueryPromoterListBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.QueryPromoterListBffResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryPromoterListRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryPromoterListResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class QueryPromoterListService extends CarSoaService<QueryPromoterListBffRequestType, QueryPromoterListBffResponseType, QueryPromoterListRequestType, QueryPromoterListResponseType> {

    @Autowired
    private QueryPromoterListMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public QueryPromoterListRequestType convertRequest(QueryPromoterListBffRequestType request) {
        return mapper.from(request);
    }

    @Override
    public QueryPromoterListResponseType executeSOA(QueryPromoterListRequestType request) {
        return proxy.queryPromoterList(request);
    }

    @Override
    public QueryPromoterListBffResponseType convertResponse(QueryPromoterListResponseType response, QueryPromoterListRequestType var2, QueryPromoterListBffRequestType var3) {
        return mapper.to(response);
    }
}
