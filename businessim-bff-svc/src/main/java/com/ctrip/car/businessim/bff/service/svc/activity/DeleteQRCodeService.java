package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.activity.DeleteQRCodeMapper;
import com.ctrip.car.businessim.bff.service.entity.DeleteQRCodeBffRequestType;
import com.ctrip.car.businessim.bff.service.entity.DeleteQRCodeBffResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.DeleteQRCodeRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.DeleteQRCodeResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DeleteQRCodeService extends CarSoaService<DeleteQRCodeBffRequestType, DeleteQRCodeBffResponseType, DeleteQRCodeRequestType, DeleteQRCodeResponseType>{

    @Autowired
    private DeleteQRCodeMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public DeleteQRCodeRequestType convertRequest(DeleteQRCodeBffRequestType request) {
        return mapper.from(request);
    }

    @Override
    public DeleteQRCodeResponseType executeSOA(DeleteQRCodeRequestType request) {
        return proxy.deleteQRCode(request);
    }

    @Override
    public DeleteQRCodeBffResponseType convertResponse(DeleteQRCodeResponseType response, DeleteQRCodeRequestType var2, DeleteQRCodeBffRequestType var3) {
        return mapper.to(response);
    }
}
