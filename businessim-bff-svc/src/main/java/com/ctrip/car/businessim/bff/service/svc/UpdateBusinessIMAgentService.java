package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.UpdateBusinessIMAgentMapper;
import com.ctrip.car.businessim.bff.service.entity.UpdateBusinessIMAgentRequestType;
import com.ctrip.car.businessim.bff.service.entity.UpdateBusinessIMAgentResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/06
*/
@Service
public class UpdateBusinessIMAgentService extends CarSoaService<UpdateBusinessIMAgentRequestType, UpdateBusinessIMAgentResponseType, com.ctrip.car.customer.businessim.contract.servicetype.UpdateBusinessIMAgentRequestType, com.ctrip.car.customer.businessim.contract.servicetype.UpdateBusinessIMAgentResponseType> {

    @Autowired
    private UpdateBusinessIMAgentMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.UpdateBusinessIMAgentRequestType convertRequest(UpdateBusinessIMAgentRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.UpdateBusinessIMAgentResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.UpdateBusinessIMAgentRequestType request) {
        return proxy.updateBusinessIMAgent(request);
    }

    @Override
    public UpdateBusinessIMAgentResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.UpdateBusinessIMAgentResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.UpdateBusinessIMAgentRequestType request, UpdateBusinessIMAgentRequestType request2) {
        return mapper.to(response);
    }
}
