package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.ListDropdownUserMapper;
import com.ctrip.car.businessim.bff.service.entity.ListDropdownUserRequestType;
import com.ctrip.car.businessim.bff.service.entity.ListDropdownUserResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/06
*/
@Service
public class ListDropdownUserService extends CarSoaService<ListDropdownUserRequestType, ListDropdownUserResponseType, com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownUserRequestType, com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownUserResponseType> {

    @Autowired
    private ListDropdownUserMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownUserRequestType convertRequest(ListDropdownUserRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownUserResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownUserRequestType request) {
        return proxy.listDropdownUser(request);
    }

    @Override
    public ListDropdownUserResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownUserResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownUserRequestType request, ListDropdownUserRequestType request2) {
        return mapper.to(response);
    }
}
