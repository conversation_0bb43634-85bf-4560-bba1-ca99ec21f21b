package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.GetIMGrayStatusMapper;
import com.ctrip.car.businessim.bff.service.entity.GetIMGrayStatusRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetIMGrayStatusResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/08
*/
@Service
public class GetIMGrayStatusService extends CarSoaService<GetIMGrayStatusRequestType, GetIMGrayStatusResponseType, com.ctrip.car.customer.businessim.contract.servicetype.GetIMGrayStatusRequestType, com.ctrip.car.customer.businessim.contract.servicetype.GetIMGrayStatusResponseType> {

    @Autowired
    private GetIMGrayStatusMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetIMGrayStatusRequestType convertRequest(GetIMGrayStatusRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetIMGrayStatusResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.GetIMGrayStatusRequestType request) {
        return proxy.getIMGrayStatus(request);
    }

    @Override
    public GetIMGrayStatusResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.GetIMGrayStatusResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.GetIMGrayStatusRequestType request, GetIMGrayStatusRequestType request2) {
        return mapper.to(response);
    }
}
