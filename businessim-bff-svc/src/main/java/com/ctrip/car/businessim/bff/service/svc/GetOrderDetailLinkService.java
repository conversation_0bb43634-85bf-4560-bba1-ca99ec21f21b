package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.GetOrderDetailLinkMapper;
import com.ctrip.car.businessim.bff.service.entity.GetOrderDetailLinkRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetOrderDetailLinkResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/06
*/
@Service
public class GetOrderDetailLinkService extends CarSoaService<GetOrderDetailLinkRequestType, GetOrderDetailLinkResponseType, com.ctrip.car.customer.businessim.contract.servicetype.GetOrderDetailLinkRequestType, com.ctrip.car.customer.businessim.contract.servicetype.GetOrderDetailLinkResponseType> {

    @Autowired
    private GetOrderDetailLinkMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetOrderDetailLinkRequestType convertRequest(GetOrderDetailLinkRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetOrderDetailLinkResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.GetOrderDetailLinkRequestType request) {
        return proxy.getOrderDetailLink(request);
    }

    @Override
    public GetOrderDetailLinkResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.GetOrderDetailLinkResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.GetOrderDetailLinkRequestType request, GetOrderDetailLinkRequestType request2) {
        return mapper.to(response);
    }
}
