package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.ListDropdownStoreMapper;
import com.ctrip.car.businessim.bff.service.entity.ListDropdownStoreRequestType;
import com.ctrip.car.businessim.bff.service.entity.ListDropdownStoreResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/06
*/
@Service
public class ListDropdownStoreService extends CarSoaService<ListDropdownStoreRequestType, ListDropdownStoreResponseType, com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownStoreRequestType, com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownStoreResponseType> {

    @Autowired
    private ListDropdownStoreMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownStoreRequestType convertRequest(ListDropdownStoreRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownStoreResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownStoreRequestType request) {
        return proxy.listDropdownStore(request);
    }

    @Override
    public ListDropdownStoreResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownStoreResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.ListDropdownStoreRequestType request, ListDropdownStoreRequestType request2) {
        return mapper.to(response);
    }
}
