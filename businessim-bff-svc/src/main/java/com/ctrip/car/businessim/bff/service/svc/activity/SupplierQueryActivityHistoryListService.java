package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.core.enums.ActivityStatusEnum;
import com.ctrip.car.businessim.bff.service.core.enums.SortTypeEnum;
import com.ctrip.car.businessim.bff.service.core.util.ResponseUtil;
import com.ctrip.car.businessim.bff.service.dto.activity.SupplierActivityInfo;
import com.ctrip.car.businessim.bff.service.entity.SupplierQueryActivityHistoryListRequestType;
import com.ctrip.car.businessim.bff.service.entity.SupplierQueryActivityHistoryListResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryVendorActivityListRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryVendorActivityListResponseType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SupplierQueryActivityHistoryListService extends ActivityBaseService<SupplierQueryActivityHistoryListRequestType, SupplierQueryActivityHistoryListResponseType> {

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    protected SupplierQueryActivityHistoryListResponseType business(SupplierQueryActivityHistoryListRequestType request) {
        SupplierQueryActivityHistoryListResponseType response = new SupplierQueryActivityHistoryListResponseType();
        if (request.getSortType() == null || request.getSortType() < 0) request.setSortType(0);
        if (request.getPageSize() == null || request.getPageSize() <= 0) request.setPageSize(10);
        if (request.getPageIndex() == null || request.getPageIndex() <= 0) request.setPageIndex(1);

        QueryVendorActivityListRequestType queryVendorActivityListRequest = this.buildSoaRequest(request);
        QueryVendorActivityListResponseType queryVendorActivityListResponse = proxy.queryVendorActivityList(queryVendorActivityListRequest);
        if (queryVendorActivityListResponse != null) {
            response = this.buildBffResponse(queryVendorActivityListResponse);
        } else {
            response.setBaseResponse(ResponseUtil.success());
        }
        response.setPageSize(request.getPageSize());
        response.setPageIndex(request.getPageIndex());
        response.setSortTypeName(SortTypeEnum.valueOfCode(request.getSortType()).getDesc());
        return response;
    }

    private QueryVendorActivityListRequestType buildSoaRequest(SupplierQueryActivityHistoryListRequestType request) {
        QueryVendorActivityListRequestType soaRequest = new QueryVendorActivityListRequestType();
        soaRequest.setBaseRequest(request.getBaseRequest());
        soaRequest.setActivityName(request.getActivityName());
        soaRequest.setLabelId(request.getLabelId());
        soaRequest.setLabelName(request.getLabelName());
        soaRequest.setStatus(request.getActivityStatuses());
        soaRequest.setSortIndex(request.getSortType());
        soaRequest.setVendorType(request.getSupplierType());
        soaRequest.setVendorId(request.getVendorId());
        if (NumberUtils.isDigits(request.getBaseRequest().getUid())) {
            soaRequest.setUserId(Long.parseLong(request.getBaseRequest().getUid()));
        }
        soaRequest.setPageNo(request.getPageIndex());
        soaRequest.setPageSize(request.getPageSize());
        soaRequest.setProductType(request.getProductType());
        return soaRequest;
    }

    private SupplierQueryActivityHistoryListResponseType buildBffResponse(QueryVendorActivityListResponseType soaResponse) {
        SupplierQueryActivityHistoryListResponseType bffResponse = new SupplierQueryActivityHistoryListResponseType();
        bffResponse.setBaseResponse(soaResponse.getBaseResponse());
        bffResponse.setCount(soaResponse.getTotal());
        if (CollectionUtils.isNotEmpty(soaResponse.getVendorActivityList())) {
            List<SupplierActivityInfo> list = soaResponse.getVendorActivityList().stream()
                    .map(vendorActivityListItem -> {
                        SupplierActivityInfo activityInfo = new SupplierActivityInfo();
                        activityInfo.setTemplateId(vendorActivityListItem.getTemplateId());
                        activityInfo.setActivityId(vendorActivityListItem.getActivityId());
                        activityInfo.setActivityName(vendorActivityListItem.getActivityName());
                        activityInfo.setLabelName(vendorActivityListItem.getLabelName());
                        activityInfo.setDiscountStrength(vendorActivityListItem.getDeductionType() + "<br/>" + vendorActivityListItem.getDeductionAmount());
                        activityInfo.setActivityDatetime(vendorActivityListItem.getActivityDateStr());
                        activityInfo.setActivityStatus(vendorActivityListItem.getStatus());
                        activityInfo.setActivityStatusName(ActivityStatusEnum.valueOfCode(vendorActivityListItem.getStatus()).getDesc());
                        activityInfo.setSignUpDatetime(vendorActivityListItem.getRegisterDateStr());
                        activityInfo.setSignUpCount(vendorActivityListItem.getRegisterCount());
                        activityInfo.setDeductionAmountList(vendorActivityListItem.getDeductionAmountList());
                        activityInfo.setUpdateTime(vendorActivityListItem.getUpdateTime());
                        activityInfo.setProductType(vendorActivityListItem.getProductType());
                        // 报名类型 0-手动报名 1-跟价自动报名
                        activityInfo.setSignUpMode(vendorActivityListItem.getSignUpMode());
                        return activityInfo;
                    })
                    .collect(Collectors.toList());
            bffResponse.setActivityInfoList(list);
        } else {
            bffResponse.setActivityInfoList(new ArrayList<>());
        }
        return bffResponse;
    }

    @Override
    protected SupplierQueryActivityHistoryListResponseType validRequest(SupplierQueryActivityHistoryListRequestType request) {
        String errorMsg = "";
        if (request.getBaseRequest() == null) {
            errorMsg += "baseRequest is null; ";
        } else {
            if (request.getBaseRequest().getUid() == null) {
                errorMsg += "uid is null; ";
            }
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            SupplierQueryActivityHistoryListResponseType response = new SupplierQueryActivityHistoryListResponseType();
            response.setBaseResponse(ResponseUtil.parameterError(errorMsg));
            return response;
        } else {
            return null;
        }
    }

}