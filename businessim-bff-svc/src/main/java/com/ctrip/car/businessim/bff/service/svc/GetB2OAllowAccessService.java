package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.GetB2OAllowAccessMapper;
import com.ctrip.car.businessim.bff.service.entity.GetB2OAllowAccessRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetB2OAllowAccessResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/08
*/
@Service
public class GetB2OAllowAccessService extends CarSoaService<GetB2OAllowAccessRequestType, GetB2OAllowAccessResponseType, com.ctrip.car.customer.businessim.contract.servicetype.GetB2OAllowAccessRequestType, com.ctrip.car.customer.businessim.contract.servicetype.GetB2OAllowAccessResponseType> {

    @Autowired
    private GetB2OAllowAccessMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetB2OAllowAccessRequestType convertRequest(GetB2OAllowAccessRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetB2OAllowAccessResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.GetB2OAllowAccessRequestType request) {
        return proxy.getB2OAllowAccess(request);
    }

    @Override
    public GetB2OAllowAccessResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.GetB2OAllowAccessResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.GetB2OAllowAccessRequestType request, GetB2OAllowAccessRequestType request2) {
        return mapper.to(response);
    }
}
