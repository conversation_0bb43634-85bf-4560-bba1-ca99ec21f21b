package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.core.util.ResponseUtil;
import com.ctrip.car.businessim.bff.service.entity.SupplierQuitActivityRequestType;
import com.ctrip.car.businessim.bff.service.entity.SupplierQuitActivityResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.UpdateActivityStatusRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.UpdateActivityStatusResponseType;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SupplierQuitActivityService extends ActivityBaseService<SupplierQuitActivityRequestType, SupplierQuitActivityResponseType> {

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    protected SupplierQuitActivityResponseType business(SupplierQuitActivityRequestType request) {
        UpdateActivityStatusRequestType updateActivityStatusRequestType = this.buildSoaRequest(request);
        UpdateActivityStatusResponseType updateActivityStatusResponseType = proxy.updateActivityStatus(updateActivityStatusRequestType);
        if (updateActivityStatusResponseType != null) {
            return this.buildBffResponse(updateActivityStatusResponseType);
        } else {
            SupplierQuitActivityResponseType response = new SupplierQuitActivityResponseType();
            response.setBaseResponse(ResponseUtil.thirdServiceError("an error occurs when invoking third-service, fail to quit activity."));
            return response;
        }
    }

    private UpdateActivityStatusRequestType buildSoaRequest(SupplierQuitActivityRequestType request) {
        UpdateActivityStatusRequestType soaRequest = new UpdateActivityStatusRequestType();
        soaRequest.setBaseRequest(request.getBaseRequest());
        soaRequest.setActivityId(request.getActivityId());
        soaRequest.setVendorId(request.getVendorId());
        if (NumberUtils.isDigits(request.getBaseRequest().getUid())) {
            soaRequest.setUserId(Long.parseLong(request.getBaseRequest().getUid()));
        }
        soaRequest.setStatus(5); //0待审核 1未开始 2活动中 3已作废 4已过期 5已退出
        soaRequest.setProxyUid(request.getBaseRequest().getProxyUid());
        return soaRequest;
    }

    private SupplierQuitActivityResponseType buildBffResponse(UpdateActivityStatusResponseType soaResponse) {
        SupplierQuitActivityResponseType bffResponse = new SupplierQuitActivityResponseType();
        bffResponse.setBaseResponse(soaResponse.getBaseResponse());
        return bffResponse;
    }

    @Override
    protected SupplierQuitActivityResponseType validRequest(SupplierQuitActivityRequestType request) {
        String errorMsg = "";
        if (request.getBaseRequest() == null) {
            errorMsg += "baseRequest is null; ";
        } else {
            if (request.getBaseRequest().getUid() == null) {
                errorMsg += "uid is null; ";
            }
        }
        if (request.getActivityId() == null) {
            errorMsg += "activityId is null; ";
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            SupplierQuitActivityResponseType response = new SupplierQuitActivityResponseType();
            response.setBaseResponse(ResponseUtil.parameterError(errorMsg));
            return response;
        } else {
            return null;
        }
    }

}
