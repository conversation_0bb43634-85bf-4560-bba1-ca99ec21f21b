package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.GetIMSessionReportMapper;
import com.ctrip.car.businessim.bff.service.entity.GetIMSessionReportRequestType;
import com.ctrip.car.businessim.bff.service.entity.GetIMSessionReportResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/06
*/
@Service
public class GetIMSessionReportService extends CarSoaService<GetIMSessionReportRequestType, GetIMSessionReportResponseType, com.ctrip.car.customer.businessim.contract.servicetype.GetIMSessionReportRequestType, com.ctrip.car.customer.businessim.contract.servicetype.GetIMSessionReportResponseType> {

    @Autowired
    private GetIMSessionReportMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetIMSessionReportRequestType convertRequest(GetIMSessionReportRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.GetIMSessionReportResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.GetIMSessionReportRequestType request) {
        return proxy.getIMSessionReport(request);
    }

    @Override
    public GetIMSessionReportResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.GetIMSessionReportResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.GetIMSessionReportRequestType request, GetIMSessionReportRequestType request2) {
        return mapper.to(response);
    }
}
