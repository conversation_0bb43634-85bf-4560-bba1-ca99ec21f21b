package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.core.util.ResponseUtil;
import com.ctrip.car.businessim.bff.service.dto.activity.CityInfo;
import com.ctrip.car.businessim.bff.service.entity.SupplierQueryOpenCityInfoRequestType;
import com.ctrip.car.businessim.bff.service.entity.SupplierQueryOpenCityInfoResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryOpenCityInfoByVendorIdsRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryOpenCityInfoByVendorIdsResponseType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SupplierQueryOpenCityInfoService extends ActivityBaseService<SupplierQueryOpenCityInfoRequestType, SupplierQueryOpenCityInfoResponseType> {

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    protected SupplierQueryOpenCityInfoResponseType business(SupplierQueryOpenCityInfoRequestType request) {
        if (StringUtils.isBlank(request.getKeyWords())) {
            SupplierQueryOpenCityInfoResponseType response = new SupplierQueryOpenCityInfoResponseType();
            response.setBaseResponse(ResponseUtil.success());
            return response;
        }

        QueryOpenCityInfoByVendorIdsRequestType queryOpenCityInfoByVendorIdsRequestType = this.buildSoaRequest(request);
        QueryOpenCityInfoByVendorIdsResponseType soaResponse = proxy.queryOpenCityInfoList(queryOpenCityInfoByVendorIdsRequestType);
        if (soaResponse != null) {
            return this.buildBffResponse(soaResponse);
        } else {
            SupplierQueryOpenCityInfoResponseType response = new SupplierQueryOpenCityInfoResponseType();
            response.setBaseResponse(ResponseUtil.thirdServiceError("an error occurs when invoking third-service, fail to quit activity."));
            return response;
        }
    }

    private QueryOpenCityInfoByVendorIdsRequestType buildSoaRequest(SupplierQueryOpenCityInfoRequestType request) {
        QueryOpenCityInfoByVendorIdsRequestType soaRequest = new QueryOpenCityInfoByVendorIdsRequestType();
        soaRequest.setBaseRequest(request.getBaseRequest());
        List<Long> vendorIdList = new ArrayList<>();
        vendorIdList.add(request.getVendorId());
        soaRequest.setVendorIds(vendorIdList);
        if (NumberUtils.isDigits(request.getBaseRequest().getUid())) {
            soaRequest.setUserId(Long.parseLong(request.getBaseRequest().getUid()));
        }
        soaRequest.setKeyWords(request.getKeyWords());
        soaRequest.setActTempId(request.getTemplateId());
        return soaRequest;
    }

    private SupplierQueryOpenCityInfoResponseType buildBffResponse(QueryOpenCityInfoByVendorIdsResponseType soaResponse) {
        SupplierQueryOpenCityInfoResponseType bffResponse = new SupplierQueryOpenCityInfoResponseType();
        if (CollectionUtils.isNotEmpty(soaResponse.getInfos())) {
            bffResponse.setCityInfoList(
                    soaResponse.getInfos().stream()
                            .map(dataSourceInfo -> {
                                CityInfo cityInfo = new CityInfo();
                                cityInfo.setCityId(Long.parseLong(dataSourceInfo.getDataKey()));
                                cityInfo.setCityName(dataSourceInfo.getDisplayName());
                                return cityInfo;
                            })
                            .collect(Collectors.toList()));
        } else {
            bffResponse.setCityInfoList(new ArrayList<>());
        }
        bffResponse.setBaseResponse(soaResponse.getBaseResponse());
        return bffResponse;
    }

    @Override
    protected SupplierQueryOpenCityInfoResponseType validRequest(SupplierQueryOpenCityInfoRequestType request) {
        String errorMsg = "";
        if (request.getBaseRequest() == null) {
            errorMsg += "baseRequest is null; ";
        } else {
            if (request.getBaseRequest().getUid() == null) {
                errorMsg += "uid is null; ";
            }
        }
        if (request.getTemplateId() == null || request.getTemplateId() < 0) {
            errorMsg += "templateId is null; ";
        }
        if (request.getVendorId() == null || request.getVendorId() < 0) {
            errorMsg += "vendorId is null; ";
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            SupplierQueryOpenCityInfoResponseType response = new SupplierQueryOpenCityInfoResponseType();
            response.setBaseResponse(ResponseUtil.parameterError(errorMsg));
            return response;
        } else {
            return null;
        }
    }

}