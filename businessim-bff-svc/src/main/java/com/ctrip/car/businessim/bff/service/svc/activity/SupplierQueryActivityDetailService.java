package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.core.config.Config;
import com.ctrip.car.businessim.bff.service.core.dto.HolidayDto;
import com.ctrip.car.businessim.bff.service.core.enums.*;
import com.ctrip.car.businessim.bff.service.core.util.CalendarFormatUtil;
import com.ctrip.car.businessim.bff.service.core.util.QConfigUtil;
import com.ctrip.car.businessim.bff.service.core.util.ResponseUtil;
import com.ctrip.car.businessim.bff.service.dto.activity.*;
import com.ctrip.car.businessim.bff.service.entity.SupplierQueryActivityDetailRequestType;
import com.ctrip.car.businessim.bff.service.entity.SupplierQueryActivityDetailResponseType;
import com.ctrip.car.businessim.bff.service.proxy.dto.VehicleGroupDTO;
import com.ctrip.car.businessim.bff.service.proxy.soa.*;
import com.ctrip.car.commodity.common.service.datasync.StandardProductDto;
import com.ctrip.car.commodity.store.dto.StoreShortInfoDto;
import com.ctrip.car.customer.businessim.contract.dto.activity.*;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStandardProductInfoRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStandardProductInfoResponseType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryVendorActivityDetailRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryVendorActivityDetailResponseType;
import com.ctrip.car.market.common.enums.ActivityGroup;
import com.ctrip.car.market.common.enums.ActivityStatus;
import com.ctrip.car.market.common.enums.DeductionType;
import com.ctrip.car.market.common.enums.StoreType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.triplog.shaded.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SupplierQueryActivityDetailService extends ActivityBaseService<SupplierQueryActivityDetailRequestType, SupplierQueryActivityDetailResponseType> {

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Autowired
    private SDMarketingServiceProxy sdMarketingServiceProxy;

    @Autowired
    private CarCommodityVendorServiceProxy carCommodityVendorServiceProxy;

    @Autowired
    private StoreServiceProxy storeServiceProxy;

    @Autowired
    private Config config;

    @Autowired
    private CarcommoditycommonserviceProxy carcommoditycommonserviceProxy;

    @Override
    protected SupplierQueryActivityDetailResponseType business(SupplierQueryActivityDetailRequestType request) {
        SupplierQueryActivityDetailResponseType response = new SupplierQueryActivityDetailResponseType();
        QueryVendorActivityDetailRequestType queryVendorActivityListRequest = this.buildSoaRequest(request);
        QueryVendorActivityDetailResponseType queryVendorActivityDetailResponse = proxy.queryVendorActivityDetail(queryVendorActivityListRequest);
        if (queryVendorActivityDetailResponse != null) {
            response = this.buildBffResponse(queryVendorActivityDetailResponse, request);
        }
        response.setActivityId(request.getActivityId());
        response.setTemplateId(request.getTemplateId());
        response.setVendorId(request.getVendorId());
        com.ctrip.car.commodity.vendor.service.contract.dto.VendorInfo vendor = carCommodityVendorServiceProxy.queryVendorInfoByVendorId(request.getVendorId());
        if (vendor != null) {
            response.setAccessWay(vendor.getAccessWay());
        }
        return response;
    }

    private QueryVendorActivityDetailRequestType buildSoaRequest(SupplierQueryActivityDetailRequestType request) {
        QueryVendorActivityDetailRequestType soaRequest = new QueryVendorActivityDetailRequestType();
        soaRequest.setBaseRequest(request.getBaseRequest());
        soaRequest.setActivityId(request.getActivityId());
        soaRequest.setTemplateId(request.getTemplateId());
        soaRequest.setVendorId(request.getVendorId());
        if (NumberUtils.isDigits(request.getBaseRequest().getUid())) {
            soaRequest.setUserId(Long.parseLong(request.getBaseRequest().getUid()));
        }
        return soaRequest;
    }

    public SupplierQueryActivityDetailResponseType buildBffResponse(QueryVendorActivityDetailResponseType soaResponse, SupplierQueryActivityDetailRequestType bffRequest) {
        SupplierQueryActivityDetailResponseType bffResponse = new SupplierQueryActivityDetailResponseType();
        if (soaResponse.getActTemplate() == null) {
            bffResponse.setBaseResponse(soaResponse.getBaseResponse());
            return bffResponse;
        }
        ActTemplateDto soaTemplate = soaResponse.getActTemplate();
        bffResponse.setBaseResponse(soaResponse.getBaseResponse());
        bffResponse.setTemplateType(soaTemplate.getTemplateType());
        SupplierActivityDetail supplierActivityDetail = new SupplierActivityDetail();
        // 字段：活动名称
        supplierActivityDetail.setActivityName(soaTemplate.getName());
        // 字段：报名状态
        supplierActivityDetail.setSignUpStatus(soaResponse.getRegisterStatus());
        supplierActivityDetail.setSignUpStatusName(SignUpStatusEnum.valueOfCode(soaResponse.getRegisterStatus()).getDesc());
        // 字段：活动标签
        supplierActivityDetail.setLabelName(soaTemplate.getLabelName());
        // 字段：活动简介
        supplierActivityDetail.setInformation(soaTemplate.getRemark());
        // 字段：注意事项
        supplierActivityDetail.setNotice(QConfigUtil.getChineseOrDefault("SIGN_UP_NOTICE", ""));
        // 字段：报名时间
        supplierActivityDetail.setSignUpDatetime(CalendarFormatUtil.format_yyyy_MM_dd_HH_mm_ss(soaTemplate.getRegisterStartTime()) + " " +
                QConfigUtil.getChineseOrDefault("TO", "to") + " " +
                CalendarFormatUtil.format_yyyy_MM_dd_HH_mm_ss(soaTemplate.getRegisterEndTime()));
        // 标准车型限制说明
        supplierActivityDetail.setStandardProductLimit(soaResponse.getStandardProductLimit());
        //活动分组

        // 字段：活动时间
        StringBuilder activityDatetimeSB = new StringBuilder();
        activityDatetimeSB.append(CalendarFormatUtil.format_yyyy_MM_dd_HH_mm_ss(soaTemplate.getActivityStartTime())).append(" ");
        activityDatetimeSB.append(QConfigUtil.getChineseOrDefault("TO", "to")).append(" ");
        activityDatetimeSB.append(CalendarFormatUtil.format_yyyy_MM_dd_HH_mm_ss(soaTemplate.getActivityEndTime()));
        List<Integer> repetitionPeriod = soaTemplate.getRepetitionPeriod(); //重复周期
        List<LimitDateDto> excludeDate = soaTemplate.getExcludeDate(); //排除时间
        if (CollectionUtils.isNotEmpty(repetitionPeriod) || CollectionUtils.isNotEmpty(excludeDate)){
            activityDatetimeSB.append("（");
            if (CollectionUtils.isNotEmpty(repetitionPeriod)) {
                activityDatetimeSB.append(QConfigUtil.getChineseOrDefault("EVERY", "every"));
                for (int i = 0; i < repetitionPeriod.size(); i++) {
                    activityDatetimeSB.append(WeekEnum.valueOfCode(repetitionPeriod.get(i)).getDesc());
                    if (i != repetitionPeriod.size() - 1) {
                        activityDatetimeSB.append("、");
                    }
                }
                activityDatetimeSB.append(QConfigUtil.getChineseOrDefault("VALID", "valid"));
            }
            if (CollectionUtils.isNotEmpty(excludeDate)) {
                if (CollectionUtils.isNotEmpty(repetitionPeriod)) {
                    activityDatetimeSB.append("；");
                }
                for (int i = 0; i < excludeDate.size(); i++) {
                    LimitDateDto limitDateDto = excludeDate.get(i);
                    if (limitDateDto != null) {
                        activityDatetimeSB.append(CalendarFormatUtil.format_yyyy_MM_dd_HH_mm_ss(limitDateDto.getStart())).append(" ");
                        activityDatetimeSB.append(QConfigUtil.getChineseOrDefault("TO", "to")).append(" ");
                        activityDatetimeSB.append(CalendarFormatUtil.format_yyyy_MM_dd_HH_mm_ss(limitDateDto.getEnd()));
                        if (i != excludeDate.size() - 1) {
                            activityDatetimeSB.append("、");
                        }
                        if (i == excludeDate.size() - 1) {
                            activityDatetimeSB.append(" ");
                        }
                    }
                }
                activityDatetimeSB.append(QConfigUtil.getChineseOrDefault("EXCLUDE", "exclude"));
            }
            activityDatetimeSB.append("）");
        }
        supplierActivityDetail.setActivityDatetime(activityDatetimeSB.toString());

        ActTempContentDto actTempContent = soaTemplate.getActTempContent();
        ActDto soaActInfo = soaResponse.getActInfo();
        if (actTempContent != null) {
            // 优惠策略
            ActDeductionStrategyDto deductionStrategy = actTempContent.getDeductionStrategy();
            if (deductionStrategy != null) {
                // 字段：优惠方式
                StringBuilder includeFeesSB = new StringBuilder(); //可用费用项
                List<String> includeFees = deductionStrategy.getIncludeFees();
                if (CollectionUtils.isNotEmpty(includeFees)) {
                    for (int i = 0; i < includeFees.size(); i++) {
                        includeFeesSB.append(FeeTypeEnum.valueOfCode(includeFees.get(i)).getDesc());
                        if (i != includeFees.size() -1) {
                            includeFeesSB.append(" ");
                        }
                    }
                }
                //优惠范围文案
                String deductionRange = null;
                //具体的优惠力度
                String deductionDesc = null;
                //上面的优惠力度文案
                String discountStrength = null;
                //优惠文案
                if (deductionStrategy.getDeductionType() == 0) { //固定金额
                    supplierActivityDetail.setDiscountStrength(String.format(
                            QConfigUtil.getChineseOrDefault("DiscountStrength_AMOUNT_S", "%s"), includeFeesSB));
                    deductionRange = QConfigUtil.getChineseOrDefault("DeductionInfo_AMOUNT_RANGE_S_S", "");
                    discountStrength = String.format(QConfigUtil.getChineseOrDefault("DiscountStrength_AMOUNT_S", "%s"), includeFeesSB);
                    deductionDesc = String.format(QConfigUtil.getChineseOrDefault("DeductionInfo_AMOUNT_S", "%s"), deductionStrategy.getDeductionAmount());
                } else if (deductionStrategy.getDeductionType() == 1) { //折扣百分比
                    supplierActivityDetail.setDiscountStrength(String.format(
                            QConfigUtil.getChineseOrDefault("DiscountStrength_PERCENT_S", "%s"),
                            deductionStrategy.getDeductionAmount(),
                            includeFeesSB));
                    deductionRange = QConfigUtil.getChineseOrDefault("DeductionInfo_PERCENT_RANGE_S_S", "");
                    discountStrength = String.format(QConfigUtil.getChineseOrDefault("DiscountStrength_AMOUNT_S", "%s"), includeFeesSB);
                    deductionDesc = String.format(QConfigUtil.getChineseOrDefault("DeductionInfo_PERCENT_S", "%s"), deductionStrategy.getDeductionAmount());
                } else if (deductionStrategy.getDeductionType() == 7) { //首日租金
                    supplierActivityDetail.setDiscountStrength(QConfigUtil.getChineseOrDefault("DiscountStrength_FIRST_DAY", ""));
                    deductionDesc = QConfigUtil.getChineseOrDefault("DeductionInfo_FIRST_DAY", "");
                    discountStrength = String.format(QConfigUtil.getChineseOrDefault("DiscountStrength_AMOUNT_S", "%s"), includeFeesSB);
                } else  if(deductionStrategy.getDeductionType() == 11) {
                    supplierActivityDetail.setDiscountStrength(String.format(
                            QConfigUtil.getChineseOrDefault("DiscountStrength_STEP_S", "%s"),
                            includeFeesSB));
                    deductionRange = QConfigUtil.getChineseOrDefault("DeductionInfo_PERCENT_RANGE_S_S", "");
                    deductionDesc = QConfigUtil.getChineseOrDefault("DeductionInfo_STEP_AMOUNT_S_S_S", "");
                    discountStrength = String.format(QConfigUtil.getChineseOrDefault("DiscountStrength_STEP_S", "%s"), includeFeesSB);
                }else  if(deductionStrategy.getDeductionType() == 12) {
                    supplierActivityDetail.setDiscountStrength(String.format(
                            QConfigUtil.getChineseOrDefault("DiscountStrength_STEP_S", "%s"),
                            includeFeesSB));
                    deductionRange = QConfigUtil.getChineseOrDefault("DeductionInfo_PERCENT_RANGE_S_S", "");
                    deductionDesc = QConfigUtil.getChineseOrDefault("DeductionInfo_STEP_PERCENT_S_S_S", "");
                    discountStrength = String.format(QConfigUtil.getChineseOrDefault("DiscountStrength_STEP_S", "%s"), includeFeesSB);
                } else if (deductionStrategy.getDeductionType() == 13) {
                    supplierActivityDetail.setDiscountStrength(String.format(QConfigUtil.getChineseOrDefault("DiscountStrength_STEP_S", "%s"), includeFeesSB));
                    deductionRange = QConfigUtil.getChineseOrDefault("DeductionInfo_AMOUNT_RANGE_S_S", "");
                    deductionDesc = QConfigUtil.getChineseOrDefault("DeductionInfo_STEP_AMOUNT_ADVANCE_S_S_S", "");
                    discountStrength = String.format(QConfigUtil.getChineseOrDefault("DiscountStrength_STEP_ADVANCE_S", "%s"), includeFeesSB);
                } else if (deductionStrategy.getDeductionType() == 14) {
                    supplierActivityDetail.setDiscountStrength(String.format(QConfigUtil.getChineseOrDefault("DiscountStrength_STEP_S", "%s"), includeFeesSB));
                    deductionRange = QConfigUtil.getChineseOrDefault("DeductionInfo_PERCENT_RANGE_S_S", "");
                    deductionDesc = QConfigUtil.getChineseOrDefault("DeductionInfo_STEP_PERCENT_ADVANCE_S_S_S", "");
                    discountStrength = String.format(QConfigUtil.getChineseOrDefault("DiscountStrength_STEP_ADVANCE_S", "%s"), includeFeesSB);
                } else if (deductionStrategy.getDeductionType() == 15) { //15-动态立减 com.ctrip.car.market.common.enums.DeductionType
                    // 注意事项
                    supplierActivityDetail.setNotice(QConfigUtil.getChineseOrDefault("DYNAMIC_DISCOUNT_NOTICE", "")); //chinese:
                    supplierActivityDetail.setDiscountStrength(QConfigUtil.getChineseOrDefault("DiscountStrength_AMOUNT_S", "")); //chinese: 订单可享以下优惠力度
                    deductionRange = QConfigUtil.getChineseOrDefault("DeductionInfo_AMOUNT_RANGE_S_S", ""); //chinese: 可设置优惠范围：%s至%s
                    BigDecimal subsidyRangeMax = Objects.nonNull(deductionStrategy.getSubsidyRangeMax()) ? deductionStrategy.getSubsidyRangeMax() : BigDecimal.ZERO;
                    // 优惠方案
                    deductionDesc = QConfigUtil.getChineseOrDefault("DeductionInfo_DYNAMIC_DISCOUNT_RANGE_S", "") //chinese: 动态立减：每单最多优惠租车费的[subsidy]%
                            .replace("[subsidy]", subsidyRangeMax.toString());
                    discountStrength = QConfigUtil.getChineseOrDefault("DiscountStrength_AMOUNT_S", ""); //chinese: 订单可享以下优惠力度
                }

                BigDecimal deductionAmount = deductionStrategy.getDeductionAmount();
                List<CustomDeductionStrategyItemDto> customDeductionList =  new ArrayList<>();
                if (soaActInfo != null && soaActInfo.getCustomContent() != null && soaActInfo.getCustomContent().getDeductionStrategy() != null) {
                    deductionAmount = soaActInfo.getCustomContent().getDeductionStrategy().getDeductionAmount();
                    customDeductionList = soaActInfo.getCustomContent().getDeductionStrategy().getDeductionStrategyList();
                }
                DeductionInfo deductionInfo = new DeductionInfo();
                deductionInfo.setDiscountStrength(discountStrength);
                deductionInfo.setDeductionDesc(deductionDesc);
                deductionInfo.setDeductionRange(deductionRange);
                deductionInfo.setMinAmount(deductionStrategy.getMinAmount());
                deductionInfo.setMaxAmount(deductionStrategy.getMaxAmount());
                if (CollectionUtils.isNotEmpty(deductionStrategy.getDeductionStrategyList())) {
                    List<DeductionInfoItem> items = new ArrayList<>();
                    for (int i = 0; i < deductionStrategy.getDeductionStrategyList().size(); i++) {
                        ActDeductionStrategyItemDto item = deductionStrategy.getDeductionStrategyList().get(i);
                        BigDecimal amount = item.getDeductionAmount();
                        if (CollectionUtils.isNotEmpty(customDeductionList)) {
                            CustomDeductionStrategyItemDto customDeductionStrategyItemDto = customDeductionList.get(i);
                            if (customDeductionStrategyItemDto != null) {
                                amount = customDeductionStrategyItemDto.getDeductionAmount();
                            }
                        }
                        DeductionInfoItem deductionInfoItem = new DeductionInfoItem();
                        deductionInfoItem.setDeductionAmount(amount);
                        deductionInfoItem.setMaxAmount(item.getMaxAmount());
                        deductionInfoItem.setMinAmount(item.getMinAmount());
                        deductionInfoItem.setStartValue(item.getStartValue());
                        if (StringUtils.isNotEmpty(deductionDesc)) {
                            deductionInfoItem.setDeductionDesc(String.format(deductionDesc, i+1 ,item.getStartValue(), item.getDeductionAmount()));
                        }
                        deductionInfoItem.setDeductionRange(deductionRange);
                        items.add(deductionInfoItem);
                    }
                    deductionInfo.setDeductionStrategyList(items);
                }
                deductionInfo.setDeductionAmount(deductionAmount);
                deductionInfo.setDeductionType(deductionStrategy.getDeductionType());
                deductionInfo.setAllowCustomization(deductionStrategy.isAllowCustomization());
                Integer dType = deductionStrategy.getDeductionType();
                BigDecimal limitAmount = deductionStrategy.getDeductionAmountLimit();
                if (DeductionType.Discount.equal(dType) || DeductionType.FirstAmount.equal(dType) || DeductionType.Tenancy_Discount.equal(dType) || DeductionType.Advance_Discount.equal(dType) || Objects.equals(dType, 15)) {
                    if (limitAmount != null && limitAmount.compareTo(BigDecimal.ZERO) > 0) {
                        deductionInfo.setDeductionAmountLimitStr(limitAmount + QConfigUtil.getChineseOrDefault("YUAN",""));
                    }
                }
                supplierActivityDetail.setDeductionInfo(deductionInfo);

                // 字段：优惠同享
                if (deductionStrategy.isShareWithCoupon()) {
                    supplierActivityDetail.setDiscountShare(QConfigUtil.getChineseOrDefault("CAN_SHARE_COUPON", ""));
                } else {
                    supplierActivityDetail.setDiscountShare(QConfigUtil.getChineseOrDefault("CANT_SHARE_COUPON", ""));
                }
            }

            // 成本承担
            ShareDetailDto shareDetail = actTempContent.getShareDetail();
            if (shareDetail != null) {
                // 字段：成本承担方式
                if (shareDetail.getCostShare() == 0 && shareDetail.getShareType() == 0) {
                    supplierActivityDetail.setCostBearer(String.format(
                            QConfigUtil.getChineseOrDefault("VENDOR_BEARER_PERCENT_S", "%s"),
                            shareDetail.getPercentage()));
                }
                if (shareDetail.getCostShare() == 0 && shareDetail.getShareType() == 1) {
                    supplierActivityDetail.setCostBearer(String.format(
                            QConfigUtil.getChineseOrDefault("VENDOR_BEARER_AMOUNT_S", "%s"),
                            shareDetail.getFixedAmount()));
                }
                if (shareDetail.getCostShare() == 1 && shareDetail.getShareType() == 0) {
                    supplierActivityDetail.setCostBearer(String.format(
                            QConfigUtil.getChineseOrDefault("CTRIP_BEARER_PERCENT_S", "%s"),
                            shareDetail.getPercentage()));
                }
                if (shareDetail.getCostShare() == 1 && shareDetail.getShareType() == 1) {
                    supplierActivityDetail.setCostBearer(String.format(
                            QConfigUtil.getChineseOrDefault("CTRIP_BEARER_AMOUNT_S", "%s"),
                            shareDetail.getFixedAmount()));
                }
            }

            // 使用条件
            SupplierConditionDto supplierCondition = actTempContent.getSupplierCondition();
            if (supplierCondition != null) {
                // 字段：支付方式
                List<Integer> payModes = supplierCondition.getPayModes();
                if (CollectionUtils.isNotEmpty(payModes)) {
                    StringBuilder paymentTypeSB = new StringBuilder();
                    for (int i = 0; i < payModes.size(); i++) {
                        if (payModes.get(i) == 1) {
                            paymentTypeSB.append(QConfigUtil.getChineseOrDefault("PAYMENT_TYPE_NOW", ""));
                        }
                        if (payModes.get(i) == 2) {
                            paymentTypeSB.append(QConfigUtil.getChineseOrDefault("PAYMENT_TYPE_ADVANCED", ""));
                        }
                        if (i != payModes.size() - 1) {
                            paymentTypeSB.append(" ");
                        }
                    }
                    supplierActivityDetail.setPaymentType(paymentTypeSB.toString());
                }
                // 字段：租期限制
                List<LimitRangeDto> tenancyRangeList = supplierCondition.getTenancyRange();
                if (CollectionUtils.isEmpty(tenancyRangeList)) {
                    supplierActivityDetail.setRentalDayLimitation(QConfigUtil.getChineseOrDefault("RENT_DAY_NO_LIMIT", ""));
                }
                if (CollectionUtils.isNotEmpty(tenancyRangeList)) {
                    StringBuilder rentalDayLimitationSB = new StringBuilder();
                    for (int i = 0; i < tenancyRangeList.size(); i++) {
                        LimitRangeDto limitRange = tenancyRangeList.get(i);
                        if (limitRange.getFloor() != null && limitRange.getUpline() != null) {
                            rentalDayLimitationSB.append(String.format(
                                    QConfigUtil.getChineseOrDefault("RENT_DAY_NO_LIMIT_FLOOR_UPLINE_S_S", "%s%s"),
                                    limitRange.getFloor(),
                                    limitRange.getUpline()));
                        } else if (limitRange.getFloor() != null) {
                            rentalDayLimitationSB.append(String.format(
                                    QConfigUtil.getChineseOrDefault("RENT_DAY_NO_LIMIT_FLOOR_S", "%s"),
                                    limitRange.getFloor()));
                        } else if (limitRange.getUpline() != null) {
                            rentalDayLimitationSB.append(String.format(
                                    QConfigUtil.getChineseOrDefault("RENT_DAY_NO_LIMIT_UPLINE_S", "%s"),
                                    limitRange.getUpline()));
                        }
                        if (i != tenancyRangeList.size() - 1) {
                            rentalDayLimitationSB.append("<br/>");
                        }
                    }
                    if (StringUtils.isBlank(rentalDayLimitationSB.toString())) {
                        rentalDayLimitationSB.append(QConfigUtil.getChineseOrDefault("RENT_DAY_NO_LIMIT", ""));
                    }
                    supplierActivityDetail.setRentalDayLimitation(rentalDayLimitationSB.toString());
                }
                // 字段：取还车时间提示文案
                Integer pickUpTimeLimitType = supplierCondition.getPickUpTimeLimitType();
                if (pickUpTimeLimitType == 0) {
                    supplierActivityDetail.setPickReturnDatetimeNotice(QConfigUtil.getChineseOrDefault("PICK_RETURN_TIME_NOTICE_STRICT", ""));
                }
                if (pickUpTimeLimitType == 1) {
                    supplierActivityDetail.setPickReturnDatetimeNotice(QConfigUtil.getChineseOrDefault("PICK_RETURN_TIME_NOTICE_LOOSE", ""));
                }
                // 字段：取还车时间限制
                List<LimitDateDto> pickUpTimeRangeList = supplierCondition.getPickUpTimeRange();
                if (CollectionUtils.isEmpty(pickUpTimeRangeList)) {
                    supplierActivityDetail.setPickReturnDatetimeNotice(null);
                    supplierActivityDetail.setPickReturnDatetimeLimitation(QConfigUtil.getChineseOrDefault("PICK_RETURN_TIME_NO_LIMIT", ""));
                }
                if (CollectionUtils.isNotEmpty(pickUpTimeRangeList)) {
                    StringBuilder pickReturnDatetimeLimitationSB = new StringBuilder();
                    for (int i = 0; i < pickUpTimeRangeList.size(); i++) {
                        LimitDateDto limitDateDto = pickUpTimeRangeList.get(i);
                        pickReturnDatetimeLimitationSB.append(String.format(
                                QConfigUtil.getChineseOrDefault("PICK_RETURN_TIME_FLOOR_UPLINE_S_S", "%s%s="),
                                CalendarFormatUtil.format_yyyy_MM_dd_HH_mm_ss(limitDateDto.getStart()),
                                CalendarFormatUtil.format_yyyy_MM_dd_HH_mm_ss(limitDateDto.getEnd())));
                        if (i != pickUpTimeRangeList.size() - 1) {
                            pickReturnDatetimeLimitationSB.append("<br/>");
                        }
                    }
                    if (StringUtils.isBlank(pickReturnDatetimeLimitationSB.toString())) {
                        pickReturnDatetimeLimitationSB.append(QConfigUtil.getChineseOrDefault("PICK_RETURN_TIME_NO_LIMIT", ""));
                    }
                    supplierActivityDetail.setPickReturnDatetimeLimitation(pickReturnDatetimeLimitationSB.toString());
                }
                // 字段：节假日取还车限制字段
                Boolean templateAllowHoliday = supplierCondition.isAllowHoliday();
                StringBuilder holidayLimitationSB = new StringBuilder();
                if (templateAllowHoliday != null && templateAllowHoliday) {
                    holidayLimitationSB.append(QConfigUtil.getChineseOrDefault("ALLOW_HOLIDAY_RETURN", ""));
                } else {
                    holidayLimitationSB.append(QConfigUtil.getChineseOrDefault("NO_ALLOW_HOLIDAY_RETURN", ""));
                }
                supplierActivityDetail.setHolidayLimitation(holidayLimitationSB.toString());
                List<HolidayDto> holidayList = config.getHolidayList();
                if (CollectionUtils.isNotEmpty(holidayList)) {
                    supplierActivityDetail.setHolidayInfo(holidayList.stream().filter(l-> Objects.nonNull(l.getStart())&&Objects.nonNull(l.getEnd())).map(li->{
                        String start = CalendarFormatUtil.format_yyyy_MM_dd_HH_mm_ss(li.getStart());
                        String end = CalendarFormatUtil.format_yyyy_MM_dd_HH_mm_ss(li.getEnd());
                        return li.getHolidayName().concat(":").concat(start).concat("-").concat(end);
                    }).collect(Collectors.toList()));
                }
                // 字段：活动限制字段
                List<Integer> templateCityIds = supplierCondition.getCityIds() != null ? supplierCondition.getCityIds() : new ArrayList<>();
                List<Integer> templateVehicleGroupIds = supplierCondition.getVehicleGroupIds() != null ? supplierCondition.getVehicleGroupIds() : new ArrayList<>();
                if (CollectionUtils.isNotEmpty(templateCityIds) || CollectionUtils.isNotEmpty(templateVehicleGroupIds) || CollectionUtils.isNotEmpty(supplierCondition.getStoreType())
                        || CollectionUtils.isNotEmpty(supplierCondition.getStandardProductIds())
                ) {
                    StringBuilder activityLimitationSB = new StringBuilder();
                    //List<BasicCityDTO> cityInfoList = igtBasicServiceProxy.getCityInfoByCityIdList(templateCityIds.stream().map(Integer::longValue).collect(Collectors.toList()));
                    List<CityDto> cityInfoList = proxy.queryCarCity(templateCityIds.stream().map(Integer::longValue).collect(Collectors.toList()));
                    Map<Long, String> cMap = CollectionUtils.isNotEmpty(cityInfoList) ? cityInfoList.stream().collect(Collectors.toMap(CityDto::getId, CityDto::getName,(k1, k2)->k1)): Maps.newHashMap();
                    List<VehicleGroupDTO> allVehicleGroupDTOList = sdMarketingServiceProxy.queryAllVehicleGroup();
                    List<VehicleGroupDTO> vehicleGroupDTOList = allVehicleGroupDTOList.stream().filter(vehicleGroupDTO -> templateVehicleGroupIds.contains(vehicleGroupDTO.getCode())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(cityInfoList) || CollectionUtils.isNotEmpty(vehicleGroupDTOList)) {
                        // 城市限制说明
                        if (CollectionUtils.isNotEmpty(cityInfoList)) {
                            activityLimitationSB.append(QConfigUtil.getChineseOrDefault("CITY", "city"));
                            activityLimitationSB.append("（");
                            for (int i = 0; i < templateCityIds.size(); i++) {
                                String cityName = cMap.getOrDefault(templateCityIds.get(i).longValue(),"");
                                if (StringUtils.isNotBlank(cityName)) {
                                    activityLimitationSB.append(cityName);
                                    if (i != cityInfoList.size() - 1) {
                                        activityLimitationSB.append("、");
                                    }
                                }
                            }
                            supplierActivityDetail.setCityLimit(soaResponse.getCityLimit());
                            activityLimitationSB.append("）");
                            if (supplierCondition.isExcludeCity() != null && supplierCondition.isExcludeCity() && StringUtils.isNotEmpty(supplierActivityDetail.getCityLimit())) { //是否是排除城市
                                activityLimitationSB.append(QConfigUtil.getChineseOrDefault("EXCLUDE", "exclude"));
                                //supplierActivityDetail.setCityLimit(QConfigUtil.getChineseOrDefault("allCity", "") + "(" + QConfigUtil.getChineseOrDefault("excludeStr", "") + supplierActivityDetail.getCityLimit() + ")");
                            }
                            activityLimitationSB.append("；");
                        }
                        // 车型组限制说明
                        if (CollectionUtils.isNotEmpty(vehicleGroupDTOList)) {
                            List<String> nameList = Lists.newArrayList();
                            activityLimitationSB.append(QConfigUtil.getChineseOrDefault("VEHICLE_GROUP", "vehicle group"));
                            activityLimitationSB.append("（");
                            for (int i = 0; i < vehicleGroupDTOList.size(); i++) {
                                nameList.add(vehicleGroupDTOList.get(i).getName());
                                activityLimitationSB.append(vehicleGroupDTOList.get(i).getName());
                                if (i != vehicleGroupDTOList.size() - 1) {
                                    activityLimitationSB.append("、");
                                }
                            }
                            supplierActivityDetail.setVehicleLimit(String.join("、", nameList));
                            activityLimitationSB.append("）");
                            if (supplierCondition.isExcludeVehicleGroup() != null && supplierCondition.isExcludeVehicleGroup()) { //是否是排除车型组
                                activityLimitationSB.append(QConfigUtil.getChineseOrDefault("EXCLUDE", "exclude"));
                                supplierActivityDetail.setVehicleLimit(QConfigUtil.getChineseOrDefault("allProduct", "") + "(" + QConfigUtil.getChineseOrDefault("excludeStr", "") + supplierActivityDetail.getVehicleLimit() + ")");
                            }
                            activityLimitationSB.append("；");
                        }
                        //if (supplierCondition.isCitySkuLimit() == null || !supplierCondition.isCitySkuLimit()) {
                        //    activityLimitationSB.append(QConfigUtil.getChineseOrDefault("ALL_ELIGIBLE_SKU_PARTICIPATE", "All eligible sku need to participate."));
                        //}
                    }
                    // 门店类型限制说明
                    if (CollectionUtils.isNotEmpty(supplierCondition.getStoreType())) {
                        List<String> nameList = Lists.newArrayList();
                        for (Integer type : supplierCondition.getStoreType()) {
                            nameList.add(StoreType.findByType(type));
                        }
                        supplierActivityDetail.setStoreLimit(String.join("、", nameList));
                    }
                    supplierActivityDetail.setActivityLimitation(activityLimitationSB.toString());
                } else {
                    supplierActivityDetail.setActivityLimitation(QConfigUtil.getChineseOrDefault("NO_LIMIT", "no limit"));
                }

                StringBuilder unableAttendReason = new StringBuilder();
                //车型组限制校验
                boolean vehicleGroupCondition = true;
                //包含新能源时不校验
                if (CollectionUtils.isNotEmpty(templateVehicleGroupIds) && !templateVehicleGroupIds.contains(66)) {
                    Set<Integer> tempVehicleGroupSet = Sets.newHashSet(templateVehicleGroupIds);
                    //服务商可用车型组
                    List<Integer> vendorVehicleGroupIds = proxy.queryVendorVehicleGroup(bffRequest.getVendorId(), bffRequest.getBaseRequest().getUid(), bffRequest.getBaseRequest());
                    if (CollectionUtils.isEmpty(vendorVehicleGroupIds)) {
                        vehicleGroupCondition = false;
                    } else if (supplierCondition.isExcludeVehicleGroup()) { //排除车型组，完全排除
                        if (tempVehicleGroupSet.containsAll(vendorVehicleGroupIds)) {
                            vehicleGroupCondition = false;
                        }
                    } else if (vendorVehicleGroupIds.stream().noneMatch(tempVehicleGroupSet::contains)) {
                        vehicleGroupCondition = false;
                    }
                }
                //活动门店限制校验
                boolean storeCondition = true;
                //城市限制校验
                boolean cityCondition = true;
                if (CollectionUtils.isNotEmpty(supplierCondition.getStoreType()) && supplierCondition.getStoreType().contains(1)) {
                    List<StoreShortInfoDto> storeList = storeServiceProxy.getStoreInfo(bffRequest.getVendorId());
                    if (CollectionUtils.isEmpty(storeList)) {
                        storeCondition = false;
                    } else if (CollectionUtils.isNotEmpty(supplierCondition.getCityIds())) {
                        Set<Integer> citySet = Sets.newHashSet(supplierCondition.getCityIds());
                        if (Objects.equals(supplierCondition.isExcludeCity(), true)) {
                            if (storeList.stream().allMatch(l -> citySet.contains(l.getCityId()))) {
                                cityCondition = false;
                            }
                        } else if (storeList.stream().noneMatch(l -> citySet.contains(l.getCityId()))) {
                            cityCondition = false;
                        }
                    }
                }

                if (cityCondition && CollectionUtils.isNotEmpty(supplierCondition.getCityIds())) {
                    List<Integer> vendorCityList = storeServiceProxy.getAllVendorCity(bffRequest.getVendorId());
                    Set<Integer> citySet = Sets.newHashSet(supplierCondition.getCityIds());
                    if (Objects.equals(supplierCondition.isExcludeCity(), true)) {
                        if (citySet.containsAll(vendorCityList)) {
                            cityCondition = false;
                        }
                    } else if (vendorCityList.stream().noneMatch(citySet::contains)) {
                        cityCondition = false;
                    }
                }

                // 判断标准车型限制（只判断包含，不判断排除情况）
                boolean standardProductCondition = true;
                if (CollectionUtils.isNotEmpty(supplierCondition.getStandardProductIds()) && !Boolean.TRUE.equals(supplierCondition.isExcludeStandardProduct())) {
                    QueryStandardProductInfoRequestType requestType = new QueryStandardProductInfoRequestType();
                    requestType.setBaseRequest(bffRequest.getBaseRequest());
                    requestType.setUserId(Long.parseLong(bffRequest.getBaseRequest().getUid()));
                    requestType.setVendorId(bffRequest.getVendorId());
                    requestType.setStandardPIds(supplierCondition.getStandardProductIds());
                    QueryStandardProductInfoResponseType responseType = proxy.queryProductInfoList(requestType);
                    if (Objects.isNull(responseType) || CollectionUtils.isEmpty(responseType.getInfos())) {
                        standardProductCondition = false;
                    }
                }
                if (!storeCondition) {
                    unableAttendReason.append(QConfigUtil.getChineseOrDefault("STORE_TYPE_ERROR", ""));
                } else if (!cityCondition) {
                    unableAttendReason.append(QConfigUtil.getChineseOrDefault("CITY_ERROR", ""));
                } else if (!vehicleGroupCondition) {
                    unableAttendReason.append(QConfigUtil.getChineseOrDefault("VEHICLE_ERROR", ""));
                } else if (!standardProductCondition) {
                    // 不满足限制条件
                    unableAttendReason.append(QConfigUtil.getChineseOrDefault("STANDARD_PRODUCT_ERROR", "")); //chinese: 无符合活动条件的车型，请检查后再进行报名
                }
                supplierActivityDetail.setCanSignUp(cityCondition && vehicleGroupCondition && storeCondition && standardProductCondition);
                supplierActivityDetail.setUnableAttendReason(unableAttendReason.toString());

                //是否允许自定义节假日是否可用
                supplierActivityDetail.setAllowHolidayLimit(Objects.equals(supplierCondition.isAllowHolidayLimit(),true));
                //是否允许节假日取还
                supplierActivityDetail.setAllowHoliday(Objects.equals(supplierCondition.isAllowHoliday(),true));
                //自定义供应商参与条件
                if (Objects.nonNull(soaActInfo) && Objects.nonNull(soaActInfo.getCustomContent()) && Objects.nonNull(soaActInfo.getCustomContent().getSupplierCondition())
                        && Objects.nonNull(soaActInfo.getCustomContent().getSupplierCondition().isAllowHoliday())) {
                    supplierActivityDetail.setAllowHoliday(soaActInfo.getCustomContent().getSupplierCondition().isAllowHoliday());
                    if (soaActInfo.getCustomContent().getSupplierCondition().isAllowHoliday()) {
                        supplierActivityDetail.setHolidayLimitation(QConfigUtil.getChineseOrDefault("ALLOW_HOLIDAY_RETURN", ""));
                    } else {
                        supplierActivityDetail.setHolidayLimitation(QConfigUtil.getChineseOrDefault("NO_ALLOW_HOLIDAY_RETURN", ""));
                    }
                }

                // vendorCityLimit 字段为 true 代表商家不能自定义模板中。模板中的 cityLimit 相反，为 true 代表能自定义城市
                supplierActivityDetail.setVendorCityLimit(!supplierCondition.isCityLimit());
                supplierActivityDetail.setVendorProductLimit(!supplierCondition.isStandardPIdLimit());
                //阶梯-提前预订期满减、阶梯-提前预订期满折、取车日期 = 当天 场景下不展示
                Integer dType = deductionStrategy != null ? deductionStrategy.getDeductionType() : -1;
                if (Objects.equals(DeductionType.Advance_Discount.getType(), dType)
                        || Objects.equals(DeductionType.Advance_Amount.getType(), dType)
                        || Objects.equals(supplierCondition.getTakeTimeType(), 2)) {
                    supplierActivityDetail.setAdvanceTimeStr(null);
                } else {
                    supplierActivityDetail.setAdvanceTimeStr(getAdvanceTimeStr(supplierCondition.getAdvanceTime()));
                }
            }
        }

        // 活动信息
        if (soaActInfo != null) {
            ActivityVendorInfo activityVendorInfo = new ActivityVendorInfo();
            // 字段：已报名活动供应商信息
            if (soaActInfo.getVendorId() != null) {
                com.ctrip.car.commodity.vendor.service.contract.dto.VendorInfo reportedVendor = carCommodityVendorServiceProxy.queryVendorInfoByVendorIdWithCache(soaActInfo.getVendorId());
                if (reportedVendor != null) {
                    VendorInfo vendorInfo = new VendorInfo();
                    vendorInfo.setVendorId(reportedVendor.getVendorId());
                    vendorInfo.setVendorName(reportedVendor.getVendorName());
                    activityVendorInfo.setVendorInfo(vendorInfo);
                }
            }
            // 字段：活动状态。如果活动已报名就用 soaActInfo 里的状态
            activityVendorInfo.setAttendStatus(AttendStatusEnum.ATTENDED.getCode());
            activityVendorInfo.setActivityStatus(soaActInfo.getStatus());
            activityVendorInfo.setActivityStatusName(ActivityStatusEnum.valueOfCode(soaActInfo.getStatus()).getDesc());
            // 字段：已报名城市
            List<Integer> activityCityIds = soaActInfo.getCityIds();
            if (CollectionUtils.isNotEmpty(activityCityIds)) {
                //List<BasicCityDTO> cityDTOs = igtBasicServiceProxy.getCityInfoByCityIdList(activityCityIds.stream().map(Integer::longValue).collect(Collectors.toList()));
                List<CityDto> cityDTOs = proxy.queryCarCity(activityCityIds.stream().map(Integer::longValue).collect(Collectors.toList()));
                List<CityInfo> cityInfoList = new ArrayList<>();
                for (CityDto cityDTO : cityDTOs) {
                    if (cityDTO != null) {
                        CityInfo cityInfo = new CityInfo();
                        cityInfo.setCityId(cityDTO.getId());
                        cityInfo.setCityName(cityDTO.getName());
                        cityInfoList.add(cityInfo);
                    }
                }
                activityVendorInfo.setCityInfoList(cityInfoList);
            }
            // 字段：已报名车型
            List<Long> activityStandardPIds = soaActInfo.getStandardPIds();
            if (CollectionUtils.isNotEmpty(activityStandardPIds)) {
                QueryStandardProductInfoRequestType requestType = new QueryStandardProductInfoRequestType();
                requestType.setBaseRequest(bffRequest.getBaseRequest());
                requestType.setUserId(Long.parseLong(bffRequest.getBaseRequest().getUid()));
                requestType.setVendorId(bffRequest.getVendorId());
                requestType.setActTempId(bffRequest.getTemplateId());
                requestType.setStandardPIds(activityStandardPIds);
                QueryStandardProductInfoResponseType responseType = proxy.queryProductInfoList(requestType);
                if (responseType != null && CollectionUtils.isNotEmpty(responseType.getInfos())) {
                    List<ProductInfo> productInfoList = new ArrayList<>();
                    List<DataSourceInfo> products = responseType.getInfos();
                    for (DataSourceInfo product : products) {
                        if (product != null) {
                            ProductInfo productInfo = new ProductInfo();
                            productInfo.setProductId(Long.parseLong(product.getDataKey()));
                            productInfo.setProductName(product.getDisplayName());
                            productInfoList.add(productInfo);
                        }
                    }
                    activityVendorInfo.setProductInfoList(productInfoList);
                }
            }
            activityVendorInfo.setCheckCode(soaActInfo.getVendorCheckCode());
            activityVendorInfo.setSignUpMode(soaActInfo.getSignUpMode());
            supplierActivityDetail.setActivityVendorInfo(activityVendorInfo);
            supplierActivityDetail.setVendorCheckCodeList(soaActInfo.getVendorCheckCodeList());
        } else {
            // 字段：活动状态。如果未报名则活动状态展示的是模板状态
            ActivityVendorInfo activityVendorInfo = new ActivityVendorInfo();
            activityVendorInfo.setAttendStatus(AttendStatusEnum.NOT_ATTEND.getCode());
            activityVendorInfo.setActivityStatusName(TemplateStatusEnum.valueOfCode(soaTemplate.getStatus()).getDesc());
            supplierActivityDetail.setActivityVendorInfo(activityVendorInfo);
        }
        supplierActivityDetail.setPlatform(Objects.nonNull(actTempContent) && Objects.nonNull(actTempContent.getUserCondition()) ? actTempContent.getUserCondition().getPlatForm() : Lists.newArrayList());
        supplierActivityDetail.setProductType(soaTemplate.getProductType());
        bffResponse.setActivityDetail(supplierActivityDetail);
        //活动分组
        bffResponse.setGroupId(soaTemplate.getGroupId());
        //调价活动不支持手动报名，未报名状态下提醒供应商去开启跟价
        if (Objects.equals(ActivityGroup.Adjust.getId(), bffResponse.getGroupId())
                && Objects.equals(bffResponse.getActivityDetail().getActivityVendorInfo().getAttendStatus(), AttendStatusEnum.NOT_ATTEND.getCode())
                && !config.isSupportSignupAdjustActivity()) {
            bffResponse.getActivityDetail().setCanSignUp(false);
            bffResponse.getActivityDetail().setUnableAttendReason(QConfigUtil.getChineseOrDefault("AdjustActivitySignUpMessage",""));
        }
        return bffResponse;
    }

    public String getAdvanceTimeStr(LimitRangeDto limitRangeDto) {
        if (limitRangeDto == null) {
            return QConfigUtil.getChineseOrDefault("NO_LIMIT","");
        }
        if (limitRangeDto.getFloor() != null && limitRangeDto.getUpline() != null) {
            return String.format(QConfigUtil.getChineseOrDefault("advanceTime_limit",""), limitRangeDto.getFloor(), limitRangeDto.getUpline());
        }
        if (limitRangeDto.getFloor() != null) {
            return limitRangeDto.getFloor() + QConfigUtil.getChineseOrDefault("advanceTime_floor","");
        }
        if (limitRangeDto.getUpline() != null) {
            return limitRangeDto.getUpline() + QConfigUtil.getChineseOrDefault("advanceTime_upline","");
        }
        return QConfigUtil.getChineseOrDefault("NO_LIMIT","");
    }

    public ActDto copyActDto(ActDto oldActDto) {
        ActDto newActDto = new ActDto();
        newActDto.setActId(oldActDto.getActId());
        newActDto.setVendorId(oldActDto.getVendorId());
        newActDto.setActivityStartTime(oldActDto.getActivityStartTime());
        newActDto.setActivityEndTime(oldActDto.getActivityEndTime());
        newActDto.setCityIds(oldActDto.getCityIds());
        newActDto.setReturnCityIds(oldActDto.getReturnCityIds());
        newActDto.setStoreIds(oldActDto.getStoreIds());
        newActDto.setVehicleGroupIds(oldActDto.getVehicleGroupIds());
        newActDto.setSkuIds(oldActDto.getSkuIds());
        newActDto.setStatus(oldActDto.getStatus());
        newActDto.setVendorCheckCode(oldActDto.getVendorCheckCode());
        newActDto.setStandardPIds(oldActDto.getStandardPIds());
        newActDto.setCustomContent(oldActDto.getCustomContent());
        newActDto.setVendorCheckCodeList(oldActDto.getVendorCheckCodeList());
        newActDto.setSignUpMode(oldActDto.getSignUpMode());
        return newActDto;
    }

    @Override
    protected SupplierQueryActivityDetailResponseType validRequest(SupplierQueryActivityDetailRequestType request) {
        String errorMsg = "";
        if (request.getBaseRequest() == null) {
            errorMsg += "baseRequest is null; ";
        } else {
            if (request.getBaseRequest().getUid() == null) {
                errorMsg += "uid is null; ";
            }
        }
        if (request.getTemplateId() == null) {
            errorMsg += "templateId is null; ";
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            SupplierQueryActivityDetailResponseType response = new SupplierQueryActivityDetailResponseType();
            response.setBaseResponse(ResponseUtil.parameterError(errorMsg));
            return response;
        } else {
            return null;
        }
    }

}