package com.ctrip.car.businessim.bff.service;

public abstract class CarSoaService<TRequest, TResponse, CTRequest, CTResponse> {

    public TResponse toResponse(TRequest tRequest) {
        CTRequest ctRequest = this.convertRequest(tRequest);
        CTResponse ctResponse = this.executeSOA(ctRequest);
        return this.convertResponse(ctResponse, ctRequest, tRequest);
    }

    public abstract CTRequest convertRequest(TRequest var1);

    public abstract CTResponse executeSOA(CTRequest var1);

    public abstract TResponse convertResponse(CTResponse var1, CTRequest var2, TRequest var3);

}
