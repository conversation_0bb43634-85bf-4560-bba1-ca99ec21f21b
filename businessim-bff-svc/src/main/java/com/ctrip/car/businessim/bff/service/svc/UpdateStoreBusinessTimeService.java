package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.UpdateStoreBusinessTimeMapper;
import com.ctrip.car.businessim.bff.service.entity.UpdateStoreBusinessTimeRequestType;
import com.ctrip.car.businessim.bff.service.entity.UpdateStoreBusinessTimeResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/06
*/
@Service
public class UpdateStoreBusinessTimeService extends CarSoaService<UpdateStoreBusinessTimeRequestType, UpdateStoreBusinessTimeResponseType, com.ctrip.car.customer.businessim.contract.servicetype.UpdateStoreBusinessTimeRequestType, com.ctrip.car.customer.businessim.contract.servicetype.UpdateStoreBusinessTimeResponseType> {

    @Autowired
    private UpdateStoreBusinessTimeMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.UpdateStoreBusinessTimeRequestType convertRequest(UpdateStoreBusinessTimeRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.UpdateStoreBusinessTimeResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.UpdateStoreBusinessTimeRequestType request) {
        return proxy.updateStoreBusinessTime(request);
    }

    @Override
    public UpdateStoreBusinessTimeResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.UpdateStoreBusinessTimeResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.UpdateStoreBusinessTimeRequestType request, UpdateStoreBusinessTimeRequestType request2) {
        return mapper.to(response);
    }
}
