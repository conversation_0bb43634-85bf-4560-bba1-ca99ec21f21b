package com.ctrip.car.businessim.bff.service.svc.seo;

import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.seo.SaveSeoPageConfigMapper;
import com.ctrip.car.businessim.bff.service.dto.seo.SaveSeoPageConfigBffRequestType;
import com.ctrip.car.businessim.bff.service.dto.seo.SaveSeoPageConfigBffResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.seo.SaveSeoPageConfigRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.seo.SaveSeoPageConfigResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SaveSeoPageConfigService extends CarSoaService<SaveSeoPageConfigBffRequestType, SaveSeoPageConfigBffResponseType, SaveSeoPageConfigRequestType, SaveSeoPageConfigResponseType> {

    @Autowired
    private SaveSeoPageConfigMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public SaveSeoPageConfigRequestType convertRequest(SaveSeoPageConfigBffRequestType request) {
        return mapper.from(request);
    }

    @Override
    public SaveSeoPageConfigResponseType executeSOA(SaveSeoPageConfigRequestType request) {
        return proxy.saveSeoPageConfig(request);
    }

    @Override
    public SaveSeoPageConfigBffResponseType convertResponse(SaveSeoPageConfigResponseType response, SaveSeoPageConfigRequestType var2, SaveSeoPageConfigBffRequestType var3) {
        return mapper.to(response);
    }
}
