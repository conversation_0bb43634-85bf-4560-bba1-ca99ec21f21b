package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.UpdateAgentAllocateRuleMapper;
import com.ctrip.car.businessim.bff.service.entity.UpdateAgentAllocateRuleRequestType;
import com.ctrip.car.businessim.bff.service.entity.UpdateAgentAllocateRuleResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/12/09
*/
@Service
public class UpdateAgentAllocateRuleService extends CarSoaService<UpdateAgentAllocateRuleRequestType, UpdateAgentAllocateRuleResponseType, com.ctrip.car.customer.businessim.contract.servicetype.UpdateAgentAllocateRuleRequestType, com.ctrip.car.customer.businessim.contract.servicetype.UpdateAgentAllocateRuleResponseType> {

    @Autowired
    private UpdateAgentAllocateRuleMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.UpdateAgentAllocateRuleRequestType convertRequest(UpdateAgentAllocateRuleRequestType updateAgentAllocateRuleRequestType) {
        return mapper.from(updateAgentAllocateRuleRequestType);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.UpdateAgentAllocateRuleResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.UpdateAgentAllocateRuleRequestType updateAgentAllocateRuleRequestType) {
        return proxy.updateAgentAllocateRule(updateAgentAllocateRuleRequestType);
    }

    @Override
    public UpdateAgentAllocateRuleResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.UpdateAgentAllocateRuleResponseType updateAgentAllocateRuleResponseType, com.ctrip.car.customer.businessim.contract.servicetype.UpdateAgentAllocateRuleRequestType updateAgentAllocateRuleRequestType, UpdateAgentAllocateRuleRequestType updateAgentAllocateRuleRequestType2) {
        return mapper.to(updateAgentAllocateRuleResponseType);
    }
}
