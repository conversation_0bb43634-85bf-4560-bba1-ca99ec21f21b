package com.ctrip.car.businessim.bff.service.svc;

import com.ctrip.car.business.auth.Auth;
import com.ctrip.car.business.auth.enums.PlatformTypeEnum;
import com.ctrip.car.businessim.bff.service.CarSoaService;
import com.ctrip.car.businessim.bff.service.core.mapper.AddIMAgentMapper;
import com.ctrip.car.businessim.bff.service.entity.AddIMAgentRequestType;
import com.ctrip.car.businessim.bff.service.entity.AddIMAgentResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2022/08/06
*/
@Service
public class AddIMAgentService extends CarSoaService<AddIMAgentRequestType, AddIMAgentResponseType, com.ctrip.car.customer.businessim.contract.servicetype.AddIMAgentRequestType, com.ctrip.car.customer.businessim.contract.servicetype.AddIMAgentResponseType> {

    @Autowired
    private AddIMAgentMapper mapper;

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.AddIMAgentRequestType convertRequest(AddIMAgentRequestType request) {
        return mapper.from(request);
    }

    @Override
    public com.ctrip.car.customer.businessim.contract.servicetype.AddIMAgentResponseType executeSOA(com.ctrip.car.customer.businessim.contract.servicetype.AddIMAgentRequestType request) {
        return proxy.addIMAgent(request);
    }

    @Override
    public AddIMAgentResponseType convertResponse(com.ctrip.car.customer.businessim.contract.servicetype.AddIMAgentResponseType response, com.ctrip.car.customer.businessim.contract.servicetype.AddIMAgentRequestType request, AddIMAgentRequestType request2) {
        return mapper.to(response);
    }
}
