package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.core.enums.SignUpStatusEnum;
import com.ctrip.car.businessim.bff.service.core.enums.SortTypeEnum;
import com.ctrip.car.businessim.bff.service.core.util.QConfigUtil;
import com.ctrip.car.businessim.bff.service.core.util.ResponseUtil;
import com.ctrip.car.businessim.bff.service.dto.activity.SupplierActivityTemplateInfo;
import com.ctrip.car.businessim.bff.service.entity.SupplierQueryActivityTemplateListRequestType;
import com.ctrip.car.businessim.bff.service.entity.SupplierQueryActivityTemplateListResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.businessim.bff.service.proxy.soa.StoreServiceProxy;
import com.ctrip.car.commodity.store.dto.StoreShortInfoDto;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryActivityListRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryActivityListResponseType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStandardProductInfoRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStandardProductInfoResponseType;
import com.google.common.collect.Lists;
import com.google.triplog.shaded.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class SupplierQueryActivityTemplateListService extends ActivityBaseService<SupplierQueryActivityTemplateListRequestType, SupplierQueryActivityTemplateListResponseType> {

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Autowired
    private StoreServiceProxy storeServiceProxy;

    @Autowired
    private CustomerBusinessIMServiceProxy customerBusinessIMServiceProxy;

    @Override
    protected SupplierQueryActivityTemplateListResponseType business(SupplierQueryActivityTemplateListRequestType request) {
        SupplierQueryActivityTemplateListResponseType response = new SupplierQueryActivityTemplateListResponseType();
        if (request.getSortType() == null || request.getSortType() < 0) request.setSortType(0);
        if (request.getPageSize() == null || request.getPageSize() <= 0) request.setPageSize(10);
        if (request.getPageIndex() == null || request.getPageIndex() <= 0) request.setPageIndex(1);
        QueryActivityListRequestType queryActivityListRequest = this.buildSoaRequest(request);
        QueryActivityListResponseType queryActivityListResponse = proxy.queryActivityList(queryActivityListRequest);
        if (queryActivityListResponse != null && CollectionUtils.isNotEmpty(queryActivityListResponse.getActivityList())) {
            response = this.buildBffResponse(queryActivityListResponse, request);
        } else {
            response.setBaseResponse(ResponseUtil.success());
        }
        response.setPageSize(request.getPageSize());
        response.setPageIndex(request.getPageIndex());
        response.setSortTypeName(SortTypeEnum.valueOfCode(request.getSortType()).getDesc());
        return response;
    }

    private QueryActivityListRequestType buildSoaRequest(SupplierQueryActivityTemplateListRequestType request) {
        QueryActivityListRequestType soaRequest = new QueryActivityListRequestType();
        soaRequest.setBaseRequest(request.getBaseRequest());
        soaRequest.setActivityName(request.getActivityName());
        soaRequest.setLabelId(request.getLabelId());
        soaRequest.setLabelName(request.getLabelName());
        soaRequest.setStatus(request.getSignUpStatuses());
        soaRequest.setSortIndex(request.getSortType());
        soaRequest.setVendorId(request.getVendorId());
        if (NumberUtils.isDigits(request.getBaseRequest().getUid())) {
            soaRequest.setUserId(Long.parseLong(request.getBaseRequest().getUid()));
        }
        soaRequest.setPageNo(request.getPageIndex());
        soaRequest.setPageSize(request.getPageSize());
        soaRequest.setType(request.getType());
        soaRequest.setProductType(request.getProductType());
        return soaRequest;
    }

    public SupplierQueryActivityTemplateListResponseType buildBffResponse(QueryActivityListResponseType soaResponse, SupplierQueryActivityTemplateListRequestType bffRequest) {
        SupplierQueryActivityTemplateListResponseType bffResponse = new SupplierQueryActivityTemplateListResponseType();
        if (soaResponse == null) return bffResponse;
        bffResponse.setCount(soaResponse.getTotal());
        if (CollectionUtils.isNotEmpty(soaResponse.getActivityList())) {
            //服务商可用车型组
            List<Integer> vendorVehicleGroupIds = proxy.queryVendorVehicleGroup(bffRequest.getVendorId(), bffRequest.getBaseRequest().getUid(), bffRequest.getBaseRequest());
            //服务商优选门店
            List<StoreShortInfoDto> storeList = soaResponse.getActivityList().stream().anyMatch(l->CollectionUtils.isNotEmpty(l.getStoreType())) ?
                    storeServiceProxy.getStoreInfo(bffRequest.getVendorId()) : Lists.newArrayList();
            //服务商全量城市
            List<Integer> vendorCityList = soaResponse.getActivityList().stream().anyMatch(l->CollectionUtils.isNotEmpty(l.getCityids())) ?
                    storeServiceProxy.getAllVendorCity(bffRequest.getVendorId()) : Lists.newArrayList();
            List<SupplierActivityTemplateInfo> list = soaResponse.getActivityList().stream()
                    .map(activityListItem -> {
                        SupplierActivityTemplateInfo templateInfo = new SupplierActivityTemplateInfo();
                        templateInfo.setActivityId(activityListItem.getActivityId());
                        templateInfo.setTemplateId(activityListItem.getTemplateId());
                        templateInfo.setSignUpStatus(activityListItem.getStatus());
                        templateInfo.setSignUpStatusName(SignUpStatusEnum.valueOfCode(activityListItem.getStatus()).getDesc());
                        templateInfo.setLabelName(activityListItem.getLabelName());
                        templateInfo.setActivityName(activityListItem.getName());
                        templateInfo.setSignUpDatetime(activityListItem.getRegisterDateStr());
                        templateInfo.setInformation(activityListItem.getActivityDateStr() + "; " + activityListItem.getRemark());
                        templateInfo.setLimitation(activityListItem.getActivityLimit());
                        templateInfo.setActivityDate(activityListItem.getActivityDateStr());
                        templateInfo.setRemark(activityListItem.getRemark());
                        templateInfo.setCityLimit(activityListItem.getCityLimit());
                        templateInfo.setStoreLimit(activityListItem.getStoreLimit());
                        templateInfo.setVehicleLimit(activityListItem.getVehicleLimit());
                        // 标准车型限制说明
                        templateInfo.setStandardProductLimit(activityListItem.getStandardProductLimit());
                        templateInfo.setProductType(activityListItem.getProductType());
                        StringBuilder unableAttendReason = new StringBuilder();
                        //车型组限制校验
                        boolean vehicleGroupCondition = true;
                        //包含新能源时不校验
                        if (CollectionUtils.isNotEmpty(activityListItem.getVehicleGroupIds()) && !activityListItem.getVehicleGroupIds().contains(66)) {
                            Set<Integer> tempVehicleGroupSet = Sets.newHashSet(activityListItem.getVehicleGroupIds());
                            if (CollectionUtils.isEmpty(vendorVehicleGroupIds)) {
                                vehicleGroupCondition = false;
                            } else if (activityListItem.isExcludeVehicleGroup()) { //排除车型组，完全排除
                                if (tempVehicleGroupSet.containsAll(vendorVehicleGroupIds)) {
                                    vehicleGroupCondition = false;
                                }
                            } else if (vendorVehicleGroupIds.stream().noneMatch(tempVehicleGroupSet::contains)) {
                                vehicleGroupCondition = false;
                            }
                        }
                        //活动门店限制校验
                        boolean storeCondition = true;
                        //城市限制校验
                        boolean cityCondition = true;
                        if (CollectionUtils.isNotEmpty(activityListItem.getStoreType()) && activityListItem.getStoreType().contains(1)) {
                            if (CollectionUtils.isEmpty(storeList)) {
                                storeCondition = false;
                            } else if (CollectionUtils.isNotEmpty(activityListItem.getCityids())) {
                                Set<Integer> citySet = Sets.newHashSet(activityListItem.getCityids());
                                if (Objects.equals(activityListItem.isExcludeCity(), true)) {
                                    if (storeList.stream().allMatch(l -> citySet.contains(l.getCityId()))) {
                                        cityCondition = false;
                                    }
                                } else if (storeList.stream().noneMatch(l -> citySet.contains(l.getCityId()))) {
                                    cityCondition = false;
                                }
                            }
                        }

                        if (cityCondition && CollectionUtils.isNotEmpty(activityListItem.getCityids())) {
                            Set<Integer> citySet = Sets.newHashSet(activityListItem.getCityids());
                            if (Objects.equals(activityListItem.isExcludeCity(), true)) {
                                if (citySet.containsAll(vendorCityList)) {
                                    cityCondition = false;
                                }
                            } else if (vendorCityList.stream().noneMatch(citySet::contains)) {
                                cityCondition = false;
                            }
                        }

                        // 判断标准车型限制（只判断包含，不判断排除情况）
                        boolean standardProductCondition = true;
                        if (CollectionUtils.isNotEmpty(activityListItem.getStandardProductIds()) && !Boolean.TRUE.equals(activityListItem.isExcludeStandardProduct())) {
                            QueryStandardProductInfoRequestType requestType = new QueryStandardProductInfoRequestType();
                            requestType.setBaseRequest(bffRequest.getBaseRequest());
                            requestType.setUserId(Long.parseLong(bffRequest.getBaseRequest().getUid()));
                            requestType.setVendorId(bffRequest.getVendorId());
                            requestType.setStandardPIds(activityListItem.getStandardProductIds());
                            QueryStandardProductInfoResponseType responseType = proxy.queryProductInfoList(requestType);
                            if (Objects.isNull(responseType) || CollectionUtils.isEmpty(responseType.getInfos())) {
                                standardProductCondition = false;
                            }
                        }
                        if (!storeCondition) {
                            unableAttendReason.append(QConfigUtil.getChineseOrDefault("STORE_TYPE_ERROR", ""));
                        } else if (!cityCondition) {
                            unableAttendReason.append(QConfigUtil.getChineseOrDefault("CITY_ERROR", ""));
                        } else if (!vehicleGroupCondition) {
                            unableAttendReason.append(QConfigUtil.getChineseOrDefault("VEHICLE_ERROR", ""));
                        } else if (!standardProductCondition) {
                            // 不满足限制条件
                            unableAttendReason.append(QConfigUtil.getChineseOrDefault("STANDARD_PRODUCT_ERROR", "")); //chinese: 无符合活动条件的车型，请检查后再进行报名
                        }
                        templateInfo.setCanSignUp(storeCondition && cityCondition && vehicleGroupCondition && standardProductCondition);
                        templateInfo.setUnableAttendReason(unableAttendReason.toString());

                        // 已报名数量
                        templateInfo.setSignUpCount(activityListItem.getRegisterCount());
                        templateInfo.setType(activityListItem.getType());
                        templateInfo.setPlatform(activityListItem.getPlatform());
                        return templateInfo;
                    })
                    .collect(Collectors.toList());
            bffResponse.setTemplateInfoList(list);
        } else {
            bffResponse.setTemplateInfoList(new ArrayList<>());
        }
        bffResponse.setBaseResponse(soaResponse.getBaseResponse());
        return bffResponse;
    }

    @Override
    protected SupplierQueryActivityTemplateListResponseType validRequest(SupplierQueryActivityTemplateListRequestType request) {
        String errorMsg = "";
        if (request.getBaseRequest() == null) {
            errorMsg += "baseRequest is null; ";
        } else {
            if (request.getBaseRequest().getUid() == null) {
                errorMsg += "uid is null; ";
            }
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            SupplierQueryActivityTemplateListResponseType response = new SupplierQueryActivityTemplateListResponseType();
            response.setBaseResponse(ResponseUtil.parameterError(errorMsg));
            return response;
        } else {
            return null;
        }
    }

}