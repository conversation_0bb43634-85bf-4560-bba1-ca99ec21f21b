package com.ctrip.car.businessim.bff.service.svc.activity;

import com.ctrip.car.businessim.bff.service.core.util.ResponseUtil;
import com.ctrip.car.businessim.bff.service.dto.activity.ProductInfo;
import com.ctrip.car.businessim.bff.service.entity.SupplierQueryProductInfoRequestType;
import com.ctrip.car.businessim.bff.service.entity.SupplierQueryProductInfoResponseType;
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStandardProductInfoRequestType;
import com.ctrip.car.customer.businessim.contract.servicetype.activity.QueryStandardProductInfoResponseType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.stream.Collectors;

@Service
public class SupplierQueryProductInfoService extends ActivityBaseService<SupplierQueryProductInfoRequestType, SupplierQueryProductInfoResponseType> {

    @Autowired
    private CustomerBusinessIMServiceProxy proxy;

    @Override
    protected SupplierQueryProductInfoResponseType business(SupplierQueryProductInfoRequestType request) {
        if (StringUtils.isBlank(request.getKeyWords())) {
            SupplierQueryProductInfoResponseType response = new SupplierQueryProductInfoResponseType();
            response.setBaseResponse(ResponseUtil.success());
            return response;
        }

        QueryStandardProductInfoRequestType queryStandardProductInfoRequestType = this.buildSoaRequest(request);
        QueryStandardProductInfoResponseType soaResponse = proxy.queryProductInfoList(queryStandardProductInfoRequestType);
        if (soaResponse != null) {
            return this.buildBffResponse(soaResponse);
        } else {
            SupplierQueryProductInfoResponseType response = new SupplierQueryProductInfoResponseType();
            response.setBaseResponse(ResponseUtil.thirdServiceError("an error occurs when invoking third-service, fail to quit activity."));
            return response;
        }
    }

    private QueryStandardProductInfoRequestType buildSoaRequest(SupplierQueryProductInfoRequestType request) {
        QueryStandardProductInfoRequestType soaRequest = new QueryStandardProductInfoRequestType();
        soaRequest.setBaseRequest(request.getBaseRequest());
        if (NumberUtils.isDigits(request.getBaseRequest().getUid())) {
            soaRequest.setUserId(Long.parseLong(request.getBaseRequest().getUid()));
        }
        soaRequest.setCityIds(request.getCityIds());
        soaRequest.setVendorId(request.getVendorId());
        soaRequest.setKeyWords(request.getKeyWords());
        soaRequest.setActTempId(request.getTemplateId());
        return soaRequest;
    }

    private SupplierQueryProductInfoResponseType buildBffResponse(QueryStandardProductInfoResponseType soaResponse) {
        SupplierQueryProductInfoResponseType bffResponse = new SupplierQueryProductInfoResponseType();
        if (CollectionUtils.isNotEmpty(soaResponse.getInfos())) {
            bffResponse.setProductInfoList(
                    soaResponse.getInfos().stream()
                            .map(dataSourceInfo -> {
                                ProductInfo productInfo = new ProductInfo();
                                productInfo.setProductId(Long.parseLong(dataSourceInfo.getDataKey()));
                                productInfo.setProductName(dataSourceInfo.getDisplayName());
                                return productInfo;
                            })
                            .collect(Collectors.toList()));
        } else {
            bffResponse.setProductInfoList(new ArrayList<>());
        }
        bffResponse.setBaseResponse(soaResponse.getBaseResponse());
        return bffResponse;
    }

    @Override
    protected SupplierQueryProductInfoResponseType validRequest(SupplierQueryProductInfoRequestType request) {
        String errorMsg = "";
        if (request.getBaseRequest() == null) {
            errorMsg += "baseRequest is null; ";
        } else {
            if (request.getBaseRequest().getUid() == null) {
                errorMsg += "uid is null; ";
            }
        }
        if (request.getTemplateId() == null || request.getTemplateId() < 0) {
            errorMsg += "templateId is null; ";
        }
        if (request.getVendorId() == null || request.getVendorId() < 0) {
            errorMsg += "vendorId is null; ";
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            SupplierQueryProductInfoResponseType response = new SupplierQueryProductInfoResponseType();
            response.setBaseResponse(ResponseUtil.parameterError(errorMsg));
            return response;
        } else {
            return null;
        }
    }

}
